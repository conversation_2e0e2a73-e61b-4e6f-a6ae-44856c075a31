{"name": "awwwards-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true npm run build", "build:production": "node scripts/build-and-analyze.js", "export": "next build && next export"}, "dependencies": {"autoprefixer": "^10.4.21", "clsx": "^2.0.0", "framer-motion": "^11.0.0", "gsap": "^3.12.0", "lenis": "^1.0.0", "next": "15.3.5", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/gsap": "^3.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.3.5", "postcss": "^8", "tailwindcss": "^3.4.0", "typescript": "^5"}}