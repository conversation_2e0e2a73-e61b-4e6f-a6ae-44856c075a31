'use client'

import { useEffect, useState } from 'react'
import Head from 'next/head'
import { fetchMetaTags, fetchAnalyticsSettings, type MetaTags, type AnalyticsSettings } from '@/lib/api'

interface MetaHeadProps {
  title?: string
  description?: string
  image?: string
  url?: string
}

export default function MetaHead({ title, description, image, url }: MetaHeadProps) {
  const [metaTags, setMetaTags] = useState<MetaTags>({})
  const [analytics, setAnalytics] = useState<AnalyticsSettings>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadMetaData = async () => {
      try {
        const [metaData, analyticsData] = await Promise.all([
          fetchMetaTags(),
          fetchAnalyticsSettings()
        ])
        setMetaTags(metaData)
        setAnalytics(analyticsData)
      } catch (error) {
        console.error('Failed to load meta data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadMetaData()
  }, [])

  if (loading) {
    return null
  }

  // Use props or fallback to API data
  const pageTitle = title || metaTags.site_name || 'Portfolio'
  const pageDescription = description || metaTags.site_description || 'Professional portfolio website'
  const pageImage = image || metaTags.og_image
  const pageUrl = url || metaTags.canonical_url || (typeof window !== 'undefined' ? window.location.href : '')

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      {metaTags.site_keywords && <meta name="keywords" content={metaTags.site_keywords} />}
      {metaTags.meta_author && <meta name="author" content={metaTags.meta_author} />}
      {metaTags.meta_robots && <meta name="robots" content={metaTags.meta_robots} />}
      {metaTags.meta_viewport && <meta name="viewport" content={metaTags.meta_viewport} />}

      {/* Open Graph Tags */}
      <meta property="og:title" content={metaTags.og_title || pageTitle} />
      <meta property="og:description" content={metaTags.og_description || pageDescription} />
      <meta property="og:type" content="website" />
      {pageUrl && <meta property="og:url" content={pageUrl} />}
      {pageImage && <meta property="og:image" content={pageImage} />}
      {pageImage && <meta property="og:image:width" content="1200" />}
      {pageImage && <meta property="og:image:height" content="630" />}

      {/* Twitter Card Tags */}
      <meta name="twitter:card" content={metaTags.twitter_card || 'summary_large_image'} />
      {metaTags.twitter_site && <meta name="twitter:site" content={metaTags.twitter_site} />}
      <meta name="twitter:title" content={metaTags.og_title || pageTitle} />
      <meta name="twitter:description" content={metaTags.og_description || pageDescription} />
      {pageImage && <meta name="twitter:image" content={pageImage} />}

      {/* Canonical URL */}
      {pageUrl && <link rel="canonical" href={pageUrl} />}

      {/* Structured Data */}
      {metaTags.structured_data && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: typeof metaTags.structured_data === 'string' 
              ? metaTags.structured_data 
              : JSON.stringify(metaTags.structured_data)
          }}
        />
      )}

      {/* Google Analytics */}
      {analytics.google_analytics_id && (
        <>
          <script async src={`https://www.googletagmanager.com/gtag/js?id=${analytics.google_analytics_id}`} />
          <script
            dangerouslySetInnerHTML={{
              __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${analytics.google_analytics_id}');
              `
            }}
          />
        </>
      )}

      {/* Google Tag Manager */}
      {analytics.google_tag_manager_id && (
        <>
          <script
            dangerouslySetInnerHTML={{
              __html: `
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${analytics.google_tag_manager_id}');
              `
            }}
          />
        </>
      )}

      {/* Facebook Pixel */}
      {analytics.facebook_pixel_id && (
        <script
          dangerouslySetInnerHTML={{
            __html: `
              !function(f,b,e,v,n,t,s)
              {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
              n.callMethod.apply(n,arguments):n.queue.push(arguments)};
              if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
              n.queue=[];t=b.createElement(e);t.async=!0;
              t.src=v;s=b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t,s)}(window, document,'script',
              'https://connect.facebook.net/en_US/fbevents.js');
              fbq('init', '${analytics.facebook_pixel_id}');
              fbq('track', 'PageView');
            `
          }}
        />
      )}

      {/* Hotjar */}
      {analytics.hotjar_id && (
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(h,o,t,j,a,r){
                h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                h._hjSettings={hjid:${analytics.hotjar_id},hjsv:6};
                a=o.getElementsByTagName('head')[0];
                r=o.createElement('script');r.async=1;
                r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
                a.appendChild(r);
              })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
            `
          }}
        />
      )}
    </Head>
  )
}
