'use client'

import { useEffect, useState } from 'react'
import { fetchMetaTags, fetchAnalyticsSettings, type MetaTags, type AnalyticsSettings } from '@/lib/api'

interface MetaHeadProps {
  title?: string
  description?: string
  image?: string
  url?: string
}

export default function MetaHead({ title, description, image, url }: MetaHeadProps) {
  const [metaTags, setMetaTags] = useState<MetaTags>({})
  const [analytics, setAnalytics] = useState<AnalyticsSettings>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadMetaData = async () => {
      try {
        const [metaData, analyticsData] = await Promise.all([
          fetchMetaTags(),
          fetchAnalyticsSettings()
        ])
        setMetaTags(metaData)
        setAnalytics(analyticsData)
      } catch (error) {
        console.error('Failed to load meta data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadMetaData()
  }, [])

  useEffect(() => {
    if (loading) return

    // Use props or fallback to API data
    const pageTitle = title || metaTags.site_name || 'Portfolio'
    const pageDescription = description || metaTags.site_description || 'Professional portfolio website'
    const pageImage = image || metaTags.og_image
    const pageUrl = url || metaTags.canonical_url || (typeof window !== 'undefined' ? window.location.href : '')

    // Update document title
    if (pageTitle) {
      document.title = pageTitle
    }

    // Update or create meta tags
    const updateMetaTag = (name: string, content: string, property?: string) => {
      if (!content) return

      const selector = property ? `meta[property="${property}"]` : `meta[name="${name}"]`
      let meta = document.querySelector(selector) as HTMLMetaElement

      if (!meta) {
        meta = document.createElement('meta')
        if (property) {
          meta.setAttribute('property', property)
        } else {
          meta.setAttribute('name', name)
        }
        document.head.appendChild(meta)
      }

      meta.setAttribute('content', content)
    }

    // Basic meta tags
    updateMetaTag('description', pageDescription)
    if (metaTags.site_keywords) updateMetaTag('keywords', metaTags.site_keywords)
    if (metaTags.meta_author) updateMetaTag('author', metaTags.meta_author)
    if (metaTags.meta_robots) updateMetaTag('robots', metaTags.meta_robots)

    // Open Graph tags
    updateMetaTag('', metaTags.og_title || pageTitle, 'og:title')
    updateMetaTag('', metaTags.og_description || pageDescription, 'og:description')
    updateMetaTag('', 'website', 'og:type')
    if (pageUrl) updateMetaTag('', pageUrl, 'og:url')
    if (pageImage) updateMetaTag('', pageImage, 'og:image')

    // Twitter Card tags
    updateMetaTag('twitter:card', metaTags.twitter_card || 'summary_large_image')
    if (metaTags.twitter_site) updateMetaTag('twitter:site', metaTags.twitter_site)
    updateMetaTag('twitter:title', metaTags.og_title || pageTitle)
    updateMetaTag('twitter:description', metaTags.og_description || pageDescription)
    if (pageImage) updateMetaTag('twitter:image', pageImage)

    // Canonical URL
    if (pageUrl) {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
      if (!canonical) {
        canonical = document.createElement('link')
        canonical.setAttribute('rel', 'canonical')
        document.head.appendChild(canonical)
      }
      canonical.setAttribute('href', pageUrl)
    }

    // Structured Data
    if (metaTags.structured_data) {
      let script = document.querySelector('script[type="application/ld+json"]') as HTMLScriptElement
      if (!script) {
        script = document.createElement('script')
        script.setAttribute('type', 'application/ld+json')
        document.head.appendChild(script)
      }
      script.textContent = typeof metaTags.structured_data === 'string'
        ? metaTags.structured_data
        : JSON.stringify(metaTags.structured_data)
    }

  }, [loading, metaTags, analytics, title, description, image, url])

  useEffect(() => {
    if (loading || !analytics) return

    // Google Analytics
    if (analytics.google_analytics_id && !document.querySelector(`script[src*="${analytics.google_analytics_id}"]`)) {
      const script1 = document.createElement('script')
      script1.async = true
      script1.src = `https://www.googletagmanager.com/gtag/js?id=${analytics.google_analytics_id}`
      document.head.appendChild(script1)

      const script2 = document.createElement('script')
      script2.textContent = `
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '${analytics.google_analytics_id}');
      `
      document.head.appendChild(script2)
    }

    // Google Tag Manager
    if (analytics.google_tag_manager_id && !document.querySelector(`script[src*="${analytics.google_tag_manager_id}"]`)) {
      const script = document.createElement('script')
      script.textContent = `
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','${analytics.google_tag_manager_id}');
      `
      document.head.appendChild(script)
    }

    // Facebook Pixel
    if (analytics.facebook_pixel_id && !window.fbq) {
      const script = document.createElement('script')
      script.textContent = `
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '${analytics.facebook_pixel_id}');
        fbq('track', 'PageView');
      `
      document.head.appendChild(script)
    }

    // Hotjar
    if (analytics.hotjar_id && !window.hj) {
      const script = document.createElement('script')
      script.textContent = `
        (function(h,o,t,j,a,r){
          h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
          h._hjSettings={hjid:${analytics.hotjar_id},hjsv:6};
          a=o.getElementsByTagName('head')[0];
          r=o.createElement('script');r.async=1;
          r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
          a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
      `
      document.head.appendChild(script)
    }

  }, [loading, analytics])

  return null // This component doesn't render anything visible
}
