'use client'

import { useEffect, useState } from 'react'
import { fetchAnalyticsSettings, type AnalyticsSettings } from '@/lib/api'

export default function GTMNoScript() {
  const [analytics, setAnalytics] = useState<AnalyticsSettings>({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadAnalytics = async () => {
      try {
        const analyticsData = await fetchAnalyticsSettings()
        setAnalytics(analyticsData)
      } catch (error) {
        console.error('Failed to load analytics settings:', error)
      } finally {
        setLoading(false)
      }
    }

    loadAnalytics()
  }, [])

  if (loading || !analytics.google_tag_manager_id) {
    return null
  }

  return (
    <noscript>
      <iframe
        src={`https://www.googletagmanager.com/ns.html?id=${analytics.google_tag_manager_id}`}
        height="0"
        width="0"
        style={{ display: 'none', visibility: 'hidden' }}
      />
    </noscript>
  )
}
