'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const Footer = () => {
  const footerRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const footer = footerRef.current
    if (!footer) return

    gsap.fromTo(
      footer.querySelectorAll('.footer-item'),
      { opacity: 0, y: 30 },
      {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: footer,
          start: 'top 90%',
          toggleActions: 'play none none reverse',
        },
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <footer ref={footerRef} className="py-12 px-6 bg-primary-black text-primary-neutral">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          {/* Brand */}
          <div className="footer-item">
            <h3 className="font-clash text-2xl font-bold mb-4">Your Name</h3>
            <p className="text-primary-neutral/70 text-sm leading-relaxed">
              Creating award-winning digital experiences with passion and precision.
            </p>
          </div>

          {/* Quick Links */}
          <div className="footer-item">
            <h4 className="font-satoshi text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2 text-sm">
              <li><a href="#projects" className="text-primary-neutral/70 hover:text-primary-peach transition-colors">Projects</a></li>
              <li><a href="#skills" className="text-primary-neutral/70 hover:text-primary-peach transition-colors">Skills</a></li>
              <li><a href="#about" className="text-primary-neutral/70 hover:text-primary-peach transition-colors">About</a></li>
              <li><a href="#contact" className="text-primary-neutral/70 hover:text-primary-peach transition-colors">Contact</a></li>
            </ul>
          </div>

          {/* Social Links */}
          <div className="footer-item">
            <h4 className="font-satoshi text-lg font-semibold mb-4">Connect</h4>
            <div className="flex space-x-4">
              {['GitHub', 'LinkedIn', 'Twitter', 'Dribbble'].map((social) => (
                <a
                  key={social}
                  href="#"
                  className="w-10 h-10 bg-primary-neutral/10 rounded-full flex items-center justify-center text-xs font-medium hover:bg-primary-peach hover:text-primary-black transition-colors duration-300"
                  data-cursor="pointer"
                >
                  {social.slice(0, 2)}
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="footer-item border-t border-primary-neutral/20 pt-8 flex flex-col md:flex-row justify-between items-center text-sm text-primary-neutral/70">
          <p>&copy; 2024 Your Name. All rights reserved.</p>
          <p className="mt-2 md:mt-0">Designed & Developed with ❤️</p>
        </div>
      </div>
    </footer>
  )
}

export default Footer
