'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Container from '@/components/ui/Container'
import { useScrollAnimations } from '@/hooks/useScrollAnimations'

gsap.registerPlugin(ScrollTrigger)

const Footer = () => {
  const footerRef = useRef<HTMLElement>(null)
  const { staggerAnimation } = useScrollAnimations()

  useEffect(() => {
    const footer = footerRef.current
    if (!footer) return

    // Animate footer items
    staggerAnimation('.footer-item', 'fadeIn', {
      trigger: footer,
      start: 'top 90%',
      stagger: 0.1,
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [staggerAnimation])

  return (
    <footer ref={footerRef} className="py-16 bg-primary-black text-primary-neutral relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,#fecf8b_1px,transparent_1px)] bg-[length:30px_30px]" />
      </div>

      <Container size="xl" className="relative z-10">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Brand */}
          <div className="footer-item lg:col-span-2">
            <Typography
              variant="h3"
              font="clash"
              weight="bold"
              className="mb-4 text-primary-peach"
            >
              Your Name
            </Typography>
            <Typography
              variant="body"
              className="text-primary-neutral/70 mb-6 leading-relaxed max-w-md"
            >
              Creating award-winning digital experiences with passion and precision.
              Let's build something amazing together.
            </Typography>

            {/* Newsletter */}
            <div className="flex flex-col sm:flex-row gap-3">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 bg-primary-neutral/10 border border-primary-neutral/20 rounded-lg text-primary-neutral placeholder-primary-neutral/50 focus:outline-none focus:border-primary-peach transition-colors"
              />
              <motion.button
                className="px-6 py-3 bg-primary-peach text-primary-black rounded-lg font-medium hover:bg-primary-peach/90 transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                data-cursor="pointer"
              >
                Subscribe
              </motion.button>
            </div>
          </div>

          {/* Quick Links */}
          <div className="footer-item">
            <Typography
              variant="h6"
              font="satoshi"
              weight="semibold"
              className="mb-6"
            >
              Quick Links
            </Typography>
            <ul className="space-y-3">
              {[
                { name: 'Home', href: '#home' },
                { name: 'Projects', href: '#projects' },
                { name: 'Skills', href: '#skills' },
                { name: 'About', href: '#about' },
                { name: 'Contact', href: '#contact' },
              ].map((link) => (
                <li key={link.name}>
                  <motion.a
                    href={link.href}
                    className="text-primary-neutral/70 hover:text-primary-peach transition-colors duration-300 text-sm"
                    whileHover={{ x: 4 }}
                    data-cursor="pointer"
                  >
                    {link.name}
                  </motion.a>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div className="footer-item">
            <Typography
              variant="h6"
              font="satoshi"
              weight="semibold"
              className="mb-6"
            >
              Services
            </Typography>
            <ul className="space-y-3">
              {[
                'Web Development',
                'UI/UX Design',
                'Mobile Apps',
                'Consulting',
                'Maintenance',
              ].map((service) => (
                <li key={service}>
                  <span className="text-primary-neutral/70 text-sm">
                    {service}
                  </span>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Social Links */}
        <div className="footer-item border-t border-primary-neutral/20 pt-8 mb-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-6">
            <Typography
              variant="body"
              className="text-primary-neutral/70"
            >
              Follow me on social media
            </Typography>

            <div className="flex space-x-4">
              {[
                { name: 'GitHub', icon: 'GH', url: '#' },
                { name: 'LinkedIn', icon: 'LI', url: '#' },
                { name: 'Twitter', icon: 'TW', url: '#' },
                { name: 'Dribbble', icon: 'DR', url: '#' },
                { name: 'Instagram', icon: 'IG', url: '#' },
              ].map((social) => (
                <motion.a
                  key={social.name}
                  href={social.url}
                  className="w-12 h-12 bg-primary-neutral/10 rounded-full flex items-center justify-center text-xs font-bold hover:bg-primary-peach hover:text-primary-black transition-colors duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  data-cursor="pointer"
                  title={social.name}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="footer-item border-t border-primary-neutral/20 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 text-sm text-primary-neutral/70">
            <div className="flex flex-col md:flex-row items-center gap-4">
              <p>&copy; 2024 Your Name. All rights reserved.</p>
              <div className="flex gap-4">
                <a href="#" className="hover:text-primary-peach transition-colors">Privacy Policy</a>
                <a href="#" className="hover:text-primary-peach transition-colors">Terms of Service</a>
              </div>
            </div>
            <p>Designed & Developed with ❤️ using Next.js</p>
          </div>
        </div>

        {/* Back to top button */}
        <motion.button
          className="fixed bottom-8 right-8 w-12 h-12 bg-primary-peach text-primary-black rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow z-50"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          data-cursor="pointer"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
        >
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M18 15l-6-6-6 6" />
          </svg>
        </motion.button>
      </Container>
    </footer>
  )
}

export default Footer
