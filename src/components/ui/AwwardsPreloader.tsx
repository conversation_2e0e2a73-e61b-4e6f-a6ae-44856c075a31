'use client'

import { useEffect, useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { gsap } from 'gsap'
import Typography from './Typography'

interface AwwardsPreloaderProps {
  onComplete?: () => void
  duration?: number
}

const AwwardsPreloader = ({ onComplete, duration = 3000 }: AwwardsPreloaderProps) => {
  const [progress, setProgress] = useState(0)
  const [currentWord, setCurrentWord] = useState(0)
  const [isComplete, setIsComplete] = useState(false)
  const [showContent, setShowContent] = useState(false)
  
  const containerRef = useRef<HTMLDivElement>(null)
  const progressBarRef = useRef<HTMLDivElement>(null)
  const counterRef = useRef<HTMLDivElement>(null)
  const logoRef = useRef<HTMLDivElement>(null)

  const loadingWords = [
    'LOADING',
    'CREATING',
    'CRAFTING',
    'BUILDING',
    'DESIGNING',
    'ANIMATING',
    'READY'
  ]

  useEffect(() => {
    setShowContent(true)
    
    // Animate progress
    const progressInterval = setInterval(() => {
      setProgress(prev => {
        const increment = Math.random() * 8 + 2
        const newProgress = Math.min(prev + increment, 100)
        
        // Update current word based on progress
        const wordIndex = Math.floor((newProgress / 100) * (loadingWords.length - 1))
        setCurrentWord(wordIndex)
        
        if (newProgress >= 100) {
          clearInterval(progressInterval)
          setTimeout(() => {
            setIsComplete(true)
            setTimeout(() => {
              onComplete?.()
            }, 1000)
          }, 500)
        }
        
        return newProgress
      })
    }, 50)

    return () => clearInterval(progressInterval)
  }, [onComplete, loadingWords.length])

  // GSAP animations
  useEffect(() => {
    if (!showContent) return

    const container = containerRef.current
    const progressBar = progressBarRef.current
    const counter = counterRef.current
    const logo = logoRef.current

    if (!container || !progressBar || !counter || !logo) return

    // Initial setup
    gsap.set([logo, counter, progressBar], { opacity: 0, y: 30 })

    // Animation timeline
    const tl = gsap.timeline()

    // Logo entrance
    tl.to(logo, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: 'power3.out',
    })
    // Counter entrance
    .to(counter, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
    }, '-=0.5')
    // Progress bar entrance
    .to(progressBar, {
      opacity: 1,
      y: 0,
      duration: 0.6,
      ease: 'power2.out',
    }, '-=0.3')

    return () => {
      tl.kill()
    }
  }, [showContent])

  // Progress bar animation
  useEffect(() => {
    const progressBar = progressBarRef.current?.querySelector('.progress-fill')
    if (progressBar) {
      gsap.to(progressBar, {
        width: `${progress}%`,
        duration: 0.3,
        ease: 'power2.out',
      })
    }
  }, [progress])

  // Counter animation
  useEffect(() => {
    const counter = counterRef.current
    if (counter) {
      gsap.to(counter, {
        textContent: Math.floor(progress),
        duration: 0.3,
        ease: 'power2.out',
        snap: { textContent: 1 },
      })
    }
  }, [progress])

  const exitVariants = {
    hidden: { opacity: 1 },
    exit: {
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: 1,
        ease: [0.76, 0, 0.24, 1],
      }
    }
  }

  const curtainVariants = {
    hidden: { y: 0 },
    exit: {
      y: '-100%',
      transition: {
        duration: 1.2,
        ease: [0.76, 0, 0.24, 1],
        delay: 0.2,
      }
    }
  }

  return (
    <AnimatePresence>
      {!isComplete && (
        <motion.div
          ref={containerRef}
          className="fixed inset-0 z-[9999] bg-primary-black flex items-center justify-center overflow-hidden"
          variants={exitVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {/* Animated background elements */}
          <div className="absolute inset-0">
            {/* Moving grid */}
            <motion.div
              className="absolute inset-0 opacity-5"
              animate={{
                backgroundPosition: ['0px 0px', '50px 50px'],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: 'linear',
              }}
            >
              <div className="absolute inset-0 bg-[linear-gradient(90deg,transparent_24%,rgba(254,207,139,0.3)_25%,rgba(254,207,139,0.3)_26%,transparent_27%,transparent_74%,rgba(254,207,139,0.3)_75%,rgba(254,207,139,0.3)_76%,transparent_77%,transparent)] bg-[length:50px_50px]" />
              <div className="absolute inset-0 bg-[linear-gradient(0deg,transparent_24%,rgba(69,82,62,0.3)_25%,rgba(69,82,62,0.3)_26%,transparent_27%,transparent_74%,rgba(69,82,62,0.3)_75%,rgba(69,82,62,0.3)_76%,transparent_77%,transparent)] bg-[length:50px_50px]" />
            </motion.div>

            {/* Geometric shapes */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute border border-primary-peach/20"
                  style={{
                    width: `${60 + i * 20}px`,
                    height: `${60 + i * 20}px`,
                    left: `${10 + i * 12}%`,
                    top: `${15 + i * 8}%`,
                    borderRadius: i % 2 === 0 ? '50%' : '0%',
                  }}
                  animate={{
                    rotate: [0, 360],
                    scale: [1, 1.1, 1],
                    opacity: [0.1, 0.3, 0.1],
                  }}
                  transition={{
                    duration: 15 + i * 2,
                    repeat: Infinity,
                    ease: 'linear',
                    delay: i * 0.5,
                  }}
                />
              ))}
            </div>

            {/* Gradient orbs */}
            {[...Array(4)].map((_, i) => (
              <motion.div
                key={`orb-${i}`}
                className="absolute rounded-full blur-xl"
                style={{
                  width: `${100 + i * 50}px`,
                  height: `${100 + i * 50}px`,
                  left: `${20 + i * 20}%`,
                  top: `${30 + i * 15}%`,
                  background: `radial-gradient(circle, ${
                    i === 0 ? '#fecf8b15' : i === 1 ? '#45523e10' : i === 2 ? '#eeedf308' : '#01010105'
                  }, transparent)`,
                }}
                animate={{
                  x: [0, 30, 0],
                  y: [0, -20, 0],
                  scale: [1, 1.2, 1],
                }}
                transition={{
                  duration: 8 + i * 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: i * 1.5,
                }}
              />
            ))}
          </div>

          {/* Floating particles */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(20)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-primary-peach rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  y: [0, -30, 0],
                  opacity: [0.2, 1, 0.2],
                  scale: [1, 1.5, 1],
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: Math.random() * 2,
                }}
              />
            ))}
          </div>

          {/* Main content */}
          <div className="relative z-10 text-center">
            {/* Logo */}
            <div ref={logoRef} className="mb-16">
              <Typography
                variant="h1"
                font="clash"
                weight="bold"
                className="text-8xl md:text-9xl text-primary-neutral mb-4"
              >
                YN
              </Typography>
              <div className="flex items-center justify-center space-x-4">
                <div className="w-12 h-px bg-primary-peach" />
                <Typography
                  variant="body"
                  font="satoshi"
                  className="text-primary-neutral/80 tracking-[0.2em] text-sm"
                >
                  CREATIVE DEVELOPER
                </Typography>
                <div className="w-12 h-px bg-primary-peach" />
              </div>
            </div>

            {/* Loading word */}
            <div className="mb-12 h-8">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentWord}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5, ease: 'easeOut' }}
                >
                  <Typography
                    variant="h6"
                    font="satoshi"
                    weight="medium"
                    className="text-primary-peach tracking-[0.3em]"
                  >
                    {loadingWords[currentWord]}
                  </Typography>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Progress section */}
            <div className="w-80 mx-auto">
              {/* Progress bar */}
              <div ref={progressBarRef} className="relative mb-6">
                <div className="w-full h-px bg-primary-neutral/20">
                  <div className="progress-fill h-full bg-gradient-to-r from-primary-peach to-primary-green origin-left" />
                </div>
                
                {/* Progress dots */}
                <div className="absolute -top-1 left-0 w-2 h-2 bg-primary-peach rounded-full" />
                <div 
                  className="absolute -top-1 w-2 h-2 bg-primary-green rounded-full transition-all duration-300"
                  style={{ left: `calc(${progress}% - 4px)` }}
                />
              </div>

              {/* Counter and percentage */}
              <div className="flex justify-between items-center">
                <Typography
                  variant="caption"
                  font="satoshi"
                  className="text-primary-neutral/60 text-xs tracking-wider"
                >
                  LOADING EXPERIENCE
                </Typography>
                
                <div className="flex items-center space-x-2">
                  <div
                    ref={counterRef}
                    className="text-primary-neutral font-mono text-sm font-medium"
                  >
                    0
                  </div>
                  <Typography
                    variant="caption"
                    className="text-primary-neutral/60 text-sm"
                  >
                    %
                  </Typography>
                </div>
              </div>
            </div>

            {/* Bottom text */}
            <motion.div
              className="absolute bottom-12 left-1/2 transform -translate-x-1/2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.5, duration: 1 }}
            >
              <Typography
                variant="caption"
                font="satoshi"
                className="text-primary-neutral/40 text-xs tracking-[0.2em]"
              >
                CRAFTING DIGITAL EXPERIENCES
              </Typography>
            </motion.div>
          </div>

          {/* Exit curtain effect */}
          <motion.div
            className="absolute inset-0 bg-primary-black z-20"
            variants={curtainVariants}
            initial="hidden"
            exit="exit"
          />
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default AwwardsPreloader
