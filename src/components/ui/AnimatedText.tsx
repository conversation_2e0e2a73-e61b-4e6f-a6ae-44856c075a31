'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { cn } from '@/lib/utils'

interface AnimatedTextProps {
  text: string
  className?: string
  delay?: number
  duration?: number
  stagger?: number
  as?: keyof JSX.IntrinsicElements
}

const AnimatedText = ({ 
  text, 
  className, 
  delay = 0, 
  duration = 0.8, 
  stagger = 0.03,
  as: Component = 'span'
}: AnimatedTextProps) => {
  const textRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const element = textRef.current
    if (!element) return

    // Split text into characters
    const chars = text.split('').map((char, index) => {
      const span = document.createElement('span')
      span.textContent = char === ' ' ? '\u00A0' : char
      span.style.display = 'inline-block'
      span.style.opacity = '0'
      span.style.transform = 'translateY(100px) rotateX(90deg)'
      return span
    })

    // Clear element and add character spans
    element.innerHTML = ''
    chars.forEach(char => element.appendChild(char))

    // Animate characters
    gsap.to(chars, {
      opacity: 1,
      y: 0,
      rotationX: 0,
      duration,
      stagger,
      delay,
      ease: 'back.out(1.7)',
    })

    return () => {
      // Cleanup
      if (element) {
        element.innerHTML = text
      }
    }
  }, [text, delay, duration, stagger])

  return (
    <Component
      ref={textRef}
      className={cn('inline-block', className)}
      style={{ perspective: '1000px' }}
    >
      {text}
    </Component>
  )
}

export default AnimatedText
