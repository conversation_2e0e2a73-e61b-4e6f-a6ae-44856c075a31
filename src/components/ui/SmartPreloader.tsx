'use client'

import { useEffect, useState } from 'react'
import AwwardsPreloader from './AwwardsPreloader'
import MinimalPreloader from './MinimalPreloader'
import preloaderConfig from '@/config/preloader'

interface SmartPreloaderProps {
  onComplete?: () => void
}

const SmartPreloader = ({ onComplete }: SmartPreloaderProps) => {
  const [shouldShow, setShouldShow] = useState(false)

  useEffect(() => {
    // Check if preloader is enabled
    if (!preloaderConfig.enabled) {
      onComplete?.()
      return
    }

    // Check if should show only on first visit
    if (preloaderConfig.showOnlyOnFirstVisit) {
      const hasVisited = localStorage.getItem('hasVisited')
      if (hasVisited) {
        onComplete?.()
        return
      } else {
        localStorage.setItem('hasVisited', 'true')
      }
    }

    setShouldShow(true)
  }, [onComplete])

  const handleComplete = () => {
    setShouldShow(false)
    onComplete?.()
  }

  if (!shouldShow) {
    return null
  }

  // Render based on configuration
  switch (preloaderConfig.style) {
    case 'awwwards':
      return (
        <AwwardsPreloader 
          onComplete={handleComplete}
          duration={preloaderConfig.duration}
        />
      )
    
    case 'minimal':
      return (
        <MinimalPreloader 
          onComplete={handleComplete}
          duration={preloaderConfig.duration}
        />
      )
    
    case 'disabled':
    default:
      return null
  }
}

export default SmartPreloader
