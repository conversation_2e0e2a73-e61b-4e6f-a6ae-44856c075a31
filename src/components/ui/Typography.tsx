'use client'

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body' | 'caption' | 'overline'
  font?: 'clash' | 'satoshi' | 'inter'
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold'
  color?: 'primary' | 'secondary' | 'muted' | 'accent'
  as?: keyof JSX.IntrinsicElements
  children: React.ReactNode
}

const Typography = forwardRef<HTMLElement, TypographyProps>(
  ({ 
    className, 
    variant = 'body', 
    font = 'inter', 
    weight = 'normal',
    color = 'primary',
    as,
    children, 
    ...props 
  }, ref) => {
    const variants = {
      h1: 'text-6xl md:text-8xl lg:text-9xl leading-none',
      h2: 'text-4xl md:text-5xl lg:text-6xl leading-tight',
      h3: 'text-3xl md:text-4xl leading-tight',
      h4: 'text-2xl md:text-3xl leading-snug',
      h5: 'text-xl md:text-2xl leading-snug',
      h6: 'text-lg md:text-xl leading-normal',
      body: 'text-base md:text-lg leading-relaxed',
      caption: 'text-sm leading-normal',
      overline: 'text-xs uppercase tracking-wider leading-normal',
    }

    const fonts = {
      clash: 'font-clash',
      satoshi: 'font-satoshi',
      inter: 'font-inter',
    }

    const weights = {
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
    }

    const colors = {
      primary: 'text-primary-black',
      secondary: 'text-primary-green',
      muted: 'text-primary-black/70',
      accent: 'text-primary-peach',
    }

    const defaultElements = {
      h1: 'h1',
      h2: 'h2',
      h3: 'h3',
      h4: 'h4',
      h5: 'h5',
      h6: 'h6',
      body: 'p',
      caption: 'span',
      overline: 'span',
    }

    const Component = as || defaultElements[variant] || 'p'

    return (
      <Component
        ref={ref}
        className={cn(
          variants[variant],
          fonts[font],
          weights[weight],
          colors[color],
          className
        )}
        {...props}
      >
        {children}
      </Component>
    )
  }
)

Typography.displayName = 'Typography'

export default Typography
