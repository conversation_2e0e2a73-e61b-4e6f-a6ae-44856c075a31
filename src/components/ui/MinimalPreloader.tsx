'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { gsap } from 'gsap'

interface MinimalPreloaderProps {
  onComplete?: () => void
  duration?: number
}

const MinimalPreloader = ({ onComplete, duration = 2500 }: MinimalPreloaderProps) => {
  const [progress, setProgress] = useState(0)
  const [isComplete, setIsComplete] = useState(false)

  useEffect(() => {
    // Smooth progress animation
    const progressTween = gsap.to({ value: 0 }, {
      value: 100,
      duration: duration / 1000,
      ease: 'power2.out',
      onUpdate: function() {
        setProgress(Math.round(this.targets()[0].value))
      },
      onComplete: () => {
        setTimeout(() => {
          setIsComplete(true)
          setTimeout(() => {
            onComplete?.()
          }, 800)
        }, 300)
      }
    })

    return () => {
      progressTween.kill()
    }
  }, [onComplete, duration])

  const containerVariants = {
    hidden: { opacity: 1 },
    exit: {
      opacity: 0,
      transition: {
        duration: 0.8,
        ease: [0.76, 0, 0.24, 1],
      }
    }
  }

  const logoVariants = {
    hidden: { 
      scale: 0.8,
      opacity: 0,
    },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: 'back.out(1.7)',
        delay: 0.2
      }
    }
  }

  const progressVariants = {
    hidden: { width: 0 },
    visible: {
      width: `${progress}%`,
      transition: {
        duration: 0.3,
        ease: 'easeOut'
      }
    }
  }

  return (
    <AnimatePresence>
      {!isComplete && (
        <motion.div
          className="fixed inset-0 z-[9999] bg-primary-neutral flex items-center justify-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          {/* Subtle background pattern */}
          <div className="absolute inset-0 opacity-[0.02]">
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,#010101_1px,transparent_1px)] bg-[length:40px_40px]" />
          </div>

          <div className="relative z-10 text-center">
            {/* Logo */}
            <motion.div
              variants={logoVariants}
              className="mb-16"
            >
              <div className="text-8xl md:text-9xl font-clash font-bold text-primary-black mb-4">
                YN
              </div>
              <div className="text-primary-black/60 font-satoshi tracking-[0.3em] text-sm">
                CREATIVE DEVELOPER
              </div>
            </motion.div>

            {/* Progress */}
            <div className="w-64 mx-auto">
              {/* Progress bar */}
              <div className="relative mb-4">
                <div className="w-full h-px bg-primary-black/10">
                  <motion.div
                    className="h-full bg-primary-black origin-left"
                    variants={progressVariants}
                    animate="visible"
                  />
                </div>
              </div>

              {/* Counter */}
              <div className="flex justify-between items-center text-xs">
                <span className="text-primary-black/40 font-satoshi tracking-wider">
                  LOADING
                </span>
                <span className="text-primary-black font-mono">
                  {progress.toString().padStart(2, '0')}%
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default MinimalPreloader
