'use client'

import { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Project } from '@/lib/api'
import Typography from './Typography'
import Button from './Button'

interface ProjectModalProps {
  project: Project | null
  isOpen: boolean
  onClose: () => void
}

const ProjectModal = ({ project, isOpen, onClose }: ProjectModalProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!project) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Modal Content */}
          <motion.div
            className="relative bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            {/* Close button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 w-10 h-10 bg-white/90 hover:bg-white rounded-full flex items-center justify-center transition-colors duration-200"
              data-cursor="pointer"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18" />
                <line x1="6" y1="6" x2="18" y2="18" />
              </svg>
            </button>

            {/* Project Image */}
            <div className="aspect-video bg-gradient-to-br from-primary-peach/20 to-primary-green/20 relative overflow-hidden rounded-t-2xl">
              <div className="absolute inset-0 bg-primary-peach/30" />
              <div className="absolute top-6 left-6">
                <span className="px-3 py-1 bg-white/90 text-primary-black text-sm font-medium rounded-full">
                  {project.category}
                </span>
              </div>
            </div>

            {/* Content */}
            <div className="p-8">
              <div className="flex items-start justify-between mb-6">
                <div>
                  <Typography
                    variant="h3"
                    font="clash"
                    weight="bold"
                    className="mb-2"
                  >
                    {project.title}
                  </Typography>
                  <Typography
                    variant="body"
                    color="muted"
                    className="text-sm"
                  >
                    {project.year}
                  </Typography>
                </div>
              </div>

              <Typography
                variant="body"
                color="muted"
                className="mb-8 leading-relaxed"
              >
                {project.description}
              </Typography>

              {/* Technologies */}
              <div className="mb-8">
                <Typography
                  variant="h6"
                  font="satoshi"
                  weight="semibold"
                  className="mb-4"
                >
                  Technologies Used
                </Typography>
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech) => (
                    <span
                      key={tech}
                      className="px-3 py-1 bg-primary-neutral text-primary-black text-sm rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Project Details */}
              <div className="mb-8">
                <Typography
                  variant="h6"
                  font="satoshi"
                  weight="semibold"
                  className="mb-4"
                >
                  Project Details
                </Typography>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Typography
                      variant="body"
                      weight="medium"
                      className="mb-2"
                    >
                      Category
                    </Typography>
                    <Typography
                      variant="caption"
                      color="muted"
                    >
                      {project.category}
                    </Typography>
                  </div>
                  <div>
                    <Typography
                      variant="body"
                      weight="medium"
                      className="mb-2"
                    >
                      Year
                    </Typography>
                    <Typography
                      variant="caption"
                      color="muted"
                    >
                      {project.year}
                    </Typography>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                {project.url && (
                  <Button
                    variant="primary"
                    size="md"
                    onClick={() => window.open(project.url, '_blank')}
                  >
                    View Live Site
                  </Button>
                )}
                {project.github && (
                  <Button
                    variant="outline"
                    size="md"
                    onClick={() => window.open(project.github, '_blank')}
                  >
                    View Code
                  </Button>
                )}
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default ProjectModal
