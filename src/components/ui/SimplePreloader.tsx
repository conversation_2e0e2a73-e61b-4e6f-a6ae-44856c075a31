'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface SimplePreloaderProps {
  onComplete?: () => void
  duration?: number
}

const SimplePreloader = ({ onComplete, duration = 3000 }: SimplePreloaderProps) => {
  const [progress, setProgress] = useState(0)
  const [isComplete, setIsComplete] = useState(false)

  useEffect(() => {
    // Simple progress animation
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 2
        if (newProgress >= 100) {
          clearInterval(interval)
          setTimeout(() => {
            setIsComplete(true)
            setTimeout(() => {
              onComplete?.()
            }, 500)
          }, 300)
          return 100
        }
        return newProgress
      })
    }, duration / 50)

    return () => clearInterval(interval)
  }, [onComplete, duration])

  return (
    <AnimatePresence>
      {!isComplete && (
        <motion.div
          className="fixed inset-0 z-[9999] bg-white flex items-center justify-center"
          initial={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="text-center">
            {/* Logo */}
            <div className="mb-8">
              <h1 className="text-6xl font-bold text-black mb-2">YN</h1>
              <p className="text-black/60 text-sm tracking-widest">CREATIVE DEVELOPER</p>
            </div>

            {/* Loading text */}
            <div className="mb-6">
              <p className="text-orange-500 text-sm font-medium tracking-wider">LOADING</p>
            </div>

            {/* Progress bar */}
            <div className="w-64 mx-auto mb-4">
              <div className="w-full h-1 bg-gray-200 rounded">
                <div 
                  className="h-full bg-orange-500 rounded transition-all duration-100"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>

            {/* Counter */}
            <div className="text-black text-sm font-mono">
              {progress}%
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default SimplePreloader
