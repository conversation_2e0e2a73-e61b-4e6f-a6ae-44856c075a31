'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'

const CustomCursor = () => {
  const cursorDotRef = useRef<HTMLDivElement>(null)
  const cursorOutlineRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const cursorDot = cursorDotRef.current
    const cursorOutline = cursorOutlineRef.current

    if (!cursorDot || !cursorOutline) return

    // Hide default cursor
    document.body.style.cursor = 'none'

    const handleMouseMove = (e: MouseEvent) => {
      gsap.to(cursorDot, {
        x: e.clientX - 4,
        y: e.clientY - 4,
        duration: 0.1,
        ease: 'power2.out',
      })

      gsap.to(cursorOutline, {
        x: e.clientX - 16,
        y: e.clientY - 16,
        duration: 0.3,
        ease: 'power2.out',
      })
    }

    const handleMouseEnter = () => {
      gsap.to([cursorDot, cursorOutline], {
        scale: 1.5,
        duration: 0.3,
        ease: 'power2.out',
      })
    }

    const handleMouseLeave = () => {
      gsap.to([cursorDot, cursorOutline], {
        scale: 1,
        duration: 0.3,
        ease: 'power2.out',
      })
    }

    const handleMouseDown = () => {
      gsap.to([cursorDot, cursorOutline], {
        scale: 0.8,
        duration: 0.1,
        ease: 'power2.out',
      })
    }

    const handleMouseUp = () => {
      gsap.to([cursorDot, cursorOutline], {
        scale: 1,
        duration: 0.1,
        ease: 'power2.out',
      })
    }

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mouseup', handleMouseUp)

    // Add hover effects for interactive elements
    const interactiveElements = document.querySelectorAll('a, button, [data-cursor="pointer"]')
    
    interactiveElements.forEach((el) => {
      el.addEventListener('mouseenter', handleMouseEnter)
      el.addEventListener('mouseleave', handleMouseLeave)
    })

    return () => {
      document.body.style.cursor = 'auto'
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mouseup', handleMouseUp)
      
      interactiveElements.forEach((el) => {
        el.removeEventListener('mouseenter', handleMouseEnter)
        el.removeEventListener('mouseleave', handleMouseLeave)
      })
    }
  }, [])

  return (
    <>
      <div
        ref={cursorDotRef}
        className="cursor-dot fixed top-0 left-0 w-2 h-2 bg-primary-black rounded-full pointer-events-none z-[9999] mix-blend-difference"
      />
      <div
        ref={cursorOutlineRef}
        className="cursor-outline fixed top-0 left-0 w-8 h-8 border-2 border-primary-black rounded-full pointer-events-none z-[9998] opacity-50"
      />
    </>
  )
}

export default CustomCursor
