'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const ProjectsSection = () => {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const section = sectionRef.current
    if (!section) return

    // Animate section on scroll
    gsap.fromTo(
      section.querySelectorAll('.project-card'),
      { opacity: 0, y: 100 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: section,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section ref={sectionRef} className="py-20 px-6 bg-white">
      <div className="max-w-7xl mx-auto">
        <h2 className="font-clash text-5xl md:text-6xl font-bold text-center mb-16">
          Featured <span className="text-primary-green">Projects</span>
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[1, 2, 3, 4, 5, 6].map((project) => (
            <div
              key={project}
              className="project-card bg-primary-neutral rounded-2xl p-6 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="aspect-video bg-primary-peach/20 rounded-lg mb-4" />
              <h3 className="font-satoshi text-xl font-semibold mb-2">
                Project {project}
              </h3>
              <p className="text-primary-black/70 text-sm">
                A stunning web application built with modern technologies.
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default ProjectsSection
