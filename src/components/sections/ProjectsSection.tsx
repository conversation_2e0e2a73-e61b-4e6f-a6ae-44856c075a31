'use client'

import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Card from '@/components/ui/Card'
import Container from '@/components/ui/Container'
import ProjectModal from '@/components/ui/ProjectModal'
import { useScrollAnimations } from '@/hooks/useScrollAnimations'
import { parallax, imageZoom } from '@/lib/animations'
import { fetchProjects, Project, getProjectCategories, filterProjectsByCategory } from '@/lib/api'

gsap.registerPlugin(ScrollTrigger)

const ProjectsSection = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLDivElement>(null)
  const { staggerAnimation, slideIn, parallax: parallaxHook } = useScrollAnimations()

  const [projects, setProjects] = useState<Project[]>([])
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [activeCategory, setActiveCategory] = useState('All')
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)

  // Fetch projects on component mount
  useEffect(() => {
    const loadProjects = async () => {
      try {
        setLoading(true)
        const projectsData = await fetchProjects()
        setProjects(projectsData)
        setFilteredProjects(projectsData)

        const projectCategories = getProjectCategories(projectsData)
        setCategories(['All', ...projectCategories])
      } catch (error) {
        console.error('Failed to load projects:', error)
      } finally {
        setLoading(false)
      }
    }

    loadProjects()
  }, [])

  // Filter projects when category changes
  useEffect(() => {
    const filtered = filterProjectsByCategory(projects, activeCategory)
    setFilteredProjects(filtered)
  }, [projects, activeCategory])

  const handleProjectClick = (project: Project) => {
    setSelectedProject(project)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedProject(null)
  }

  useEffect(() => {
    const section = sectionRef.current
    const title = titleRef.current
    if (!section || !title) return

    // Animate title
    slideIn(title, 'up', { start: 'top 90%' })

    // Stagger animate project cards
    staggerAnimation('.project-card', 'fadeIn', {
      trigger: section,
      start: 'top 70%',
      stagger: 0.15,
    })

    // Add parallax to project images
    section.querySelectorAll('.project-image').forEach((img, index) => {
      parallax(img, { speed: 0.3 + (index % 3) * 0.1 })
      imageZoom(img, { scale: 1.1 })
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section id="projects" ref={sectionRef} className="py-20 bg-white relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 right-10 w-32 h-32 bg-primary-peach/5 rounded-full blur-xl" />
        <div className="absolute bottom-20 left-10 w-48 h-48 bg-primary-green/5 rounded-full blur-2xl" />
      </div>

      <Container size="xl" className="relative z-10">
        <div ref={titleRef} className="text-center mb-16">
          <Typography
            variant="h2"
            font="clash"
            weight="bold"
            className="mb-4"
          >
            Featured <span className="text-primary-green">Projects</span>
          </Typography>
          <Typography
            variant="body"
            color="muted"
            className="max-w-2xl mx-auto"
          >
            A showcase of my latest work, featuring cutting-edge technologies
            and innovative design solutions.
          </Typography>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <motion.button
              key={category}
              onClick={() => setActiveCategory(category)}
              className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                activeCategory === category
                  ? 'bg-primary-black text-white'
                  : 'bg-primary-neutral text-primary-black hover:bg-primary-peach/20'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              data-cursor="pointer"
            >
              {category}
            </motion.button>
          ))}
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-20">
            <div className="w-8 h-8 border-2 border-primary-peach border-t-transparent rounded-full animate-spin" />
          </div>
        )}

        {/* Projects Grid */}
        {!loading && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                className="project-card group cursor-pointer"
                whileHover={{ y: -8 }}
                transition={{ duration: 0.3, ease: 'easeOut' }}
                onClick={() => handleProjectClick(project)}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card
                  className="bg-primary-neutral h-full overflow-hidden"
                  variant="default"
                  padding="none"
                  rounded="xl"
                >
                  {/* Project Image */}
                  <div className="aspect-video bg-gradient-to-br from-primary-peach/20 to-primary-green/20 relative overflow-hidden">
                    <div
                      className="project-image w-full h-full bg-primary-peach/30 transition-transform duration-700 group-hover:scale-110"
                      style={{
                        backgroundImage: `linear-gradient(135deg,
                          ${index % 3 === 0 ? '#fecf8b40' : index % 3 === 1 ? '#45523e40' : '#01010140'},
                          transparent)`,
                      }}
                    />
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300" />

                    {/* Category badge */}
                    <div className="absolute top-4 left-4">
                      <span className="px-3 py-1 bg-white/90 text-primary-black text-xs font-medium rounded-full">
                        {project.category}
                      </span>
                    </div>

                    {/* Hover overlay */}
                    <div className="absolute inset-0 bg-primary-black/0 group-hover:bg-primary-black/20 transition-colors duration-300 flex items-center justify-center">
                      <motion.div
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        whileHover={{ scale: 1.1 }}
                      >
                        <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                          <svg
                            width="20"
                            height="20"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                            <circle cx="12" cy="12" r="3" />
                          </svg>
                        </div>
                      </motion.div>
                    </div>
                  </div>

                  {/* Project Content */}
                  <div className="p-6">
                    <Typography
                      variant="h5"
                      font="satoshi"
                      weight="semibold"
                      className="mb-3 group-hover:text-primary-green transition-colors duration-300"
                    >
                      {project.title}
                    </Typography>

                    <Typography
                      variant="caption"
                      color="muted"
                      className="mb-4 line-clamp-2"
                    >
                      {project.description}
                    </Typography>

                    {/* Technologies */}
                    <div className="flex flex-wrap gap-2">
                      {project.technologies.slice(0, 3).map((tech) => (
                        <span
                          key={tech}
                          className="px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded"
                        >
                          {tech}
                        </span>
                      ))}
                      {project.technologies.length > 3 && (
                        <span className="px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded">
                          +{project.technologies.length - 3}
                        </span>
                      )}
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {/* Project Modal */}
        <ProjectModal
          project={selectedProject}
          isOpen={isModalOpen}
          onClose={handleCloseModal}
        />
      </Container>
    </section>
  )
}

export default ProjectsSection
