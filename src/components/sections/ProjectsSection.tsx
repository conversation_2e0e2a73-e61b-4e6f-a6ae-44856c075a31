'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import Typography from '@/components/ui/Typography'
import Card from '@/components/ui/Card'
import Container from '@/components/ui/Container'

gsap.registerPlugin(ScrollTrigger)

const ProjectsSection = () => {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const section = sectionRef.current
    if (!section) return

    // Animate section on scroll
    gsap.fromTo(
      section.querySelectorAll('.project-card'),
      { opacity: 0, y: 100 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: section,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse',
        },
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-white">
      <Container size="xl">
        <Typography
          variant="h2"
          font="clash"
          weight="bold"
          className="text-center mb-16"
        >
          Featured <span className="text-primary-green">Projects</span>
        </Typography>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {[1, 2, 3, 4, 5, 6].map((project) => (
            <Card
              key={project}
              className="project-card bg-primary-neutral"
              variant="default"
              padding="md"
              rounded="xl"
              hover
            >
              <div className="aspect-video bg-primary-peach/20 rounded-lg mb-4" />
              <Typography
                variant="h5"
                font="satoshi"
                weight="semibold"
                className="mb-2"
              >
                Project {project}
              </Typography>
              <Typography
                variant="caption"
                color="muted"
              >
                A stunning web application built with modern technologies.
              </Typography>
            </Card>
          ))}
        </div>
      </Container>
    </section>
  )
}

export default ProjectsSection
