'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const SkillsSection = () => {
  const sectionRef = useRef<HTMLElement>(null)

  const skills = [
    'React', 'Next.js', 'TypeScript', 'Node.js', 'Python', 'GSAP',
    'Framer Motion', 'Tailwind CSS', 'Three.js', 'WebGL', 'PHP', 'MySQL'
  ]

  useEffect(() => {
    const section = sectionRef.current
    if (!section) return

    gsap.fromTo(
      section.querySelectorAll('.skill-item'),
      { opacity: 0, scale: 0.8 },
      {
        opacity: 1,
        scale: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: 'back.out(1.7)',
        scrollTrigger: {
          trigger: section,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section ref={sectionRef} className="py-20 px-6 bg-primary-neutral">
      <div className="max-w-6xl mx-auto">
        <h2 className="font-clash text-5xl md:text-6xl font-bold text-center mb-16">
          Skills & <span className="text-primary-peach">Expertise</span>
        </h2>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {skills.map((skill, index) => (
            <div
              key={skill}
              className="skill-item bg-white rounded-xl p-6 text-center hover:bg-primary-peach/10 transition-colors duration-300"
            >
              <h3 className="font-satoshi text-lg font-semibold">{skill}</h3>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default SkillsSection
