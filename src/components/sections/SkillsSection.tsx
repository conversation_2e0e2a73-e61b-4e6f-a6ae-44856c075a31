'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Container from '@/components/ui/Container'
import { useScrollAnimations } from '@/hooks/useScrollAnimations'

gsap.registerPlugin(ScrollTrigger)

const SkillsSection = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLDivElement>(null)
  const { staggerAnimation, slideIn } = useScrollAnimations()

  const skillCategories = [
    {
      title: 'Frontend',
      skills: [
        { name: 'React', level: 95, color: '#61DAFB' },
        { name: 'Next.js', level: 90, color: '#000000' },
        { name: 'TypeScript', level: 88, color: '#3178C6' },
        { name: 'Tailwind CSS', level: 92, color: '#06B6D4' },
      ]
    },
    {
      title: 'Animation',
      skills: [
        { name: 'GSA<PERSON>', level: 85, color: '#88CE02' },
        { name: 'Framer Motion', level: 80, color: '#0055FF' },
        { name: 'Three.js', level: 75, color: '#000000' },
        { name: 'WebGL', level: 70, color: '#990000' },
      ]
    },
    {
      title: 'Backend',
      skills: [
        { name: 'Node.js', level: 85, color: '#339933' },
        { name: 'Python', level: 80, color: '#3776AB' },
        { name: 'PHP', level: 75, color: '#777BB4' },
        { name: 'MySQL', level: 82, color: '#4479A1' },
      ]
    }
  ]

  useEffect(() => {
    const section = sectionRef.current
    const title = titleRef.current
    if (!section || !title) return

    // Animate title
    slideIn(title, 'up', { start: 'top 90%' })

    // Stagger animate skill categories
    staggerAnimation('.skill-category', 'fadeIn', {
      trigger: section,
      start: 'top 70%',
      stagger: 0.2,
    })

    // Animate skill bars
    section.querySelectorAll('.skill-bar').forEach((bar, index) => {
      const level = bar.getAttribute('data-level')
      gsap.fromTo(bar,
        { width: '0%' },
        {
          width: `${level}%`,
          duration: 1.5,
          ease: 'power2.out',
          delay: index * 0.1,
          scrollTrigger: {
            trigger: bar,
            start: 'top 85%',
            toggleActions: 'play none none reverse',
          },
        }
      )
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-primary-neutral relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,#010101_1px,transparent_1px)] bg-[length:50px_50px]" />
      </div>

      <Container size="xl" className="relative z-10">
        <div ref={titleRef} className="text-center mb-16">
          <Typography
            variant="h2"
            font="clash"
            weight="bold"
            className="mb-4"
          >
            Skills & <span className="text-primary-peach">Expertise</span>
          </Typography>
          <Typography
            variant="body"
            color="muted"
            className="max-w-2xl mx-auto"
          >
            A comprehensive overview of my technical skills and proficiency levels
            across different technologies and frameworks.
          </Typography>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              className="skill-category"
              whileHover={{ y: -4 }}
              transition={{ duration: 0.3 }}
            >
              <div className="bg-white rounded-2xl p-8 h-full shadow-sm hover:shadow-lg transition-shadow duration-300">
                <Typography
                  variant="h4"
                  font="satoshi"
                  weight="bold"
                  className="mb-6 text-center"
                  style={{ color: skillCategories[categoryIndex].skills[0].color }}
                >
                  {category.title}
                </Typography>

                <div className="space-y-6">
                  {category.skills.map((skill, skillIndex) => (
                    <div key={skill.name} className="skill-item">
                      <div className="flex justify-between items-center mb-2">
                        <Typography
                          variant="body"
                          font="satoshi"
                          weight="medium"
                          className="text-sm"
                        >
                          {skill.name}
                        </Typography>
                        <Typography
                          variant="caption"
                          color="muted"
                          className="text-xs"
                        >
                          {skill.level}%
                        </Typography>
                      </div>

                      <div className="w-full bg-primary-neutral rounded-full h-2 overflow-hidden">
                        <div
                          className="skill-bar h-full rounded-full transition-all duration-300"
                          data-level={skill.level}
                          style={{ backgroundColor: skill.color }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional stats */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {[
            { number: '50+', label: 'Projects Completed' },
            { number: '5+', label: 'Years Experience' },
            { number: '15+', label: 'Technologies' },
            { number: '100%', label: 'Client Satisfaction' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              className="skill-stat"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <Typography
                variant="h3"
                font="clash"
                weight="bold"
                className="text-primary-peach mb-2"
              >
                {stat.number}
              </Typography>
              <Typography
                variant="caption"
                color="muted"
                className="text-sm"
              >
                {stat.label}
              </Typography>
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  )
}

export default SkillsSection
