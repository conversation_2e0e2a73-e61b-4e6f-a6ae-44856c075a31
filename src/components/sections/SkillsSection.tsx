'use client'

import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Container from '@/components/ui/Container'
import { useScrollAnimations } from '@/hooks/useScrollAnimations'
import { fetchSkills, type SkillCategory } from '@/lib/api'

gsap.registerPlugin(ScrollTrigger)

const SkillsSection = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLDivElement>(null)
  const { staggerAnimation, slideIn } = useScrollAnimations()

  const [skillCategories, setSkillCategories] = useState<SkillCategory[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch skills data from Laravel API
  useEffect(() => {
    const loadSkills = async () => {
      try {
        const data = await fetchSkills()
        setSkillCategories(data)
      } catch (error) {
        console.error('Failed to load skills:', error)
        // Fallback data
        setSkillCategories([
          {
            id: 1,
            name: 'Frontend',
            slug: 'frontend',
            description: 'Frontend technologies',
            icon: null,
            color: '#61DAFB',
            skills: [
              { id: 1, name: 'React', description: null, icon: null, proficiency_level: 95, years_experience: 5, is_featured: true },
              { id: 2, name: 'Next.js', description: null, icon: null, proficiency_level: 90, years_experience: 3, is_featured: true },
              { id: 3, name: 'TypeScript', description: null, icon: null, proficiency_level: 88, years_experience: 4, is_featured: true },
              { id: 4, name: 'Tailwind CSS', description: null, icon: null, proficiency_level: 92, years_experience: 3, is_featured: false },
            ]
          }
        ])
      } finally {
        setLoading(false)
      }
    }

    loadSkills()
  }, [])

  useEffect(() => {
    const section = sectionRef.current
    const title = titleRef.current
    if (!section || !title) return

    // Animate title
    slideIn(title, 'up', { start: 'top 90%' })

    // Stagger animate skill categories
    staggerAnimation('.skill-category', 'fadeIn', {
      trigger: section,
      start: 'top 70%',
      stagger: 0.2,
    })

    // Animate skill bars
    section.querySelectorAll('.skill-bar').forEach((bar, index) => {
      const level = bar.getAttribute('data-level')
      gsap.fromTo(bar,
        { width: '0%' },
        {
          width: `${level}%`,
          duration: 1.5,
          ease: 'power2.out',
          delay: index * 0.1,
          scrollTrigger: {
            trigger: bar,
            start: 'top 85%',
            toggleActions: 'play none none reverse',
          },
        }
      )
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section id="skills" ref={sectionRef} className="py-20 bg-primary-neutral relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,#010101_1px,transparent_1px)] bg-[length:50px_50px]" />
      </div>

      <Container size="xl" className="relative z-10">
        <div ref={titleRef} className="text-center mb-16">
          <Typography
            variant="h2"
            font="clash"
            weight="bold"
            className="mb-4"
          >
            Skills & <span className="text-primary-peach">Expertise</span>
          </Typography>
          <Typography
            variant="body"
            color="muted"
            className="max-w-2xl mx-auto"
          >
            A comprehensive overview of my technical skills and proficiency levels
            across different technologies and frameworks.
          </Typography>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {loading ? (
            // Loading skeleton
            [...Array(3)].map((_, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 h-full shadow-sm">
                <div className="h-6 bg-gray-200 rounded animate-pulse mb-6 w-24 mx-auto"></div>
                <div className="space-y-6">
                  {[...Array(4)].map((_, skillIndex) => (
                    <div key={skillIndex} className="skill-item">
                      <div className="flex justify-between items-center mb-2">
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-16"></div>
                        <div className="h-3 bg-gray-200 rounded animate-pulse w-8"></div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 animate-pulse"></div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          ) : (
            skillCategories.map((category, categoryIndex) => (
              <motion.div
                key={category.id}
                className="skill-category"
                whileHover={{ y: -4 }}
                transition={{ duration: 0.3 }}
              >
                <div className="bg-white rounded-2xl p-8 h-full shadow-sm hover:shadow-lg transition-shadow duration-300">
                  <Typography
                    variant="h4"
                    font="satoshi"
                    weight="bold"
                    className="mb-6 text-center"
                    style={{ color: category.color || '#010101' }}
                  >
                    {category.name}
                  </Typography>

                  <div className="space-y-6">
                    {category.skills.map((skill, skillIndex) => (
                      <div key={skill.id} className="skill-item">
                        <div className="flex justify-between items-center mb-2">
                          <Typography
                            variant="body"
                            font="satoshi"
                            weight="medium"
                            className="text-sm"
                          >
                            {skill.name}
                          </Typography>
                          <Typography
                            variant="caption"
                            color="muted"
                            className="text-xs"
                          >
                            {skill.proficiency_level}%
                          </Typography>
                        </div>

                        <div className="w-full bg-primary-neutral rounded-full h-2 overflow-hidden">
                          <div
                            className="skill-bar h-full rounded-full transition-all duration-300"
                            data-level={skill.proficiency_level}
                            style={{
                              backgroundColor: category.color || '#010101',
                              width: `${skill.proficiency_level}%`
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional stats */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {[
            { number: '50+', label: 'Projects Completed' },
            { number: '5+', label: 'Years Experience' },
            { number: '15+', label: 'Technologies' },
            { number: '100%', label: 'Client Satisfaction' },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              className="skill-stat"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <Typography
                variant="h3"
                font="clash"
                weight="bold"
                className="text-primary-peach mb-2"
              >
                {stat.number}
              </Typography>
              <Typography
                variant="caption"
                color="muted"
                className="text-sm"
              >
                {stat.label}
              </Typography>
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  )
}

export default SkillsSection
