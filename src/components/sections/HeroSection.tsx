'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Button from '@/components/ui/Button'
import Container from '@/components/ui/Container'
import AnimatedText from '@/components/ui/AnimatedText'

gsap.registerPlugin(ScrollTrigger)

const HeroSection = () => {
  const heroRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const subtitleRef = useRef<HTMLParagraphElement>(null)
  const backgroundRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const hero = heroRef.current
    const title = titleRef.current
    const subtitle = subtitleRef.current
    const background = backgroundRef.current

    if (!hero || !title || !subtitle || !background) return

    // Initial animation timeline
    const tl = gsap.timeline()

    // Set initial states
    gsap.set([title, subtitle], { opacity: 0, y: 100, rotationX: 45 })
    gsap.set(background, { scale: 1.2, opacity: 0, rotation: 5 })

    // Animate in with more sophisticated effects
    tl.to(background, {
      opacity: 0.15,
      scale: 1,
      rotation: 0,
      duration: 2.5,
      ease: 'power3.out',
    })
    .to(title, {
      opacity: 1,
      y: 0,
      rotationX: 0,
      duration: 1.5,
      ease: 'back.out(1.7)',
      stagger: 0.1,
    }, '-=2')
    .to(subtitle, {
      opacity: 1,
      y: 0,
      rotationX: 0,
      duration: 1.2,
      ease: 'power3.out',
    }, '-=1')

    // Enhanced mouse move parallax effect
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e
      const { innerWidth, innerHeight } = window

      const xPos = (clientX / innerWidth - 0.5) * 2
      const yPos = (clientY / innerHeight - 0.5) * 2

      // Multi-layer parallax
      gsap.to(background, {
        x: xPos * 30,
        y: yPos * 30,
        rotation: xPos * 2,
        duration: 0.8,
        ease: 'power2.out',
      })

      gsap.to(title, {
        x: xPos * 15,
        y: yPos * 15,
        rotationY: xPos * 5,
        duration: 0.5,
        ease: 'power2.out',
      })

      gsap.to(subtitle, {
        x: xPos * 8,
        y: yPos * 8,
        duration: 0.6,
        ease: 'power2.out',
      })
    }

    // Scroll-triggered animations
    ScrollTrigger.create({
      trigger: hero,
      start: 'top top',
      end: 'bottom top',
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress
        gsap.to(title, {
          y: progress * -100,
          opacity: 1 - progress * 0.5,
          duration: 0.3,
        })
        gsap.to(subtitle, {
          y: progress * -50,
          opacity: 1 - progress * 0.8,
          duration: 0.3,
        })
      },
    })

    hero.addEventListener('mousemove', handleMouseMove)

    return () => {
      hero.removeEventListener('mousemove', handleMouseMove)
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section
      id="home"
      ref={heroRef}
      className="relative h-screen flex items-center justify-center overflow-hidden bg-primary-neutral"
    >
      {/* Animated background */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 bg-gradient-to-br from-primary-peach/20 via-primary-green/10 to-primary-black/5"
      />
      
      {/* Enhanced floating elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {/* Primary floating dots */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`dot-${i}`}
            className="absolute bg-primary-peach rounded-full"
            style={{
              width: `${4 + (i % 3) * 2}px`,
              height: `${4 + (i % 3) * 2}px`,
              left: `${15 + i * 12}%`,
              top: `${25 + (i % 4) * 15}%`,
            }}
            animate={{
              y: [0, -30 - i * 5, 0],
              x: [0, Math.sin(i) * 10, 0],
              opacity: [0.2, 0.8, 0.2],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 4 + i * 0.3,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 0.2,
            }}
          />
        ))}

        {/* Secondary geometric shapes */}
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={`shape-${i}`}
            className="absolute border border-primary-green/30"
            style={{
              width: `${20 + i * 10}px`,
              height: `${20 + i * 10}px`,
              left: `${70 + i * 8}%`,
              top: `${20 + i * 20}%`,
              borderRadius: i % 2 === 0 ? '50%' : '0%',
            }}
            animate={{
              rotate: [0, 360],
              scale: [1, 1.1, 1],
              opacity: [0.1, 0.3, 0.1],
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: 'linear',
              delay: i * 0.5,
            }}
          />
        ))}

        {/* Gradient orbs */}
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={`orb-${i}`}
            className="absolute rounded-full blur-sm"
            style={{
              width: `${60 + i * 20}px`,
              height: `${60 + i * 20}px`,
              left: `${10 + i * 30}%`,
              top: `${60 + i * 10}%`,
              background: `radial-gradient(circle, ${
                i === 0 ? '#fecf8b20' : i === 1 ? '#45523e15' : '#01010110'
              }, transparent)`,
            }}
            animate={{
              y: [0, -20, 0],
              x: [0, 10, 0],
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 6 + i * 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 1,
            }}
          />
        ))}
      </div>

      {/* Main content */}
      <Container size="xl" className="relative z-10 text-center">
        <div ref={titleRef} className="mb-6">
          <Typography
            variant="h1"
            font="clash"
            weight="bold"
            className="block mb-2"
          >
            <AnimatedText text="Creative" delay={0.5} />
          </Typography>
          <Typography
            variant="h1"
            font="clash"
            weight="bold"
            className="block text-primary-green"
          >
            <AnimatedText text="Developer" delay={1.2} />
          </Typography>
        </div>

        <div ref={subtitleRef}>
          <Typography
            variant="body"
            font="inter"
            color="muted"
            className="max-w-2xl mx-auto mb-12"
          >
            Crafting award-winning digital experiences with cutting-edge technology
            and innovative design solutions.
          </Typography>
        </div>

        {/* CTA Button */}
        <Button
          variant="primary"
          size="lg"
          className="mt-4"
        >
          Explore My Work
        </Button>
      </Container>

      {/* Enhanced scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
        <motion.div
          className="w-6 h-10 border-2 border-primary-black rounded-full flex justify-center relative overflow-hidden"
          animate={{ opacity: [1, 0.5, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-primary-black rounded-full mt-2"
            animate={{ y: [0, 16, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
          />
        </motion.div>
        <motion.p
          className="text-xs text-primary-black/60 mt-2 font-medium tracking-wider"
          animate={{ opacity: [0.6, 1, 0.6] }}
          transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
        >
          SCROLL
        </motion.p>
        <motion.div
          className="w-px h-8 bg-gradient-to-b from-primary-black/20 to-transparent mt-2"
          animate={{ scaleY: [0, 1, 0] }}
          transition={{ duration: 2, repeat: Infinity, delay: 1 }}
        />
      </div>
    </section>
  )
}

export default HeroSection
