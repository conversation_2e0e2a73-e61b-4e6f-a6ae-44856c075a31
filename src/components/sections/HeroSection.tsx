'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'

gsap.registerPlugin(ScrollTrigger)

const HeroSection = () => {
  const heroRef = useRef<HTMLElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const subtitleRef = useRef<HTMLParagraphElement>(null)
  const backgroundRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const hero = heroRef.current
    const title = titleRef.current
    const subtitle = subtitleRef.current
    const background = backgroundRef.current

    if (!hero || !title || !subtitle || !background) return

    // Initial animation timeline
    const tl = gsap.timeline()

    // Set initial states
    gsap.set([title, subtitle], { opacity: 0, y: 100 })
    gsap.set(background, { scale: 1.1, opacity: 0 })

    // Animate in
    tl.to(background, {
      opacity: 0.1,
      scale: 1,
      duration: 2,
      ease: 'power2.out',
    })
    .to(title, {
      opacity: 1,
      y: 0,
      duration: 1.2,
      ease: 'power3.out',
    }, '-=1.5')
    .to(subtitle, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: 'power2.out',
    }, '-=0.8')

    // Mouse move parallax effect
    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e
      const { innerWidth, innerHeight } = window
      
      const xPos = (clientX / innerWidth - 0.5) * 2
      const yPos = (clientY / innerHeight - 0.5) * 2

      gsap.to(background, {
        x: xPos * 20,
        y: yPos * 20,
        duration: 0.5,
        ease: 'power2.out',
      })

      gsap.to(title, {
        x: xPos * 10,
        y: yPos * 10,
        duration: 0.3,
        ease: 'power2.out',
      })
    }

    // Scroll-triggered animations
    ScrollTrigger.create({
      trigger: hero,
      start: 'top top',
      end: 'bottom top',
      scrub: 1,
      onUpdate: (self) => {
        const progress = self.progress
        gsap.to(title, {
          y: progress * -100,
          opacity: 1 - progress * 0.5,
          duration: 0.3,
        })
        gsap.to(subtitle, {
          y: progress * -50,
          opacity: 1 - progress * 0.8,
          duration: 0.3,
        })
      },
    })

    hero.addEventListener('mousemove', handleMouseMove)

    return () => {
      hero.removeEventListener('mousemove', handleMouseMove)
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section
      ref={heroRef}
      className="relative h-screen flex items-center justify-center overflow-hidden bg-primary-neutral"
    >
      {/* Animated background */}
      <div
        ref={backgroundRef}
        className="absolute inset-0 bg-gradient-to-br from-primary-peach/20 via-primary-green/10 to-primary-black/5"
      />
      
      {/* Floating elements */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-primary-peach rounded-full"
            style={{
              left: `${20 + i * 15}%`,
              top: `${30 + i * 10}%`,
            }}
            animate={{
              y: [0, -20, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 3 + i * 0.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        ))}
      </div>

      {/* Main content */}
      <div className="relative z-10 text-center max-w-6xl mx-auto px-6">
        <h1
          ref={titleRef}
          className="font-clash text-6xl md:text-8xl lg:text-9xl font-bold leading-none mb-6"
        >
          <span className="block">Creative</span>
          <span className="block text-primary-green">Developer</span>
        </h1>
        
        <p
          ref={subtitleRef}
          className="font-inter text-lg md:text-xl text-primary-black/70 max-w-2xl mx-auto leading-relaxed"
        >
          Crafting award-winning digital experiences with cutting-edge technology 
          and innovative design solutions.
        </p>

        {/* CTA Button */}
        <motion.button
          className="mt-12 px-8 py-4 bg-primary-black text-primary-neutral rounded-full font-medium hover:bg-primary-green transition-colors duration-300"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          data-cursor="pointer"
        >
          Explore My Work
        </motion.button>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <motion.div
          className="w-6 h-10 border-2 border-primary-black rounded-full flex justify-center"
          animate={{ opacity: [1, 0.3, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <motion.div
            className="w-1 h-3 bg-primary-black rounded-full mt-2"
            animate={{ y: [0, 12, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </motion.div>
      </div>
    </section>
  )
}

export default HeroSection
