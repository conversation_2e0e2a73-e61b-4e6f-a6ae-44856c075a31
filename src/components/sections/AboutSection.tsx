'use client'

import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Container from '@/components/ui/Container'
import { useScrollAnimations } from '@/hooks/useScrollAnimations'
import { fetchAboutSection, fetchTimeline, getAssetUrl, type AboutSection as AboutSectionType, type TimelineItem } from '@/lib/api'

gsap.registerPlugin(ScrollTrigger)

const AboutSection = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const timelineRef = useRef<HTMLDivElement>(null)
  const { staggerAnimation, slideIn, fadeIn } = useScrollAnimations()

  const [aboutData, setAboutData] = useState<AboutSectionType | null>(null)
  const [timelineData, setTimelineData] = useState<TimelineItem[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch about and timeline data
  useEffect(() => {
    const loadAboutData = async () => {
      try {
        const [about, timeline] = await Promise.all([
          fetchAboutSection(),
          fetchTimeline()
        ])
        setAboutData(about)
        setTimelineData(timeline)
      } catch (error) {
        console.error('Failed to load about data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadAboutData()
  }, [])

  useEffect(() => {
    const section = sectionRef.current
    const timelineEl = timelineRef.current
    if (!section || !timelineEl) return

    // Animate main content
    staggerAnimation('.about-content > *', 'fadeIn', {
      trigger: section,
      start: 'top 80%',
      stagger: 0.2,
    })

    // Animate timeline items
    staggerAnimation('.timeline-item', 'slideUp', {
      trigger: timelineEl,
      start: 'top 80%',
      stagger: 0.15,
    })

    // Animate timeline line
    gsap.fromTo('.timeline-line',
      { height: '0%' },
      {
        height: '100%',
        duration: 2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: timelineEl,
          start: 'top 70%',
          end: 'bottom 30%',
          scrub: 1,
        },
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section id="about" ref={sectionRef} className="py-20 bg-white relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-0 w-64 h-64 bg-primary-peach/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-0 w-80 h-80 bg-primary-green/5 rounded-full blur-3xl" />
      </div>

      <Container size="xl" className="relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-start">
          {/* Left Column - About Content */}
          <div className="about-content">
            <Typography
              variant="h2"
              font="clash"
              weight="bold"
              className="mb-6"
            >
              About <span className="text-primary-green">Me</span>
            </Typography>

            <Typography
              variant="body"
              color="muted"
              className="mb-6 leading-relaxed"
            >
              I'm a passionate developer who creates award-winning digital experiences.
              With expertise in modern web technologies and a keen eye for design,
              I bring ideas to life through code.
            </Typography>

            <Typography
              variant="body"
              color="muted"
              className="mb-8 leading-relaxed"
            >
              My journey spans over 5 years of crafting innovative solutions for clients
              worldwide. I specialize in creating performant, accessible, and visually
              stunning web applications that push the boundaries of what's possible.
            </Typography>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 mb-8">
              {[
                { number: '50+', label: 'Projects' },
                { number: '5+', label: 'Years' },
                { number: '15+', label: 'Clients' },
              ].map((stat, index) => (
                <div key={stat.label} className="text-center">
                  <Typography
                    variant="h3"
                    font="clash"
                    weight="bold"
                    className="text-primary-peach mb-1"
                  >
                    {stat.number}
                  </Typography>
                  <Typography
                    variant="caption"
                    color="muted"
                    className="text-sm"
                  >
                    {stat.label}
                  </Typography>
                </div>
              ))}
            </div>

            {/* CTA */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <button className="px-8 py-3 bg-primary-black text-white rounded-full font-medium hover:bg-primary-green transition-colors duration-300">
                Download Resume
              </button>
            </motion.div>
          </div>

          {/* Right Column - Timeline */}
          <div ref={timelineRef} className="relative">
            <Typography
              variant="h3"
              font="clash"
              weight="bold"
              className="mb-8 text-center lg:text-left"
            >
              My <span className="text-primary-peach">Journey</span>
            </Typography>

            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-6 top-0 w-0.5 bg-primary-neutral">
                <div className="timeline-line w-full bg-primary-peach origin-top" />
              </div>

              {/* Timeline items */}
              <div className="space-y-8">
                {timeline.map((item, index) => (
                  <motion.div
                    key={item.year}
                    className="timeline-item relative pl-16"
                    whileHover={{ x: 4 }}
                    transition={{ duration: 0.2 }}
                  >
                    {/* Timeline dot */}
                    <div className="absolute left-4 top-2 w-4 h-4 bg-primary-peach rounded-full border-4 border-white shadow-lg" />

                    <div className="bg-primary-neutral/50 rounded-xl p-6 hover:bg-primary-neutral/70 transition-colors duration-300">
                      <div className="flex items-center gap-4 mb-2">
                        <Typography
                          variant="h6"
                          font="clash"
                          weight="bold"
                          className="text-primary-green"
                        >
                          {item.year}
                        </Typography>
                        <Typography
                          variant="h6"
                          font="satoshi"
                          weight="semibold"
                        >
                          {item.title}
                        </Typography>
                      </div>
                      <Typography
                        variant="caption"
                        color="muted"
                        className="leading-relaxed"
                      >
                        {item.description}
                      </Typography>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  )
}

export default AboutSection
