'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

const AboutSection = () => {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const section = sectionRef.current
    if (!section) return

    gsap.fromTo(
      section.querySelectorAll('.about-content > *'),
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: section,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  return (
    <section ref={sectionRef} className="py-20 px-6 bg-white">
      <div className="max-w-4xl mx-auto">
        <div className="about-content text-center">
          <h2 className="font-clash text-5xl md:text-6xl font-bold mb-8">
            About <span className="text-primary-green">Me</span>
          </h2>
          
          <p className="font-inter text-lg md:text-xl text-primary-black/80 leading-relaxed mb-8">
            I'm a passionate developer who creates award-winning digital experiences. 
            With expertise in modern web technologies and a keen eye for design, 
            I bring ideas to life through code.
          </p>
          
          <p className="font-inter text-base text-primary-black/70 leading-relaxed mb-12">
            My journey spans over 5 years of crafting innovative solutions for clients 
            worldwide. I specialize in creating performant, accessible, and visually 
            stunning web applications that push the boundaries of what's possible.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <h3 className="font-clash text-3xl font-bold text-primary-peach mb-2">50+</h3>
              <p className="text-primary-black/70">Projects Completed</p>
            </div>
            <div className="text-center">
              <h3 className="font-clash text-3xl font-bold text-primary-green mb-2">5+</h3>
              <p className="text-primary-black/70">Years Experience</p>
            </div>
            <div className="text-center">
              <h3 className="font-clash text-3xl font-bold text-primary-black mb-2">15+</h3>
              <p className="text-primary-black/70">Happy Clients</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutSection
