'use client'

import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'

gsap.registerPlugin(ScrollTrigger)

const ContactSection = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })

  useEffect(() => {
    const section = sectionRef.current
    if (!section) return

    gsap.fromTo(
      section.querySelectorAll('.contact-item'),
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: section,
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <section ref={sectionRef} className="py-20 px-6 bg-primary-neutral">
      <div className="max-w-4xl mx-auto">
        <h2 className="contact-item font-clash text-5xl md:text-6xl font-bold text-center mb-16">
          Let's <span className="text-primary-peach">Connect</span>
        </h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Info */}
          <div className="contact-item">
            <h3 className="font-satoshi text-2xl font-semibold mb-6">Get in Touch</h3>
            <p className="text-primary-black/70 mb-8 leading-relaxed">
              Ready to bring your next project to life? Let's discuss how we can 
              create something amazing together.
            </p>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary-peach rounded-full flex items-center justify-center">
                  <span className="text-primary-black font-semibold">@</span>
                </div>
                <div>
                  <p className="font-medium">Email</p>
                  <p className="text-primary-black/70"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary-green rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">#</span>
                </div>
                <div>
                  <p className="font-medium">Social</p>
                  <p className="text-primary-black/70">@yourhandle</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-item">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <input
                  type="text"
                  name="name"
                  placeholder="Your Name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white rounded-lg border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-colors"
                  required
                />
              </div>
              
              <div>
                <input
                  type="email"
                  name="email"
                  placeholder="Your Email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-white rounded-lg border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-colors"
                  required
                />
              </div>
              
              <div>
                <textarea
                  name="message"
                  placeholder="Your Message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={5}
                  className="w-full px-4 py-3 bg-white rounded-lg border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-colors resize-none"
                  required
                />
              </div>
              
              <motion.button
                type="submit"
                className="w-full py-3 bg-primary-black text-white rounded-lg font-medium hover:bg-primary-green transition-colors duration-300"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                data-cursor="pointer"
              >
                Send Message
              </motion.button>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ContactSection
