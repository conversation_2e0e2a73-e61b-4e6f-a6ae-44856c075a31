'use client'

import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Button from '@/components/ui/Button'
import Container from '@/components/ui/Container'
import { useScrollAnimations } from '@/hooks/useScrollAnimations'
import { fetchContactInfo, fetchSocialLinks, submitContactForm, type ContactInfo, type SocialLink } from '@/lib/api'

gsap.registerPlugin(ScrollTrigger)

const ContactSection = () => {
  const sectionRef = useRef<HTMLElement>(null)
  const formRef = useRef<HTMLFormElement>(null)
  const { staggerAnimation, slideIn } = useScrollAnimations()

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    phone: '',
    company: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [contactInfo, setContactInfo] = useState<ContactInfo | null>(null)
  const [socialLinks, setSocialLinks] = useState<SocialLink[]>([])
  const [loading, setLoading] = useState(true)

  // Fetch contact data
  useEffect(() => {
    const loadContactData = async () => {
      try {
        const [contact, social] = await Promise.all([
          fetchContactInfo(),
          fetchSocialLinks()
        ])
        setContactInfo(contact)
        setSocialLinks(social)
      } catch (error) {
        console.error('Failed to load contact data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadContactData()
  }, [])

  useEffect(() => {
    const section = sectionRef.current
    if (!section) return

    // Animate contact items
    staggerAnimation('.contact-item', 'fadeIn', {
      trigger: section,
      start: 'top 80%',
      stagger: 0.2,
    })

    // Animate form inputs
    staggerAnimation('.form-input', 'slideUp', {
      trigger: section,
      start: 'top 70%',
      stagger: 0.1,
    })

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill())
    }
  }, [staggerAnimation])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Submit to Laravel API
      await submitContactForm({
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        phone: formData.phone,
        company: formData.company
      })

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        phone: '',
        company: ''
      })
      setSubmitStatus('success')

      // Reset status after 5 seconds
      setTimeout(() => setSubmitStatus('idle'), 5000)
    } catch (error) {
      console.error('Contact form submission failed:', error)
      setSubmitStatus('error')
      setTimeout(() => setSubmitStatus('idle'), 5000)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <section id="contact" ref={sectionRef} className="py-20 bg-primary-neutral relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 right-0 w-72 h-72 bg-primary-peach/5 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 left-0 w-96 h-96 bg-primary-green/5 rounded-full blur-3xl" />
      </div>

      <Container size="xl" className="relative z-10">
        <div className="contact-item text-center mb-16">
          <Typography
            variant="h2"
            font="clash"
            weight="bold"
            className="mb-4"
          >
            Let's <span className="text-primary-peach">Connect</span>
          </Typography>
          <Typography
            variant="body"
            color="muted"
            className="max-w-2xl mx-auto"
          >
            Ready to bring your next project to life? Let's discuss how we can
            create something amazing together.
          </Typography>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Info */}
          <div className="contact-item">
            <Typography
              variant="h3"
              font="satoshi"
              weight="bold"
              className="mb-8"
            >
              Get in Touch
            </Typography>

            <div className="space-y-8 mb-12">
              {[
                {
                  icon: (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
                      <polyline points="22,6 12,13 2,6" />
                    </svg>
                  ),
                  title: 'Email',
                  value: '<EMAIL>',
                  color: 'bg-primary-peach',
                },
                {
                  icon: (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                    </svg>
                  ),
                  title: 'Phone',
                  value: '+****************',
                  color: 'bg-primary-green',
                },
                {
                  icon: (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                      <circle cx="12" cy="10" r="3" />
                    </svg>
                  ),
                  title: 'Location',
                  value: 'San Francisco, CA',
                  color: 'bg-primary-black',
                },
              ].map((contact, index) => (
                <motion.div
                  key={contact.title}
                  className="flex items-center space-x-4"
                  whileHover={{ x: 4 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className={`w-12 h-12 ${contact.color} rounded-full flex items-center justify-center text-white`}>
                    {contact.icon}
                  </div>
                  <div>
                    <Typography variant="body" weight="medium" className="mb-1">
                      {contact.title}
                    </Typography>
                    <Typography variant="caption" color="muted">
                      {contact.value}
                    </Typography>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Social Links */}
            <div>
              <Typography
                variant="h6"
                font="satoshi"
                weight="semibold"
                className="mb-4"
              >
                Follow Me
              </Typography>
              <div className="flex space-x-4">
                {[
                  { name: 'GitHub', url: '#' },
                  { name: 'LinkedIn', url: '#' },
                  { name: 'Twitter', url: '#' },
                  { name: 'Dribbble', url: '#' },
                ].map((social) => (
                  <motion.a
                    key={social.name}
                    href={social.url}
                    className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-primary-black hover:bg-primary-peach hover:text-white transition-colors duration-300"
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    data-cursor="pointer"
                  >
                    <span className="text-sm font-bold">
                      {social.name.slice(0, 2)}
                    </span>
                  </motion.a>
                ))}
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="contact-item">
            <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
              <div className="form-input">
                <motion.input
                  type="text"
                  name="name"
                  placeholder="Your Name"
                  value={formData.name}
                  onChange={handleChange}
                  className="w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 focus:shadow-lg"
                  required
                  whileFocus={{ scale: 1.02 }}
                />
              </div>

              <div className="form-input">
                <motion.input
                  type="email"
                  name="email"
                  placeholder="Your Email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 focus:shadow-lg"
                  required
                  whileFocus={{ scale: 1.02 }}
                />
              </div>

              <div className="form-input">
                <motion.textarea
                  name="message"
                  placeholder="Your Message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={5}
                  className="w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 resize-none focus:shadow-lg"
                  required
                  whileFocus={{ scale: 1.02 }}
                />
              </div>

              <div className="form-input">
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  className="w-full"
                  isLoading={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send Message'}
                </Button>
              </div>

              {/* Status Messages */}
              {submitStatus === 'success' && (
                <motion.div
                  className="p-4 bg-green-100 text-green-800 rounded-xl text-center"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  Message sent successfully! I'll get back to you soon.
                </motion.div>
              )}

              {submitStatus === 'error' && (
                <motion.div
                  className="p-4 bg-red-100 text-red-800 rounded-xl text-center"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                >
                  Something went wrong. Please try again.
                </motion.div>
              )}
            </form>
          </div>
        </div>
      </Container>
    </section>
  )
}

export default ContactSection
