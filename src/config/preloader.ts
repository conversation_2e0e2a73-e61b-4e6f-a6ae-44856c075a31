// Preloader Configuration
// Switch between different preloader styles

export const preloaderConfig = {
  // Choose preloader style: 'awwwards' | 'minimal' | 'simple' | 'disabled'
  style: 'simple' as 'awwwards' | 'minimal' | 'simple' | 'disabled',
  
  // Duration in milliseconds
  duration: 3000,
  
  // Enable/disable preloader entirely
  enabled: true,
  
  // Show preloader only on first visit (uses localStorage)
  showOnlyOnFirstVisit: false,
  
  // Custom loading messages for Awwwards style
  loadingWords: [
    'LOADING',
    'CREATING',
    'CRAFTING', 
    'BUILDING',
    'DESIGNING',
    'ANIMATING',
    'READY'
  ],
  
  // Animation settings
  animations: {
    // Logo entrance delay
    logoDelay: 0.2,
    
    // Progress bar smoothness (lower = smoother, higher = choppier)
    progressUpdateInterval: 50,
    
    // Exit animation duration
    exitDuration: 1000,
  },
  
  // Visual settings
  visual: {
    // Show floating particles
    showParticles: true,
    
    // Show animated background
    showAnimatedBackground: true,
    
    // Show geometric shapes
    showGeometricShapes: true,
    
    // Particle count
    particleCount: 20,
  }
}

export default preloaderConfig
