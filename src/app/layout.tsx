import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/providers/ThemeProvider'
import ClientLayout from '@/components/layout/ClientLayout'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Portfolio | Award-Winning Developer',
  description: 'A stunning portfolio showcasing cutting-edge web development and design',
  keywords: ['portfolio', 'web development', 'design', 'awwwards', 'creative'],
  authors: [{ name: 'Your Name' }],
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
  openGraph: {
    title: 'Portfolio | Award-Winning Developer',
    description: 'A stunning portfolio showcasing cutting-edge web development and design',
    type: 'website',
    locale: 'en_US',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
          rel="stylesheet"
        />
      </head>
      <body className={`${inter.className} antialiased bg-primary-neutral dark:bg-primary-black text-primary-black dark:text-primary-neutral transition-colors duration-300`}>
        <ThemeProvider>
          <div id="smooth-wrapper">
            <div id="smooth-content">
              <ClientLayout>
                {children}
              </ClientLayout>
            </div>
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
