@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom fonts with fallbacks */
@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/Satoshi-Variable.woff2') format('woff2');
  font-weight: 300 900;
  font-style: normal;
  font-display: swap;
  /* Fallback to Inter if <PERSON><PERSON> fails to load */
}

@font-face {
  font-family: 'Clash Display';
  src: url('/fonts/ClashDisplay-Variable.woff2') format('woff2');
  font-weight: 200 700;
  font-style: normal;
  font-display: swap;
  /* Fallback to Inter if Clash Display fails to load */
}

/* CSS Variables */
:root {
  --background: #eeedf3;
  --foreground: #010101;
  --primary-black: #010101;
  --primary-peach: #fecf8b;
  --primary-green: #45523e;
  --primary-neutral: #eeedf3;
}

.dark {
  --background: #010101;
  --foreground: #eeedf3;
  --primary-black: #eeedf3;
  --primary-peach: #fecf8b;
  --primary-green: #45523e;
  --primary-neutral: #010101;
}

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--primary-neutral);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-black);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-green);
}

/* Selection */
::selection {
  background: var(--primary-peach);
  color: var(--primary-black);
}

/* Smooth scroll container */
#smooth-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

#smooth-content {
  position: relative;
  width: 100%;
  height: auto;
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .text-pretty {
    text-wrap: pretty;
  }
  
  .perspective-1000 {
    perspective: 1000px;
  }
  
  .transform-style-3d {
    transform-style: preserve-3d;
  }
  
  .backface-hidden {
    backface-visibility: hidden;
  }
}

/* Animation classes */
.fade-in {
  opacity: 0;
  animation: fadeIn 0.6s ease-out forwards;
}

.slide-up {
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.8s ease-out forwards;
}

.scale-in {
  opacity: 0;
  transform: scale(0.9);
  animation: scaleIn 0.6s ease-out forwards;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Custom cursor */
.custom-cursor {
  cursor: none;
}

.cursor-dot {
  position: fixed;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  background: var(--primary-black);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: transform 0.1s ease;
}

.cursor-outline {
  position: fixed;
  top: 0;
  left: 0;
  width: 32px;
  height: 32px;
  border: 2px solid var(--primary-black);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: all 0.15s ease;
  opacity: 0.5;
}
