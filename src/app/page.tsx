'use client'

import { useEffect } from 'react'
import HeroSection from '@/components/sections/HeroSection'
import ProjectsSection from '@/components/sections/ProjectsSection'
import SkillsSection from '@/components/sections/SkillsSection'
import AboutSection from '@/components/sections/AboutSection'
import ContactSection from '@/components/sections/ContactSection'
import Footer from '@/components/layout/Footer'
import CustomCursor from '@/components/ui/CustomCursor'
import ScrollProgress from '@/components/ui/ScrollProgress'
import { initSmoothScroll } from '@/lib/smoothScroll'

export default function Home() {
  useEffect(() => {
    // Initialize smooth scroll
    const cleanup = initSmoothScroll()
    
    return cleanup
  }, [])

  return (
    <main className="relative">
      <CustomCursor />
      <ScrollProgress />

      <HeroSection />
      <ProjectsSection />
      <SkillsSection />
      <AboutSection />
      <ContactSection />
      <Footer />
    </main>
  )
}
