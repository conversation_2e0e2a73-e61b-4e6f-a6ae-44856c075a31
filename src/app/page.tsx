'use client'

import { useEffect, useState } from 'react'
import HeroSection from '@/components/sections/HeroSection'
import ProjectsSection from '@/components/sections/ProjectsSection'
import SkillsSection from '@/components/sections/SkillsSection'
import AboutSection from '@/components/sections/AboutSection'
import ContactSection from '@/components/sections/ContactSection'
import Footer from '@/components/layout/Footer'
import CustomCursor from '@/components/ui/CustomCursor'
import ScrollProgress from '@/components/ui/ScrollProgress'
import ClickFeedback from '@/components/ui/ClickFeedback'
import PageTransition from '@/components/ui/PageTransition'
import SmartPreloader from '@/components/ui/SmartPreloader'
import { initSmoothScroll } from '@/lib/smoothScroll'
import { usePerformance } from '@/hooks/usePerformance'

export default function Home() {
  // Performance monitoring
  const { metrics, grade } = usePerformance()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Initialize smooth scroll
    const cleanup = initSmoothScroll()

    return cleanup
  }, [])

  const handlePreloaderComplete = () => {
    setIsLoading(false)
  }

  return (
    <>
      {/* Smart Preloader */}
      <SmartPreloader onComplete={handlePreloaderComplete} />

      {/* Main Content */}
      {!isLoading && (
        <PageTransition>
          <main className="relative">
            <CustomCursor />
            <ScrollProgress />
            <ClickFeedback />

            <HeroSection />
            <ProjectsSection />
            <SkillsSection />
            <AboutSection />
            <ContactSection />
            <Footer />
          </main>
        </PageTransition>
      )}
    </>
  )
}
