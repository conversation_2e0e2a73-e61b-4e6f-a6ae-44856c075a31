import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

// Fade in animation
export const fadeIn = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(
    element,
    { opacity: 0, y: 30 },
    {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
      ...options,
    }
  )
}

// Stagger animation for multiple elements
export const staggerFadeIn = (elements: string | Element[], options: any = {}) => {
  return gsap.fromTo(
    elements,
    { opacity: 0, y: 50 },
    {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: 'power2.out',
      stagger: 0.1,
      ...options,
    }
  )
}

// Scale in animation
export const scaleIn = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(
    element,
    { opacity: 0, scale: 0.8 },
    {
      opacity: 1,
      scale: 1,
      duration: 0.6,
      ease: 'back.out(1.7)',
      ...options,
    }
  )
}

// Slide in from left
export const slideInLeft = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(
    element,
    { opacity: 0, x: -100 },
    {
      opacity: 1,
      x: 0,
      duration: 0.8,
      ease: 'power2.out',
      ...options,
    }
  )
}

// Slide in from right
export const slideInRight = (element: string | Element, options: any = {}) => {
  return gsap.fromTo(
    element,
    { opacity: 0, x: 100 },
    {
      opacity: 1,
      x: 0,
      duration: 0.8,
      ease: 'power2.out',
      ...options,
    }
  )
}

// Enhanced parallax effect with multiple options
export const parallax = (element: string | Element, options: any = {}) => {
  const {
    speed = 0.5,
    direction = 'vertical',
    start = 'top bottom',
    end = 'bottom top',
    scrub = true,
  } = options

  const animationProps: any = {
    ease: 'none',
    scrollTrigger: {
      trigger: element,
      start,
      end,
      scrub,
    },
  }

  if (direction === 'vertical') {
    animationProps.yPercent = -50 * speed
  } else if (direction === 'horizontal') {
    animationProps.xPercent = -50 * speed
  } else if (direction === 'both') {
    animationProps.yPercent = -30 * speed
    animationProps.xPercent = -20 * speed
  }

  return gsap.to(element, animationProps)
}

// Text reveal animation
export const textReveal = (element: string | Element, options: any = {}) => {
  const tl = gsap.timeline()
  
  tl.set(element, { overflow: 'hidden' })
  tl.fromTo(
    `${element} .char`,
    { y: '100%', opacity: 0 },
    {
      y: '0%',
      opacity: 1,
      duration: 0.8,
      ease: 'power2.out',
      stagger: 0.02,
      ...options,
    }
  )
  
  return tl
}

// Image zoom on scroll
export const imageZoom = (element: string | Element, options: any = {}) => {
  const { scale = 1.2, duration = 1, ease = 'power2.out' } = options

  return gsap.fromTo(element,
    { scale: 1 },
    {
      scale,
      duration,
      ease,
      scrollTrigger: {
        trigger: element,
        start: 'top 80%',
        end: 'bottom 20%',
        scrub: 1,
      },
    }
  )
}

// Reveal animation with clip-path
export const clipReveal = (element: string | Element, options: any = {}) => {
  const { direction = 'up', duration = 1.2, ease = 'power3.out' } = options

  const clipPaths = {
    up: ['inset(100% 0 0 0)', 'inset(0% 0 0 0)'],
    down: ['inset(0 0 100% 0)', 'inset(0% 0 0% 0)'],
    left: ['inset(0 100% 0 0)', 'inset(0% 0% 0% 0%)'],
    right: ['inset(0 0 0 100%)', 'inset(0% 0% 0% 0%)'],
  }

  return gsap.fromTo(element,
    { clipPath: clipPaths[direction][0] },
    {
      clipPath: clipPaths[direction][1],
      duration,
      ease,
      scrollTrigger: {
        trigger: element,
        start: 'top 80%',
        toggleActions: 'play none none reverse',
      },
    }
  )
}

// Counter animation
export const counterAnimation = (element: string | Element, options: any = {}) => {
  const { endValue = 100, duration = 2, ease = 'power2.out' } = options

  const obj = { value: 0 }

  return gsap.to(obj, {
    value: endValue,
    duration,
    ease,
    onUpdate: () => {
      const el = typeof element === 'string' ? document.querySelector(element) : element
      if (el) {
        el.textContent = Math.round(obj.value).toString()
      }
    },
    scrollTrigger: {
      trigger: element,
      start: 'top 80%',
      toggleActions: 'play none none reverse',
    },
  })
}

// Magnetic effect for buttons
export const magneticEffect = (element: HTMLElement) => {
  const handleMouseMove = (e: MouseEvent) => {
    const rect = element.getBoundingClientRect()
    const x = e.clientX - rect.left - rect.width / 2
    const y = e.clientY - rect.top - rect.height / 2

    gsap.to(element, {
      x: x * 0.3,
      y: y * 0.3,
      duration: 0.3,
      ease: 'power2.out',
    })
  }

  const handleMouseLeave = () => {
    gsap.to(element, {
      x: 0,
      y: 0,
      duration: 0.5,
      ease: 'elastic.out(1, 0.3)',
    })
  }

  element.addEventListener('mousemove', handleMouseMove)
  element.addEventListener('mouseleave', handleMouseLeave)

  return () => {
    element.removeEventListener('mousemove', handleMouseMove)
    element.removeEventListener('mouseleave', handleMouseLeave)
  }
}
