import Lenis from 'lenis'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger)

let lenis: Lenis | null = null

export function initSmoothScroll() {
  // For now, let's disable <PERSON><PERSON> and use native smooth scroll
  // This fixes the scrolling issue while maintaining scroll animations

  // Just enable native smooth scrolling
  document.documentElement.style.scrollBehavior = 'smooth'

  // Cleanup function
  return () => {
    document.documentElement.style.scrollBehavior = 'auto'
  }
}

export function scrollTo(target: string | number, options?: any) {
  if (lenis) {
    lenis.scrollTo(target, options)
  }
}

export function getLenis() {
  return lenis
}
