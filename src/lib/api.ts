// API utilities for fetching data

export interface Project {
  id: number
  title: string
  description: string
  image: string
  technologies: string[]
  category: string
  year: number
  url?: string
  github?: string
}

export interface ApiResponse<T> {
  success: boolean
  data: T
  total?: number
  message?: string
}

// Fetch projects from PHP API
export async function fetchProjects(options: {
  category?: string
  limit?: number
} = {}): Promise<Project[]> {
  try {
    const params = new URLSearchParams()
    if (options.category) params.append('category', options.category)
    if (options.limit) params.append('limit', options.limit.toString())
    
    const url = `/api/projects.php${params.toString() ? `?${params.toString()}` : ''}`
    const response = await fetch(url)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result: ApiResponse<Project[]> = await response.json()
    
    if (!result.success) {
      throw new Error(result.message || 'Failed to fetch projects')
    }
    
    return result.data
  } catch (error) {
    console.error('Error fetching projects:', error)
    // Return fallback data
    return getFallbackProjects()
  }
}

// Fallback projects data
function getFallbackProjects(): Project[] {
  return [
    {
      id: 1,
      title: 'E-commerce Platform',
      description: 'A modern e-commerce platform built with Next.js and Stripe integration.',
      image: '/images/project1.jpg',
      technologies: ['Next.js', 'React', 'Stripe', 'Tailwind CSS'],
      category: 'Web Development',
      year: 2024,
      url: 'https://example.com',
      github: 'https://github.com/username/project1'
    },
    {
      id: 2,
      title: 'Portfolio Website',
      description: 'An award-winning portfolio website with stunning animations and interactions.',
      image: '/images/project2.jpg',
      technologies: ['React', 'GSAP', 'Framer Motion', 'Three.js'],
      category: 'Creative',
      year: 2024,
      url: 'https://example.com',
      github: 'https://github.com/username/project2'
    },
    {
      id: 3,
      title: 'SaaS Dashboard',
      description: 'A comprehensive dashboard for SaaS applications with real-time data visualization.',
      image: '/images/project3.jpg',
      technologies: ['Vue.js', 'Node.js', 'MongoDB', 'Chart.js'],
      category: 'Web Application',
      year: 2023,
      url: 'https://example.com',
      github: 'https://github.com/username/project3'
    },
    {
      id: 4,
      title: 'Mobile App UI',
      description: 'Beautiful mobile app interface design with smooth animations.',
      image: '/images/project4.jpg',
      technologies: ['React Native', 'Expo', 'TypeScript'],
      category: 'Mobile',
      year: 2023,
      url: 'https://example.com',
      github: 'https://github.com/username/project4'
    },
    {
      id: 5,
      title: 'Brand Identity',
      description: 'Complete brand identity design including logo, colors, and typography.',
      image: '/images/project5.jpg',
      technologies: ['Adobe Illustrator', 'Figma', 'Photoshop'],
      category: 'Design',
      year: 2023,
      url: 'https://example.com',
      github: null
    },
    {
      id: 6,
      title: 'AI Chat Interface',
      description: 'Modern chat interface for AI-powered customer support system.',
      image: '/images/project6.jpg',
      technologies: ['React', 'Socket.io', 'OpenAI API', 'Node.js'],
      category: 'AI/ML',
      year: 2024,
      url: 'https://example.com',
      github: 'https://github.com/username/project6'
    }
  ]
}

// Get unique categories from projects
export function getProjectCategories(projects: Project[]): string[] {
  const categories = projects.map(project => project.category)
  return Array.from(new Set(categories))
}

// Filter projects by category
export function filterProjectsByCategory(projects: Project[], category: string): Project[] {
  if (category === 'All') return projects
  return projects.filter(project => project.category === category)
}

// Sort projects by year (newest first)
export function sortProjectsByYear(projects: Project[]): Project[] {
  return [...projects].sort((a, b) => b.year - a.year)
}
