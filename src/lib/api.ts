// API utilities for fetching data from Laravel Admin Panel

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'

// Types based on Laravel Admin Panel API
export interface Project {
  id: number
  title: string
  slug: string
  description: string
  short_description?: string
  featured_image: string | null
  gallery_images?: string[]
  project_video?: string | null
  project_type: 'featured' | 'normal'
  live_url?: string | null
  github_url?: string | null
  client?: string | null
  completion_date?: string | null
  technologies: Technology[]
  created_at: string
  updated_at: string
}

export interface Technology {
  id: number
  name: string
  icon?: string | null
  color?: string | null
  category?: string | null
}

export interface HeroSection {
  id: number
  title_line_1?: string | null
  title_line_2?: string | null
  subtitle?: string | null
  background_image?: string | null
  background_video?: string | null
  cta_text?: string | null
  cta_link?: string | null
  enable_animations: boolean
  animation_settings?: any
  updated_at: string
}

export interface AboutSection {
  id: number
  content: string
  profile_image?: string | null
  signature_text?: string | null
  signature_image?: string | null
  resume_file?: string | null
  stats?: any
  updated_at: string
}

export interface TimelineItem {
  id: number
  title: string
  company?: string | null
  description: string
  start_date: string
  end_date?: string | null
  is_current: boolean
  icon?: string | null
  type: 'work' | 'education' | 'achievement'
}

export interface SkillCategory {
  id: number
  name: string
  slug: string
  description?: string | null
  icon?: string | null
  color?: string | null
  skills: Skill[]
}

export interface Skill {
  id: number
  name: string
  description?: string | null
  icon?: string | null
  proficiency_level: number
  years_experience?: number | null
  is_featured: boolean
}

export interface ContactInfo {
  id: number
  email: string
  phone?: string | null
  location?: string | null
  address?: string | null
  availability_status: 'available' | 'busy' | 'unavailable'
  availability_message?: string | null
  working_hours?: any
  updated_at: string
}

export interface SocialLink {
  id: number
  platform: string
  url: string
  username?: string | null
  icon?: string | null
  color?: string | null
}

export interface SiteSettings {
  [group: string]: {
    [key: string]: any
  }
}

export interface LaravelApiResponse<T> {
  message: string
  data: T
}

// Generic API fetch function
async function apiRequest<T>(endpoint: string): Promise<T> {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result: LaravelApiResponse<T> = await response.json()
    return result.data
  } catch (error) {
    console.error(`Error fetching ${endpoint}:`, error)
    throw error
  }
}

// Hero Section API
export async function fetchHeroSection(): Promise<HeroSection | null> {
  try {
    return await apiRequest<HeroSection>('/hero')
  } catch (error) {
    console.error('Error fetching hero section:', error)
    return null
  }
}

// Projects API
export async function fetchProjects(): Promise<Project[]> {
  try {
    return await apiRequest<Project[]>('/projects')
  } catch (error) {
    console.error('Error fetching projects:', error)
    return getFallbackProjects()
  }
}

export async function fetchFeaturedProjects(): Promise<Project[]> {
  try {
    return await apiRequest<Project[]>('/projects/featured')
  } catch (error) {
    console.error('Error fetching featured projects:', error)
    return getFallbackProjects().slice(0, 3)
  }
}

export async function fetchProject(slug: string): Promise<Project | null> {
  try {
    return await apiRequest<Project>(`/projects/${slug}`)
  } catch (error) {
    console.error(`Error fetching project ${slug}:`, error)
    return null
  }
}

// About Section API
export async function fetchAboutSection(): Promise<AboutSection | null> {
  try {
    return await apiRequest<AboutSection>('/about')
  } catch (error) {
    console.error('Error fetching about section:', error)
    return null
  }
}

export async function fetchTimeline(): Promise<TimelineItem[]> {
  try {
    return await apiRequest<TimelineItem[]>('/timeline')
  } catch (error) {
    console.error('Error fetching timeline:', error)
    return []
  }
}

// Skills API
export async function fetchSkills(): Promise<SkillCategory[]> {
  try {
    return await apiRequest<SkillCategory[]>('/skills')
  } catch (error) {
    console.error('Error fetching skills:', error)
    return []
  }
}

export async function fetchSkillCategories(): Promise<SkillCategory[]> {
  try {
    return await apiRequest<SkillCategory[]>('/skills/categories')
  } catch (error) {
    console.error('Error fetching skill categories:', error)
    return []
  }
}

// Contact API
export async function fetchContactInfo(): Promise<ContactInfo | null> {
  try {
    return await apiRequest<ContactInfo>('/contact')
  } catch (error) {
    console.error('Error fetching contact info:', error)
    return null
  }
}

export async function fetchSocialLinks(): Promise<SocialLink[]> {
  try {
    return await apiRequest<SocialLink[]>('/social-links')
  } catch (error) {
    console.error('Error fetching social links:', error)
    return []
  }
}

export async function submitContactForm(data: {
  name: string
  email: string
  subject?: string
  message: string
  phone?: string
  company?: string
}): Promise<{ id: number; status: string; created_at: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(data),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    const result: LaravelApiResponse<{ id: number; status: string; created_at: string }> = await response.json()
    return result.data
  } catch (error) {
    console.error('Error submitting contact form:', error)
    throw error
  }
}

// Settings API
export async function fetchSiteSettings(): Promise<SiteSettings> {
  try {
    return await apiRequest<SiteSettings>('/settings')
  } catch (error) {
    console.error('Error fetching site settings:', error)
    return {}
  }
}

export async function fetchSettingsByGroup(group: string): Promise<{ [key: string]: any }> {
  try {
    return await apiRequest<{ [key: string]: any }>(`/settings/group/${group}`)
  } catch (error) {
    console.error(`Error fetching settings for group ${group}:`, error)
    return {}
  }
}

// Meta Tags API
export interface MetaTags {
  site_name?: string
  site_description?: string
  site_keywords?: string
  meta_author?: string
  meta_robots?: string
  meta_viewport?: string
  og_title?: string
  og_description?: string
  og_image?: string
  twitter_card?: string
  twitter_site?: string
  canonical_url?: string
  structured_data?: any
}

export async function fetchMetaTags(): Promise<MetaTags> {
  try {
    return await apiRequest<MetaTags>('/meta-tags')
  } catch (error) {
    console.error('Error fetching meta tags:', error)
    return {}
  }
}

// Analytics API
export interface AnalyticsSettings {
  google_analytics_id?: string
  google_tag_manager_id?: string
  facebook_pixel_id?: string
  hotjar_id?: string
}

export async function fetchAnalyticsSettings(): Promise<AnalyticsSettings> {
  try {
    return await apiRequest<AnalyticsSettings>('/analytics')
  } catch (error) {
    console.error('Error fetching analytics settings:', error)
    return {}
  }
}

// Fallback projects data (matching Laravel API structure)
function getFallbackProjects(): Project[] {
  return [
    {
      id: 1,
      title: 'E-commerce Platform',
      slug: 'e-commerce-platform',
      description: 'A modern e-commerce platform built with Laravel backend and React frontend, featuring secure payment processing, inventory management, and real-time analytics.',
      short_description: 'A modern e-commerce platform with Laravel backend and React frontend.',
      featured_image: '/images/project1.jpg',
      gallery_images: ['/images/project1-1.jpg', '/images/project1-2.jpg'],
      project_video: null,
      project_type: 'featured',
      live_url: 'https://example.com',
      github_url: 'https://github.com/username/project1',
      client: 'Tech Startup Inc.',
      completion_date: '2024-06-15T00:00:00.000000Z',
      technologies: [
        { id: 1, name: 'Laravel', icon: null, color: '#FF2D20', category: 'backend' },
        { id: 2, name: 'React', icon: null, color: '#61DAFB', category: 'frontend' },
        { id: 3, name: 'MySQL', icon: null, color: '#4479A1', category: 'database' },
        { id: 4, name: 'Tailwind CSS', icon: null, color: '#06B6D4', category: 'frontend' }
      ],
      created_at: '2024-01-15T00:00:00.000000Z',
      updated_at: '2024-06-15T00:00:00.000000Z'
    },
    {
      id: 2,
      title: 'Task Management System',
      slug: 'task-management-system',
      description: 'A comprehensive task management system with team collaboration features, real-time updates, and advanced project tracking capabilities.',
      short_description: 'A comprehensive task management system with team collaboration features.',
      featured_image: '/images/project2.jpg',
      gallery_images: ['/images/project2-1.jpg', '/images/project2-2.jpg'],
      project_video: null,
      project_type: 'normal',
      live_url: 'https://tasks.example.com',
      github_url: 'https://github.com/username/task-manager',
      client: 'Remote Team Solutions',
      completion_date: '2024-03-20T00:00:00.000000Z',
      technologies: [
        { id: 5, name: 'Vue.js', icon: null, color: '#4FC08D', category: 'frontend' },
        { id: 6, name: 'Node.js', icon: null, color: '#339933', category: 'backend' },
        { id: 7, name: 'MongoDB', icon: null, color: '#47A248', category: 'database' },
        { id: 8, name: 'Socket.io', icon: null, color: '#010101', category: 'realtime' }
      ],
      created_at: '2024-01-10T00:00:00.000000Z',
      updated_at: '2024-03-20T00:00:00.000000Z'
    }
  ]
}

// Utility functions for working with API data

// Get unique technology categories from projects
export function getTechnologyCategories(projects: Project[]): string[] {
  const categories = projects.flatMap(project =>
    project.technologies.map(tech => tech.category || 'other')
  )
  return Array.from(new Set(categories))
}

// Filter projects by type
export function filterProjectsByType(projects: Project[], type: 'featured' | 'normal' | 'all'): Project[] {
  if (type === 'all') return projects
  return projects.filter(project => project.project_type === type)
}

// Sort projects by completion date (newest first)
export function sortProjectsByDate(projects: Project[]): Project[] {
  return [...projects].sort((a, b) => {
    const dateA = new Date(a.completion_date || a.created_at)
    const dateB = new Date(b.completion_date || b.created_at)
    return dateB.getTime() - dateA.getTime()
  })
}

// Get all technologies from projects
export function getAllTechnologies(projects: Project[]): Technology[] {
  const allTechs = projects.flatMap(project => project.technologies)
  const uniqueTechs = allTechs.filter((tech, index, self) =>
    index === self.findIndex(t => t.id === tech.id)
  )
  return uniqueTechs
}

// Format date for display
export function formatDate(dateString: string): string {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Get asset URL (handle both relative and absolute URLs)
export function getAssetUrl(path: string | null): string | null {
  if (!path) return null
  if (path.startsWith('http')) return path
  return `${process.env.NEXT_PUBLIC_API_URL?.replace('/api/v1', '') || 'http://localhost:8000'}/storage/${path}`
}

// Check if project is featured
export function isFeaturedProject(project: Project): boolean {
  return project.project_type === 'featured'
}

// Get project completion year
export function getProjectYear(project: Project): number {
  const date = new Date(project.completion_date || project.created_at)
  return date.getFullYear()
}

// Search projects by title or description
export function searchProjects(projects: Project[], query: string): Project[] {
  const lowercaseQuery = query.toLowerCase()
  return projects.filter(project =>
    project.title.toLowerCase().includes(lowercaseQuery) ||
    project.description.toLowerCase().includes(lowercaseQuery) ||
    project.short_description?.toLowerCase().includes(lowercaseQuery) ||
    project.technologies.some(tech => tech.name.toLowerCase().includes(lowercaseQuery))
  )
}
