'use client'

import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface ScrollAnimationOptions {
  trigger?: string | Element
  start?: string
  end?: string
  scrub?: boolean | number
  pin?: boolean
  snap?: boolean | object
  onEnter?: () => void
  onLeave?: () => void
  onEnterBack?: () => void
  onLeaveBack?: () => void
}

export const useScrollAnimations = () => {
  const animationsRef = useRef<ScrollTrigger[]>([])

  // Fade in animation
  const fadeIn = (element: string | Element, options: ScrollAnimationOptions = {}) => {
    const animation = gsap.fromTo(
      element,
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: options.trigger || element,
          start: options.start || 'top 80%',
          end: options.end || 'bottom 20%',
          toggleActions: 'play none none reverse',
          ...options,
        },
      }
    )
    
    if (animation.scrollTrigger) {
      animationsRef.current.push(animation.scrollTrigger)
    }
    return animation
  }

  // Slide in from direction
  const slideIn = (
    element: string | Element, 
    direction: 'left' | 'right' | 'up' | 'down' = 'up',
    options: ScrollAnimationOptions = {}
  ) => {
    const transforms = {
      left: { x: -100, y: 0 },
      right: { x: 100, y: 0 },
      up: { x: 0, y: 100 },
      down: { x: 0, y: -100 },
    }

    const animation = gsap.fromTo(
      element,
      { 
        opacity: 0, 
        ...transforms[direction] 
      },
      {
        opacity: 1,
        x: 0,
        y: 0,
        duration: 1.2,
        ease: 'power3.out',
        scrollTrigger: {
          trigger: options.trigger || element,
          start: options.start || 'top 80%',
          toggleActions: 'play none none reverse',
          ...options,
        },
      }
    )

    if (animation.scrollTrigger) {
      animationsRef.current.push(animation.scrollTrigger)
    }
    return animation
  }

  // Scale animation
  const scaleIn = (element: string | Element, options: ScrollAnimationOptions = {}) => {
    const animation = gsap.fromTo(
      element,
      { opacity: 0, scale: 0.8 },
      {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: 'back.out(1.7)',
        scrollTrigger: {
          trigger: options.trigger || element,
          start: options.start || 'top 80%',
          toggleActions: 'play none none reverse',
          ...options,
        },
      }
    )

    if (animation.scrollTrigger) {
      animationsRef.current.push(animation.scrollTrigger)
    }
    return animation
  }

  // Stagger animation
  const staggerAnimation = (
    elements: string | Element[],
    animationType: 'fadeIn' | 'slideUp' | 'scaleIn' = 'fadeIn',
    options: ScrollAnimationOptions & { stagger?: number } = {}
  ) => {
    const { stagger = 0.1, ...scrollOptions } = options

    const animations = {
      fadeIn: { from: { opacity: 0, y: 30 }, to: { opacity: 1, y: 0 } },
      slideUp: { from: { opacity: 0, y: 50 }, to: { opacity: 1, y: 0 } },
      scaleIn: { from: { opacity: 0, scale: 0.8 }, to: { opacity: 1, scale: 1 } },
    }

    const animation = gsap.fromTo(
      elements,
      animations[animationType].from,
      {
        ...animations[animationType].to,
        duration: 0.8,
        ease: 'power2.out',
        stagger,
        scrollTrigger: {
          trigger: scrollOptions.trigger || elements,
          start: scrollOptions.start || 'top 80%',
          toggleActions: 'play none none reverse',
          ...scrollOptions,
        },
      }
    )

    if (animation.scrollTrigger) {
      animationsRef.current.push(animation.scrollTrigger)
    }
    return animation
  }

  // Parallax effect
  const parallax = (
    element: string | Element,
    speed: number = 0.5,
    options: ScrollAnimationOptions = {}
  ) => {
    const animation = gsap.to(element, {
      yPercent: -50 * speed,
      ease: 'none',
      scrollTrigger: {
        trigger: options.trigger || element,
        start: options.start || 'top bottom',
        end: options.end || 'bottom top',
        scrub: options.scrub !== undefined ? options.scrub : true,
        ...options,
      },
    })

    if (animation.scrollTrigger) {
      animationsRef.current.push(animation.scrollTrigger)
    }
    return animation
  }

  // Pin section
  const pinSection = (element: string | Element, options: ScrollAnimationOptions = {}) => {
    const trigger = ScrollTrigger.create({
      trigger: element,
      start: options.start || 'top top',
      end: options.end || 'bottom top',
      pin: true,
      pinSpacing: false,
      ...options,
    })

    animationsRef.current.push(trigger)
    return trigger
  }

  // Cleanup function
  const cleanup = () => {
    animationsRef.current.forEach(trigger => trigger.kill())
    animationsRef.current = []
  }

  useEffect(() => {
    return cleanup
  }, [])

  return {
    fadeIn,
    slideIn,
    scaleIn,
    staggerAnimation,
    parallax,
    pinSection,
    cleanup,
  }
}
