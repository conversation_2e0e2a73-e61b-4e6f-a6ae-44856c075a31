<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class SecureFileUpload
{
    /**
     * Dangerous file extensions that should never be allowed.
     */
    protected array $dangerousExtensions = [
        'php', 'php3', 'php4', 'php5', 'phtml', 'exe', 'bat', 'cmd', 'com', 'pif', 'scr', 'vbs', 'js', 'jar', 'sh', 'py', 'pl', 'cgi', 'asp', 'aspx'
    ];

    /**
     * Allowed image MIME types.
     */
    protected array $allowedImageMimes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'
    ];

    /**
     * Allowed video MIME types.
     */
    protected array $allowedVideoMimes = [
        'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov', 'video/wmv'
    ];

    /**
     * Allowed document MIME types.
     */
    protected array $allowedDocumentMimes = [
        'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];

    /**
     * Maximum file sizes in bytes.
     */
    protected array $maxFileSizes = [
        'image' => 5 * 1024 * 1024,    // 5MB
        'video' => 50 * 1024 * 1024,   // 50MB
        'document' => 10 * 1024 * 1024, // 10MB
    ];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        if ($request->hasFile('file') || $this->hasFileUploads($request)) {
            $files = $this->getAllUploadedFiles($request);
            
            foreach ($files as $file) {
                if (!$this->validateFile($file)) {
                    Log::warning('Suspicious file upload attempt', [
                        'ip' => $request->ip(),
                        'user_agent' => $request->userAgent(),
                        'filename' => $file->getClientOriginalName(),
                        'mime_type' => $file->getMimeType(),
                        'size' => $file->getSize(),
                    ]);

                    return response()->json([
                        'message' => 'File upload failed security validation.',
                        'errors' => ['file' => ['The uploaded file is not allowed.']]
                    ], 422);
                }
            }
        }

        return $next($request);
    }

    /**
     * Check if request has file uploads.
     */
    protected function hasFileUploads(Request $request): bool
    {
        foreach ($request->allFiles() as $files) {
            if (is_array($files)) {
                foreach ($files as $file) {
                    if ($file && $file->isValid()) {
                        return true;
                    }
                }
            } elseif ($files && $files->isValid()) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get all uploaded files from request.
     */
    protected function getAllUploadedFiles(Request $request): array
    {
        $allFiles = [];
        
        foreach ($request->allFiles() as $files) {
            if (is_array($files)) {
                foreach ($files as $file) {
                    if ($file && $file->isValid()) {
                        $allFiles[] = $file;
                    }
                }
            } elseif ($files && $files->isValid()) {
                $allFiles[] = $files;
            }
        }
        
        return $allFiles;
    }

    /**
     * Validate uploaded file.
     */
    protected function validateFile($file): bool
    {
        // Check if file is valid
        if (!$file || !$file->isValid()) {
            return false;
        }

        // Get file info
        $extension = strtolower($file->getClientOriginalExtension());
        $mimeType = $file->getMimeType();
        $size = $file->getSize();
        $filename = $file->getClientOriginalName();

        // Check for dangerous extensions
        if (in_array($extension, $this->dangerousExtensions)) {
            return false;
        }

        // Check for double extensions (e.g., file.jpg.php)
        if (substr_count($filename, '.') > 1) {
            $parts = explode('.', $filename);
            foreach ($parts as $part) {
                if (in_array(strtolower($part), $this->dangerousExtensions)) {
                    return false;
                }
            }
        }

        // Validate based on file type
        if ($this->isImageFile($mimeType)) {
            return $this->validateImageFile($file, $mimeType, $size);
        } elseif ($this->isVideoFile($mimeType)) {
            return $this->validateVideoFile($file, $mimeType, $size);
        } elseif ($this->isDocumentFile($mimeType)) {
            return $this->validateDocumentFile($file, $mimeType, $size);
        }

        // If we can't determine the file type, reject it
        return false;
    }

    /**
     * Check if file is an image.
     */
    protected function isImageFile(string $mimeType): bool
    {
        return in_array($mimeType, $this->allowedImageMimes);
    }

    /**
     * Check if file is a video.
     */
    protected function isVideoFile(string $mimeType): bool
    {
        return in_array($mimeType, $this->allowedVideoMimes);
    }

    /**
     * Check if file is a document.
     */
    protected function isDocumentFile(string $mimeType): bool
    {
        return in_array($mimeType, $this->allowedDocumentMimes);
    }

    /**
     * Validate image file.
     */
    protected function validateImageFile($file, string $mimeType, int $size): bool
    {
        // Check file size
        if ($size > $this->maxFileSizes['image']) {
            return false;
        }

        // Additional image validation
        try {
            $imageInfo = getimagesize($file->getPathname());
            if (!$imageInfo) {
                return false;
            }

            // Check image dimensions (optional)
            [$width, $height] = $imageInfo;
            if ($width > 5000 || $height > 5000) {
                return false; // Reject extremely large images
            }

        } catch (\Exception $e) {
            return false;
        }

        return true;
    }

    /**
     * Validate video file.
     */
    protected function validateVideoFile($file, string $mimeType, int $size): bool
    {
        // Check file size
        if ($size > $this->maxFileSizes['video']) {
            return false;
        }

        return true;
    }

    /**
     * Validate document file.
     */
    protected function validateDocumentFile($file, string $mimeType, int $size): bool
    {
        // Check file size
        if ($size > $this->maxFileSizes['document']) {
            return false;
        }

        return true;
    }
}
