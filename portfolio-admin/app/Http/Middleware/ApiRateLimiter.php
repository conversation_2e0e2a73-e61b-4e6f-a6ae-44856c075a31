<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class ApiRateLimiter
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $key = 'api'): Response
    {
        $identifier = $this->getIdentifier($request);
        
        // Different rate limits for different endpoints
        $limits = $this->getRateLimits($key, $request);
        
        foreach ($limits as $limit) {
            $rateLimiterKey = $key . ':' . $identifier . ':' . $limit['suffix'];
            
            if (RateLimiter::tooManyAttempts($rateLimiterKey, $limit['maxAttempts'])) {
                return response()->json([
                    'message' => 'Too many requests. Please try again later.',
                    'retry_after' => RateLimiter::availableIn($rateLimiterKey),
                ], 429);
            }
            
            RateLimiter::hit($rateLimiterKey, $limit['decayMinutes'] * 60);
        }

        return $next($request);
    }

    /**
     * Get the rate limiter identifier.
     */
    protected function getIdentifier(Request $request): string
    {
        if ($request->user()) {
            return 'user:' . $request->user()->id;
        }

        return 'ip:' . $request->ip();
    }

    /**
     * Get rate limits based on the key and request.
     */
    protected function getRateLimits(string $key, Request $request): array
    {
        return match ($key) {
            'contact' => [
                ['maxAttempts' => 3, 'decayMinutes' => 60, 'suffix' => 'hourly'],
                ['maxAttempts' => 10, 'decayMinutes' => 1440, 'suffix' => 'daily'],
            ],
            'auth' => [
                ['maxAttempts' => 5, 'decayMinutes' => 15, 'suffix' => 'login'],
            ],
            'api' => [
                ['maxAttempts' => 100, 'decayMinutes' => 1, 'suffix' => 'minute'],
                ['maxAttempts' => 1000, 'decayMinutes' => 60, 'suffix' => 'hourly'],
            ],
            default => [
                ['maxAttempts' => 60, 'decayMinutes' => 1, 'suffix' => 'general'],
            ],
        };
    }
}
