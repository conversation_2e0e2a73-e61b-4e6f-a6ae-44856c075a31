<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class SiteSettingController extends Controller
{
    /**
     * Get all site settings
     */
    public function index()
    {
        try {
            $settings = Cache::remember('site_settings_all', 3600, function () {
                return SiteSetting::all()->keyBy('key')->map(function ($setting) {
                    return $this->formatSettingValue($setting);
                });
            });

            return response()->json([
                'message' => 'Site settings retrieved successfully',
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve site settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get settings by group
     */
    public function getByGroup($group)
    {
        try {
            $settings = Cache::remember("site_settings_group_{$group}", 3600, function () use ($group) {
                return SiteSetting::where('group', $group)
                    ->get()
                    ->keyBy('key')
                    ->map(function ($setting) {
                        return $this->formatSettingValue($setting);
                    });
            });

            return response()->json([
                'message' => "Settings for group '{$group}' retrieved successfully",
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific setting by key
     */
    public function show($key)
    {
        try {
            $setting = Cache::remember("site_setting_{$key}", 3600, function () use ($key) {
                $setting = SiteSetting::where('key', $key)->first();
                return $setting ? $this->formatSettingValue($setting) : null;
            });

            if (!$setting) {
                return response()->json([
                    'message' => 'Setting not found',
                ], 404);
            }

            return response()->json([
                'message' => 'Setting retrieved successfully',
                'data' => $setting
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve setting',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get meta tags for SEO
     */
    public function getMetaTags()
    {
        try {
            $metaSettings = Cache::remember('meta_tags', 3600, function () {
                return SiteSetting::whereIn('key', [
                    'site_name',
                    'site_description',
                    'site_keywords',
                    'meta_author',
                    'meta_robots',
                    'meta_viewport',
                    'og_title',
                    'og_description',
                    'og_image',
                    'twitter_card',
                    'twitter_site',
                    'canonical_url',
                    'structured_data'
                ])->get()->keyBy('key')->map(function ($setting) {
                    return $this->formatSettingValue($setting);
                });
            });

            return response()->json([
                'message' => 'Meta tags retrieved successfully',
                'data' => $metaSettings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve meta tags',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get analytics settings
     */
    public function getAnalytics()
    {
        try {
            $analyticsSettings = Cache::remember('analytics_settings', 3600, function () {
                return SiteSetting::whereIn('key', [
                    'google_analytics_id',
                    'google_tag_manager_id',
                    'facebook_pixel_id',
                    'hotjar_id'
                ])->get()->keyBy('key')->map(function ($setting) {
                    return $this->formatSettingValue($setting);
                });
            });

            return response()->json([
                'message' => 'Analytics settings retrieved successfully',
                'data' => $analyticsSettings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to retrieve analytics settings',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format setting value based on type
     */
    private function formatSettingValue($setting)
    {
        $value = $setting->value;

        switch ($setting->type) {
            case 'boolean':
                return (bool) $value;
            case 'number':
                return is_numeric($value) ? (float) $value : $value;
            case 'json':
                return json_decode($value, true) ?: $value;
            case 'image':
            case 'file':
                return $value ? asset('storage/' . $value) : null;
            default:
                return $value;
        }
    }

    /**
     * Clear settings cache
     */
    public function clearCache()
    {
        try {
            Cache::forget('site_settings_all');
            Cache::forget('meta_tags');
            Cache::forget('analytics_settings');
            
            // Clear group caches
            $groups = ['general', 'seo', 'theme', 'social', 'contact', 'analytics', 'maintenance'];
            foreach ($groups as $group) {
                Cache::forget("site_settings_group_{$group}");
            }

            return response()->json([
                'message' => 'Settings cache cleared successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to clear cache',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
