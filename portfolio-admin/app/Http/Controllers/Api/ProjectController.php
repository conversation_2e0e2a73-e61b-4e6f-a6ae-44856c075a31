<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Project;
use Illuminate\Http\JsonResponse;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects.
     */
    public function index(): JsonResponse
    {
        $projects = Project::with('technologies')
            ->where('is_published', true)
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($project) {
                return [
                    'id' => $project->id,
                    'title' => $project->title,
                    'slug' => $project->slug,
                    'short_description' => $project->short_description,
                    'featured_image' => $project->featured_image ? asset('storage/' . $project->featured_image) : null,
                    'project_type' => $project->project_type,
                    'live_url' => $project->live_url,
                    'github_url' => $project->github_url,
                    'completion_date' => $project->completion_date,
                    'technologies' => $project->technologies->map(function ($tech) {
                        return [
                            'name' => $tech->name,
                            'icon' => $tech->icon,
                            'color' => $tech->color,
                            'category' => $tech->category,
                        ];
                    }),
                ];
            });

        return response()->json([
            'message' => 'Projects retrieved successfully',
            'data' => $projects
        ]);
    }

    /**
     * Display the specified project by slug.
     */
    public function show(string $slug): JsonResponse
    {
        $project = Project::with('technologies')
            ->where('slug', $slug)
            ->where('is_published', true)
            ->first();

        if (!$project) {
            return response()->json([
                'message' => 'Project not found'
            ], 404);
        }

        $projectData = [
            'id' => $project->id,
            'title' => $project->title,
            'slug' => $project->slug,
            'description' => $project->description,
            'short_description' => $project->short_description,
            'featured_image' => $project->featured_image ? asset('storage/' . $project->featured_image) : null,
            'gallery_images' => $project->gallery_images ? collect($project->gallery_images)->map(function ($image) {
                return asset('storage/' . $image);
            }) : [],
            'project_video' => $project->project_video ? asset('storage/' . $project->project_video) : null,
            'project_type' => $project->project_type,
            'live_url' => $project->live_url,
            'github_url' => $project->github_url,
            'client' => $project->client,
            'completion_date' => $project->completion_date,
            'meta_data' => $project->meta_data,
            'technologies' => $project->technologies,
            'created_at' => $project->created_at,
            'updated_at' => $project->updated_at,
        ];

        return response()->json([
            'message' => 'Project retrieved successfully',
            'data' => $projectData
        ]);
    }

    /**
     * Get featured projects only.
     */
    public function featured(): JsonResponse
    {
        $projects = Project::with('technologies')
            ->where('is_published', true)
            ->where('project_type', 'featured')
            ->orderBy('sort_order')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'message' => 'Featured projects retrieved successfully',
            'data' => $projects
        ]);
    }
}
