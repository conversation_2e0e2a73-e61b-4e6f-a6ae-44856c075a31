<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContactInfo;
use App\Models\SocialLink;
use App\Models\ContactSubmission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    /**
     * Display contact information.
     */
    public function index(): JsonResponse
    {
        $contact = ContactInfo::where('is_active', true)->first();

        if (!$contact) {
            return response()->json([
                'message' => 'No active contact information found',
                'data' => null
            ], 404);
        }

        return response()->json([
            'message' => 'Contact information retrieved successfully',
            'data' => [
                'id' => $contact->id,
                'email' => $contact->email,
                'phone' => $contact->phone,
                'location' => $contact->location,
                'address' => $contact->address,
                'availability_status' => $contact->availability_status,
                'availability_message' => $contact->availability_message,
                'working_hours' => $contact->working_hours,
                'updated_at' => $contact->updated_at,
            ]
        ]);
    }

    /**
     * Store a contact form submission.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:5000',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $submission = ContactSubmission::create([
            'name' => $request->name,
            'email' => $request->email,
            'subject' => $request->subject,
            'message' => $request->message,
            'phone' => $request->phone,
            'company' => $request->company,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return response()->json([
            'message' => 'Contact form submitted successfully',
            'data' => [
                'id' => $submission->id,
                'status' => $submission->status,
                'created_at' => $submission->created_at,
            ]
        ], 201);
    }

    /**
     * Display social links.
     */
    public function socialLinks(): JsonResponse
    {
        $socialLinks = SocialLink::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('platform')
            ->get()
            ->map(function ($link) {
                return [
                    'id' => $link->id,
                    'platform' => $link->platform,
                    'url' => $link->url,
                    'username' => $link->username,
                    'icon' => $link->icon ? asset('storage/' . $link->icon) : null,
                    'color' => $link->color,
                ];
            });

        return response()->json([
            'message' => 'Social links retrieved successfully',
            'data' => $socialLinks
        ]);
    }
}
