<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SkillCategory;
use Illuminate\Http\JsonResponse;

class SkillController extends Controller
{
    /**
     * Display skills grouped by categories.
     */
    public function index(): JsonResponse
    {
        $categories = SkillCategory::with(['skills' => function ($query) {
            $query->orderBy('sort_order')->orderBy('name');
        }])
        ->where('is_active', true)
        ->orderBy('sort_order')
        ->orderBy('name')
        ->get()
        ->map(function ($category) {
            return [
                'id' => $category->id,
                'name' => $category->name,
                'slug' => $category->slug,
                'description' => $category->description,
                'icon' => $category->icon ? asset('storage/' . $category->icon) : null,
                'color' => $category->color,
                'skills' => $category->skills->map(function ($skill) {
                    return [
                        'id' => $skill->id,
                        'name' => $skill->name,
                        'description' => $skill->description,
                        'icon' => $skill->icon ? asset('storage/' . $skill->icon) : null,
                        'proficiency_level' => $skill->proficiency_level,
                        'years_experience' => $skill->years_experience,
                        'is_featured' => $skill->is_featured,
                    ];
                }),
            ];
        });

        return response()->json([
            'message' => 'Skills retrieved successfully',
            'data' => $categories
        ]);
    }

    /**
     * Display skill categories only.
     */
    public function categories(): JsonResponse
    {
        $categories = SkillCategory::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'slug' => $category->slug,
                    'description' => $category->description,
                    'icon' => $category->icon ? asset('storage/' . $category->icon) : null,
                    'color' => $category->color,
                ];
            });

        return response()->json([
            'message' => 'Skill categories retrieved successfully',
            'data' => $categories
        ]);
    }
}
