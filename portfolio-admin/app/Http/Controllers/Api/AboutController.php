<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\AboutSection;
use App\Models\TimelineItem;
use Illuminate\Http\JsonResponse;

class AboutController extends Controller
{
    /**
     * Display the active about section.
     */
    public function index(): JsonResponse
    {
        $about = AboutSection::where('is_active', true)->first();

        if (!$about) {
            return response()->json([
                'message' => 'No active about section found',
                'data' => null
            ], 404);
        }

        return response()->json([
            'message' => 'About section retrieved successfully',
            'data' => [
                'id' => $about->id,
                'content' => $about->content,
                'profile_image' => $about->profile_image ? asset('storage/' . $about->profile_image) : null,
                'signature_text' => $about->signature_text,
                'signature_image' => $about->signature_image ? asset('storage/' . $about->signature_image) : null,
                'resume_file' => $about->resume_file ? asset('storage/' . $about->resume_file) : null,
                'stats' => $about->stats,
                'updated_at' => $about->updated_at,
            ]
        ]);
    }

    /**
     * Display timeline items.
     */
    public function timeline(): JsonResponse
    {
        $timeline = TimelineItem::orderBy('start_date', 'desc')
            ->orderBy('sort_order')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'title' => $item->title,
                    'company' => $item->company,
                    'description' => $item->description,
                    'start_date' => $item->start_date,
                    'end_date' => $item->end_date,
                    'is_current' => $item->is_current,
                    'icon' => $item->icon ? asset('storage/' . $item->icon) : null,
                    'type' => $item->type,
                ];
            });

        return response()->json([
            'message' => 'Timeline retrieved successfully',
            'data' => $timeline
        ]);
    }
}
