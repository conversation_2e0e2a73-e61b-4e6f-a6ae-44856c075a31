<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class DocumentationController extends Controller
{
    /**
     * Get API documentation.
     */
    public function index(): JsonResponse
    {
        $documentation = [
            'title' => 'Portfolio Admin API Documentation',
            'version' => '1.0.0',
            'description' => 'RESTful API for Portfolio Admin CMS built with Laravel 11',
            'base_url' => url('/api/v1'),
            'authentication' => [
                'type' => 'Laravel Sanctum',
                'description' => 'Some endpoints require authentication using Laravel Sanctum tokens',
                'header' => 'Authorization: Bearer {token}',
            ],
            'rate_limiting' => [
                'general' => '100 requests per minute',
                'contact_form' => '3 submissions per hour, 10 per day',
                'auth' => '5 attempts per 15 minutes',
            ],
            'endpoints' => $this->getEndpoints(),
        ];

        return response()->json($documentation);
    }

    /**
     * Get all API endpoints documentation.
     */
    protected function getEndpoints(): array
    {
        return [
            'hero' => [
                'GET /hero' => [
                    'description' => 'Get active hero section data',
                    'authentication' => false,
                    'parameters' => [],
                    'response' => [
                        'message' => 'Hero section retrieved successfully',
                        'data' => [
                            'id' => 1,
                            'title_line_1' => 'Hello, I\'m',
                            'title_line_2' => 'John Developer',
                            'subtitle' => 'Full-Stack Developer & UI/UX Designer...',
                            'background_image' => 'https://example.com/storage/hero/bg.jpg',
                            'background_video' => 'https://example.com/storage/hero/bg.mp4',
                            'cta_text' => 'View My Work',
                            'cta_link' => '#projects',
                            'enable_animations' => true,
                            'animation_settings' => ['fade_in_duration' => 1000],
                            'updated_at' => '2025-07-13T22:00:00.000000Z',
                        ],
                    ],
                ],
            ],
            'projects' => [
                'GET /projects' => [
                    'description' => 'Get all published projects',
                    'authentication' => false,
                    'parameters' => [],
                    'response' => [
                        'message' => 'Projects retrieved successfully',
                        'data' => [
                            [
                                'id' => 1,
                                'title' => 'E-Commerce Platform',
                                'slug' => 'e-commerce-platform',
                                'short_description' => 'A modern e-commerce platform...',
                                'featured_image' => 'https://example.com/storage/projects/featured/image.jpg',
                                'project_type' => 'featured',
                                'live_url' => 'https://demo.example.com',
                                'github_url' => 'https://github.com/user/repo',
                                'completion_date' => '2024-06-15T00:00:00.000000Z',
                                'technologies' => [
                                    ['name' => 'Laravel', 'icon' => null, 'color' => '#FF2D20', 'category' => 'backend'],
                                ],
                            ],
                        ],
                    ],
                ],
                'GET /projects/featured' => [
                    'description' => 'Get only featured projects',
                    'authentication' => false,
                    'parameters' => [],
                ],
                'GET /projects/{slug}' => [
                    'description' => 'Get single project by slug',
                    'authentication' => false,
                    'parameters' => [
                        'slug' => 'Project slug (e.g., e-commerce-platform)',
                    ],
                ],
            ],
            'about' => [
                'GET /about' => [
                    'description' => 'Get active about section data',
                    'authentication' => false,
                    'parameters' => [],
                ],
                'GET /timeline' => [
                    'description' => 'Get timeline/experience data',
                    'authentication' => false,
                    'parameters' => [],
                ],
            ],
            'skills' => [
                'GET /skills' => [
                    'description' => 'Get skills grouped by categories',
                    'authentication' => false,
                    'parameters' => [],
                ],
                'GET /skills/categories' => [
                    'description' => 'Get skill categories only',
                    'authentication' => false,
                    'parameters' => [],
                ],
            ],
            'contact' => [
                'GET /contact' => [
                    'description' => 'Get contact information',
                    'authentication' => false,
                    'parameters' => [],
                ],
                'POST /contact' => [
                    'description' => 'Submit contact form',
                    'authentication' => false,
                    'rate_limit' => '3 per hour, 10 per day',
                    'parameters' => [
                        'name' => 'required|string|max:255',
                        'email' => 'required|email|max:255',
                        'subject' => 'nullable|string|max:255',
                        'message' => 'required|string|max:5000|min:10',
                        'phone' => 'nullable|string|max:20',
                        'company' => 'nullable|string|max:255',
                    ],
                    'response' => [
                        'message' => 'Contact form submitted successfully',
                        'data' => [
                            'id' => 1,
                            'status' => 'new',
                            'created_at' => '2025-07-13T22:00:00.000000Z',
                        ],
                    ],
                ],
                'GET /social-links' => [
                    'description' => 'Get social media links',
                    'authentication' => false,
                    'parameters' => [],
                ],
            ],
            'settings' => [
                'GET /settings' => [
                    'description' => 'Get all site settings grouped by category',
                    'authentication' => false,
                    'parameters' => [],
                ],
                'GET /settings/{group}' => [
                    'description' => 'Get settings by group (general, seo, theme, etc.)',
                    'authentication' => false,
                    'parameters' => [
                        'group' => 'Setting group (general, seo, theme, analytics, maintenance)',
                    ],
                ],
            ],
        ];
    }

    /**
     * Get API status and health check.
     */
    public function status(): JsonResponse
    {
        return response()->json([
            'status' => 'operational',
            'version' => '1.0.0',
            'timestamp' => now()->toISOString(),
            'environment' => app()->environment(),
            'laravel_version' => app()->version(),
        ]);
    }
}
