<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\HeroSection;
use Illuminate\Http\JsonResponse;

class HeroController extends Controller
{
    /**
     * Display the active hero section.
     */
    public function index(): JsonResponse
    {
        $hero = HeroSection::where('is_active', true)->first();

        if (!$hero) {
            return response()->json([
                'message' => 'No active hero section found',
                'data' => null
            ], 404);
        }

        return response()->json([
            'message' => 'Hero section retrieved successfully',
            'data' => [
                'id' => $hero->id,
                'title_line_1' => $hero->title_line_1,
                'title_line_2' => $hero->title_line_2,
                'subtitle' => $hero->subtitle,
                'background_image' => $hero->background_image ? asset('storage/' . $hero->background_image) : null,
                'background_video' => $hero->background_video ? asset('storage/' . $hero->background_video) : null,
                'cta_text' => $hero->cta_text,
                'cta_link' => $hero->cta_link,
                'enable_animations' => $hero->enable_animations,
                'animation_settings' => $hero->animation_settings,
                'updated_at' => $hero->updated_at,
            ]
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $hero = HeroSection::find($id);

        if (!$hero) {
            return response()->json([
                'message' => 'Hero section not found'
            ], 404);
        }

        return response()->json([
            'message' => 'Hero section retrieved successfully',
            'data' => $hero
        ]);
    }
}
