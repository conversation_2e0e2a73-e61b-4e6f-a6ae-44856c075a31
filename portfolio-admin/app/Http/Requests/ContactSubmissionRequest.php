<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ContactSubmissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\-\'\.]+$/', // Only letters, spaces, hyphens, apostrophes, dots
            ],
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255',
                'not_regex:/[<>"\']/', // Prevent XSS characters
            ],
            'subject' => [
                'nullable',
                'string',
                'max:255',
                'not_regex:/[<>"\']/',
            ],
            'message' => [
                'required',
                'string',
                'max:5000',
                'min:10',
                'not_regex:/[<>"\']/',
            ],
            'phone' => [
                'nullable',
                'string',
                'max:20',
                'regex:/^[\+]?[0-9\s\-\(\)]+$/', // Phone number format
            ],
            'company' => [
                'nullable',
                'string',
                'max:255',
                'not_regex:/[<>"\']/',
            ],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.regex' => 'The name field contains invalid characters.',
            'email.email' => 'Please provide a valid email address.',
            'email.not_regex' => 'The email field contains invalid characters.',
            'subject.not_regex' => 'The subject field contains invalid characters.',
            'message.not_regex' => 'The message field contains invalid characters.',
            'message.min' => 'The message must be at least 10 characters long.',
            'phone.regex' => 'Please provide a valid phone number.',
            'company.not_regex' => 'The company field contains invalid characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Sanitize input data
        $this->merge([
            'name' => $this->sanitizeInput($this->name),
            'email' => $this->sanitizeEmail($this->email),
            'subject' => $this->sanitizeInput($this->subject),
            'message' => $this->sanitizeInput($this->message),
            'phone' => $this->sanitizePhone($this->phone),
            'company' => $this->sanitizeInput($this->company),
        ]);
    }

    /**
     * Sanitize general input.
     */
    protected function sanitizeInput(?string $input): ?string
    {
        if (!$input) {
            return null;
        }

        // Remove HTML tags and trim whitespace
        $sanitized = strip_tags(trim($input));

        // Remove multiple spaces
        $sanitized = preg_replace('/\s+/', ' ', $sanitized);

        return $sanitized;
    }

    /**
     * Sanitize email input.
     */
    protected function sanitizeEmail(?string $email): ?string
    {
        if (!$email) {
            return null;
        }

        return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
    }

    /**
     * Sanitize phone input.
     */
    protected function sanitizePhone(?string $phone): ?string
    {
        if (!$phone) {
            return null;
        }

        // Remove all characters except numbers, spaces, hyphens, parentheses, and plus
        return preg_replace('/[^0-9\s\-\(\)\+]/', '', trim($phone));
    }
}
