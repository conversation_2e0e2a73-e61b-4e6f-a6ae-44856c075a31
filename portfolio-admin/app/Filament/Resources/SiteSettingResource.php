<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SiteSettingResource\Pages;
use App\Filament\Resources\SiteSettingResource\RelationManagers;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SiteSettingResource extends Resource
{
    protected static ?string $model = SiteSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Site Settings';

    protected static ?string $modelLabel = 'Site Setting';

    protected static ?string $navigationGroup = 'Settings';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Setting Details')
                    ->schema([
                        Forms\Components\TextInput::make('key')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->rules(['alpha_dash'])
                            ->placeholder('e.g., site_name, theme_color'),
                        Forms\Components\Select::make('type')
                            ->options([
                                'text' => 'Text',
                                'textarea' => 'Textarea',
                                'boolean' => 'Boolean',
                                'json' => 'JSON',
                                'file' => 'File',
                                'image' => 'Image',
                                'color' => 'Color',
                                'url' => 'URL',
                                'email' => 'Email',
                                'number' => 'Number',
                            ])
                            ->required()
                            ->live(),
                        Forms\Components\Select::make('group')
                            ->options([
                                'general' => 'General',
                                'seo' => 'SEO',
                                'theme' => 'Theme',
                                'social' => 'Social',
                                'contact' => 'Contact',
                                'analytics' => 'Analytics',
                                'maintenance' => 'Maintenance',
                            ])
                            ->default('general')
                            ->required(),
                    ])->columns(3),

                Forms\Components\Section::make('Setting Value')
                    ->schema([
                        Forms\Components\TextInput::make('value')
                            ->label('Value')
                            ->visible(fn ($get) => in_array($get('type'), ['text', 'url', 'email', 'number']))
                            ->maxLength(1000),
                        Forms\Components\Textarea::make('value')
                            ->label('Value')
                            ->visible(fn ($get) => $get('type') === 'textarea')
                            ->rows(4),
                        Forms\Components\Toggle::make('value')
                            ->label('Value')
                            ->visible(fn ($get) => $get('type') === 'boolean'),
                        Forms\Components\ColorPicker::make('value')
                            ->label('Value')
                            ->visible(fn ($get) => $get('type') === 'color'),
                        Forms\Components\FileUpload::make('value')
                            ->label('Value')
                            ->visible(fn ($get) => $get('type') === 'file')
                            ->directory('settings/files')
                            ->visibility('public'),
                        Forms\Components\FileUpload::make('value')
                            ->label('Value')
                            ->visible(fn ($get) => $get('type') === 'image')
                            ->image()
                            ->directory('settings/images')
                            ->visibility('public'),
                        Forms\Components\Textarea::make('value')
                            ->label('JSON Value')
                            ->visible(fn ($get) => $get('type') === 'json')
                            ->rows(6)
                            ->placeholder('{"key": "value"}'),
                    ])->columns(1),

                Forms\Components\Section::make('Description')
                    ->schema([
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->placeholder('Describe what this setting controls')
                            ->rows(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'text' => 'gray',
                        'textarea' => 'gray',
                        'boolean' => 'success',
                        'json' => 'info',
                        'file' => 'warning',
                        'image' => 'primary',
                        'color' => 'danger',
                        'url' => 'secondary',
                        'email' => 'secondary',
                        'number' => 'gray',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('group')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'general' => 'gray',
                        'seo' => 'success',
                        'theme' => 'primary',
                        'social' => 'info',
                        'contact' => 'warning',
                        'analytics' => 'danger',
                        'maintenance' => 'secondary',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('value')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    })
                    ->formatStateUsing(function ($state, $record) {
                        return match ($record->type) {
                            'boolean' => $state ? 'Yes' : 'No',
                            'file', 'image' => $state ? basename($state) : 'No file',
                            'json' => 'JSON Data',
                            default => $state,
                        };
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 30 ? $state : null;
                    }),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'text' => 'Text',
                        'textarea' => 'Textarea',
                        'boolean' => 'Boolean',
                        'json' => 'JSON',
                        'file' => 'File',
                        'image' => 'Image',
                        'color' => 'Color',
                        'url' => 'URL',
                        'email' => 'Email',
                        'number' => 'Number',
                    ]),
                Tables\Filters\SelectFilter::make('group')
                    ->options([
                        'general' => 'General',
                        'seo' => 'SEO',
                        'theme' => 'Theme',
                        'social' => 'Social',
                        'contact' => 'Contact',
                        'analytics' => 'Analytics',
                        'maintenance' => 'Maintenance',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('group')
            ->groups([
                Tables\Grouping\Group::make('group')
                    ->label('Group')
                    ->collapsible(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSiteSettings::route('/'),
            'create' => Pages\CreateSiteSetting::route('/create'),
            'edit' => Pages\EditSiteSetting::route('/{record}/edit'),
        ];
    }
}
