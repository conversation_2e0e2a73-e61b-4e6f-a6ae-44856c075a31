<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HeroSectionResource\Pages;
use App\Filament\Resources\HeroSectionResource\RelationManagers;
use App\Models\HeroSection;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class HeroSectionResource extends Resource
{
    protected static ?string $model = HeroSection::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationLabel = 'Hero Section';

    protected static ?string $modelLabel = 'Hero Section';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Hero Content')
                    ->schema([
                        Forms\Components\TextInput::make('title_line_1')
                            ->label('Title Line 1')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('title_line_2')
                            ->label('Title Line 2')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('subtitle')
                            ->label('Subtitle')
                            ->rows(3),
                    ])->columns(1),

                Forms\Components\Section::make('Background Media')
                    ->schema([
                        Forms\Components\FileUpload::make('background_image')
                            ->label('Background Image')
                            ->image()
                            ->directory('hero/images')
                            ->visibility('public'),
                        Forms\Components\FileUpload::make('background_video')
                            ->label('Background Video')
                            ->acceptedFileTypes(['video/mp4', 'video/webm'])
                            ->directory('hero/videos')
                            ->visibility('public'),
                    ])->columns(2),

                Forms\Components\Section::make('Call to Action')
                    ->schema([
                        Forms\Components\TextInput::make('cta_text')
                            ->label('CTA Text')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('cta_link')
                            ->label('CTA Link')
                            ->url()
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Toggle::make('enable_animations')
                            ->label('Enable Animations')
                            ->default(true),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Is Active')
                            ->default(true),
                        Forms\Components\KeyValue::make('animation_settings')
                            ->label('Animation Settings')
                            ->keyLabel('Setting')
                            ->valueLabel('Value'),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title_line_1')
                    ->label('Title Line 1')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('title_line_2')
                    ->label('Title Line 2')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\ImageColumn::make('background_image')
                    ->label('Background')
                    ->size(60),
                Tables\Columns\IconColumn::make('enable_animations')
                    ->label('Animations')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Last Updated')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
                Tables\Filters\TernaryFilter::make('enable_animations')
                    ->label('Animations Enabled'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('updated_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHeroSections::route('/'),
            'create' => Pages\CreateHeroSection::route('/create'),
            'edit' => Pages\EditHeroSection::route('/{record}/edit'),
        ];
    }
}
