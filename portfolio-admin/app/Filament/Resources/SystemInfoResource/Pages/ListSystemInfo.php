<?php

namespace App\Filament\Resources\SystemInfoResource\Pages;

use App\Filament\Resources\SystemInfoResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class ListSystemInfo extends ListRecords
{
    protected static string $resource = SystemInfoResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        // Create a fake query since we don't have a real model
        return new class {
            public function get()
            {
                $systemInfo = SystemInfoResource::getSystemInfo();
                
                // Convert array to collection of objects
                $items = collect($systemInfo)->map(function ($item) {
                    return (object) $item;
                });

                return $items;
            }

            public function paginate($perPage = 15, $columns = ['*'], $pageName = 'page', $page = null)
            {
                $items = $this->get();
                $page = $page ?: Paginator::resolveCurrentPage($pageName);
                
                return new LengthAwarePaginator(
                    $items->forPage($page, $perPage),
                    $items->count(),
                    $perPage,
                    $page,
                    [
                        'path' => request()->url(),
                        'pageName' => $pageName,
                    ]
                );
            }

            public function __call($method, $parameters)
            {
                return $this;
            }
        };
    }
}
