<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LogViewerResource\Pages;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class LogViewerResource extends Resource
{
    protected static ?string $model = null;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    
    protected static ?string $navigationLabel = 'Log Viewer';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?int $navigationSort = 2;

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('level')
                    ->label('Level')
                    ->badge()
                    ->color(fn (string $state): string => match (strtolower($state)) {
                        'emergency', 'alert', 'critical', 'error' => 'danger',
                        'warning' => 'warning',
                        'notice', 'info' => 'info',
                        'debug' => 'gray',
                        default => 'primary',
                    }),
                Tables\Columns\TextColumn::make('datetime')
                    ->label('Date/Time')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('message')
                    ->label('Message')
                    ->limit(100)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 100 ? $state : null;
                    }),
                Tables\Columns\TextColumn::make('context')
                    ->label('Context')
                    ->limit(50)
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('datetime', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('level')
                    ->options([
                        'emergency' => 'Emergency',
                        'alert' => 'Alert',
                        'critical' => 'Critical',
                        'error' => 'Error',
                        'warning' => 'Warning',
                        'notice' => 'Notice',
                        'info' => 'Info',
                        'debug' => 'Debug',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalContent(fn ($record) => view('filament.log-viewer.view-log', ['record' => $record])),
            ])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLogViewer::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function getLogEntries(): array
    {
        $logFile = storage_path('logs/laravel.log');
        
        if (!File::exists($logFile)) {
            return [];
        }

        $logs = [];
        $content = File::get($logFile);
        
        // Parse Laravel log format
        $pattern = '/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\] \w+\.(\w+): (.*?)(?=\[\d{4}-\d{2}-\d{2}|\Z)/s';
        
        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach (array_reverse($matches) as $match) {
                $logs[] = [
                    'datetime' => Carbon::createFromFormat('Y-m-d H:i:s', $match[1]),
                    'level' => strtolower($match[2]),
                    'message' => trim($match[3]),
                    'context' => self::extractContext($match[3]),
                ];
            }
        }

        return array_slice($logs, 0, 1000); // Limit to last 1000 entries
    }

    private static function extractContext(string $message): string
    {
        // Extract context information from log message
        if (preg_match('/\{.*\}/', $message, $matches)) {
            return $matches[0];
        }
        
        return '';
    }
}
