<?php

namespace App\Filament\Resources\LogViewerResource\Pages;

use App\Filament\Resources\LogViewerResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class ListLogViewer extends ListRecords
{
    protected static string $resource = LogViewerResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return new class {
            public function get()
            {
                $logs = LogViewerResource::getLogEntries();
                
                return collect($logs)->map(function ($item) {
                    return (object) $item;
                });
            }

            public function paginate($perPage = 15, $columns = ['*'], $pageName = 'page', $page = null)
            {
                $items = $this->get();
                $page = $page ?: Paginator::resolveCurrentPage($pageName);
                
                return new LengthAwarePaginator(
                    $items->forPage($page, $perPage),
                    $items->count(),
                    $perPage,
                    $page,
                    [
                        'path' => request()->url(),
                        'pageName' => $pageName,
                    ]
                );
            }

            public function __call($method, $parameters)
            {
                return $this;
            }
        };
    }
}
