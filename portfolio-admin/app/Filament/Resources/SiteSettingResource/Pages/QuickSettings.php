<?php

namespace App\Filament\Resources\SiteSettingResource\Pages;

use App\Filament\Resources\SiteSettingResource;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Cache;

class QuickSettings extends Page
{
    protected static string $resource = SiteSettingResource::class;
    
    protected static string $view = 'filament.site-settings.quick-settings';
    
    protected static ?string $title = 'Quick Settings';
    
    protected static ?string $navigationLabel = 'Quick Settings';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill($this->getFormData());
    }

    protected function getFormData(): array
    {
        $settings = SiteSetting::whereIn('key', [
            'site_name',
            'site_description',
            'site_keywords',
            'google_analytics_id',
            'google_tag_manager_id',
            'facebook_pixel_id',
            'og_title',
            'og_description',
            'twitter_site',
            'canonical_url',
            'theme_primary_color',
            'theme_secondary_color',
        ])->get()->keyBy('key');

        $data = [];
        foreach ($settings as $key => $setting) {
            $data[$key] = $setting->value;
        }

        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Basic Site Information')
                    ->schema([
                        Forms\Components\TextInput::make('site_name')
                            ->label('Site Name')
                            ->placeholder('Your Portfolio')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('site_description')
                            ->label('Site Description')
                            ->placeholder('A brief description of your portfolio...')
                            ->rows(3)
                            ->maxLength(500),
                        Forms\Components\TextInput::make('site_keywords')
                            ->label('SEO Keywords')
                            ->placeholder('portfolio, web developer, designer')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('canonical_url')
                            ->label('Website URL')
                            ->placeholder('https://yourwebsite.com')
                            ->url(),
                    ])->columns(2),

                Forms\Components\Section::make('Social Media & SEO')
                    ->schema([
                        Forms\Components\TextInput::make('og_title')
                            ->label('Social Media Title')
                            ->placeholder('Title for social media sharing')
                            ->maxLength(255),
                        Forms\Components\Textarea::make('og_description')
                            ->label('Social Media Description')
                            ->placeholder('Description for social media sharing...')
                            ->rows(2)
                            ->maxLength(300),
                        Forms\Components\TextInput::make('twitter_site')
                            ->label('Twitter Username')
                            ->placeholder('@yourusername')
                            ->maxLength(50),
                    ])->columns(2),

                Forms\Components\Section::make('Analytics Tracking')
                    ->schema([
                        Forms\Components\TextInput::make('google_analytics_id')
                            ->label('Google Analytics ID')
                            ->placeholder('GA-XXXXXXXXX or G-XXXXXXXXXX')
                            ->maxLength(50),
                        Forms\Components\TextInput::make('google_tag_manager_id')
                            ->label('Google Tag Manager ID')
                            ->placeholder('GTM-XXXXXXX')
                            ->maxLength(50),
                        Forms\Components\TextInput::make('facebook_pixel_id')
                            ->label('Facebook Pixel ID')
                            ->placeholder('123456789012345')
                            ->maxLength(50),
                    ])->columns(3),

                Forms\Components\Section::make('Theme Colors')
                    ->schema([
                        Forms\Components\ColorPicker::make('theme_primary_color')
                            ->label('Primary Color')
                            ->default('#010101'),
                        Forms\Components\ColorPicker::make('theme_secondary_color')
                            ->label('Secondary Color')
                            ->default('#fecf8b'),
                    ])->columns(2),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Save Settings')
                ->action('save')
                ->color('primary'),
            Action::make('resetForm')
                ->label('Reset to Default')
                ->action('resetForm')
                ->color('gray')
                ->requiresConfirmation(),
        ];
    }

    public function save(): void
    {
        $data = $this->form->getState();

        foreach ($data as $key => $value) {
            if ($value !== null && $value !== '') {
                SiteSetting::updateOrCreate(
                    ['key' => $key],
                    [
                        'value' => $value,
                        'type' => $this->getSettingType($key),
                        'group' => $this->getSettingGroup($key),
                        'description' => $this->getSettingDescription($key),
                    ]
                );
            }
        }

        // Clear cache
        $this->clearSettingsCache();

        $this->notify('success', 'Settings saved successfully!');
    }

    public function resetForm(): void
    {
        $this->form->fill($this->getFormData());
        $this->notify('info', 'Settings reset to current values.');
    }

    protected function getSettingType(string $key): string
    {
        return match($key) {
            'theme_primary_color', 'theme_secondary_color' => 'color',
            'site_description', 'og_description' => 'textarea',
            'canonical_url' => 'url',
            default => 'text'
        };
    }

    protected function getSettingGroup(string $key): string
    {
        return match($key) {
            'site_name', 'site_description', 'site_keywords' => 'general',
            'og_title', 'og_description', 'twitter_site', 'canonical_url' => 'seo',
            'google_analytics_id', 'google_tag_manager_id', 'facebook_pixel_id' => 'analytics',
            'theme_primary_color', 'theme_secondary_color' => 'theme',
            default => 'general'
        };
    }

    protected function getSettingDescription(string $key): string
    {
        return match($key) {
            'site_name' => 'The name of your website',
            'site_description' => 'A brief description of your website for SEO',
            'site_keywords' => 'Comma-separated keywords for SEO',
            'google_analytics_id' => 'Google Analytics tracking ID',
            'google_tag_manager_id' => 'Google Tag Manager container ID',
            'facebook_pixel_id' => 'Facebook Pixel ID for tracking',
            'og_title' => 'Title for social media sharing',
            'og_description' => 'Description for social media sharing',
            'twitter_site' => 'Twitter username for the website',
            'canonical_url' => 'The canonical URL of your website',
            'theme_primary_color' => 'Primary theme color',
            'theme_secondary_color' => 'Secondary theme color',
            default => ''
        };
    }

    protected function clearSettingsCache(): void
    {
        Cache::forget('site_settings_all');
        Cache::forget('meta_tags');
        Cache::forget('analytics_settings');
        
        $groups = ['general', 'seo', 'theme', 'social', 'contact', 'analytics', 'maintenance'];
        foreach ($groups as $group) {
            Cache::forget("site_settings_group_{$group}");
        }
    }
}
