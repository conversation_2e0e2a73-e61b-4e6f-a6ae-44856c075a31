<?php

namespace App\Filament\Resources\SiteSettingResource\Pages;

use App\Filament\Resources\SiteSettingResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Cache;

class EditSiteSetting extends EditRecord
{
    protected static string $resource = SiteSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        // Clear cache after saving settings
        Cache::forget('site_settings_all');
        Cache::forget('meta_tags');
        Cache::forget('analytics_settings');

        // Clear group caches
        $groups = ['general', 'seo', 'theme', 'social', 'contact', 'analytics', 'maintenance'];
        foreach ($groups as $group) {
            Cache::forget("site_settings_group_{$group}");
        }

        // Clear individual setting cache
        if ($this->record) {
            Cache::forget("site_setting_{$this->record->key}");
        }
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Handle boolean values
        if ($data['type'] === 'boolean') {
            $data['value'] = $data['value'] ? '1' : '0';
        }

        // Handle JSON values
        if ($data['type'] === 'json' && is_array($data['value'])) {
            $data['value'] = json_encode($data['value']);
        }

        return $data;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Handle boolean values for display
        if ($data['type'] === 'boolean') {
            $data['value'] = (bool) $data['value'];
        }

        // Handle JSON values for display
        if ($data['type'] === 'json' && is_string($data['value'])) {
            $decoded = json_decode($data['value'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $data['value'] = $decoded;
            }
        }

        return $data;
    }
}
