<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SocialLinkResource\Pages;
use App\Filament\Resources\SocialLinkResource\RelationManagers;
use App\Models\SocialLink;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SocialLinkResource extends Resource
{
    protected static ?string $model = SocialLink::class;

    protected static ?string $navigationIcon = 'heroicon-o-link';

    protected static ?string $navigationLabel = 'Social Links';

    protected static ?string $modelLabel = 'Social Link';

    protected static ?string $navigationGroup = 'Contact';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Social Link Details')
                    ->schema([
                        Forms\Components\Select::make('platform')
                            ->options([
                                'linkedin' => 'LinkedIn',
                                'github' => 'GitHub',
                                'twitter' => 'Twitter',
                                'instagram' => 'Instagram',
                                'facebook' => 'Facebook',
                                'youtube' => 'YouTube',
                                'dribbble' => 'Dribbble',
                                'behance' => 'Behance',
                                'medium' => 'Medium',
                                'dev' => 'Dev.to',
                                'stackoverflow' => 'Stack Overflow',
                                'codepen' => 'CodePen',
                                'other' => 'Other',
                            ])
                            ->required()
                            ->searchable(),
                        Forms\Components\TextInput::make('url')
                            ->url()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('username')
                            ->maxLength(255)
                            ->placeholder('e.g., @johndoe'),
                    ])->columns(3),

                Forms\Components\Section::make('Appearance')
                    ->schema([
                        Forms\Components\FileUpload::make('icon')
                            ->label('Custom Icon')
                            ->image()
                            ->directory('social/icons')
                            ->visibility('public'),
                        Forms\Components\ColorPicker::make('color')
                            ->label('Brand Color')
                            ->placeholder('#1DA1F2'),
                        Forms\Components\TextInput::make('sort_order')
                            ->numeric()
                            ->default(0)
                            ->label('Display Order'),
                    ])->columns(3),

                Forms\Components\Section::make('Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('icon')
                    ->size(30)
                    ->circular(),
                Tables\Columns\TextColumn::make('platform')
                    ->searchable()
                    ->sortable()
                    ->badge(),
                Tables\Columns\TextColumn::make('username')
                    ->searchable(),
                Tables\Columns\TextColumn::make('url')
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),
                Tables\Columns\ColorColumn::make('color')
                    ->label('Color'),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Order')
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('platform')
                    ->options([
                        'linkedin' => 'LinkedIn',
                        'github' => 'GitHub',
                        'twitter' => 'Twitter',
                        'instagram' => 'Instagram',
                        'facebook' => 'Facebook',
                        'youtube' => 'YouTube',
                        'dribbble' => 'Dribbble',
                        'behance' => 'Behance',
                        'medium' => 'Medium',
                        'dev' => 'Dev.to',
                        'stackoverflow' => 'Stack Overflow',
                        'codepen' => 'CodePen',
                        'other' => 'Other',
                    ]),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->reorderable('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialLinks::route('/'),
            'create' => Pages\CreateSocialLink::route('/create'),
            'edit' => Pages\EditSocialLink::route('/{record}/edit'),
        ];
    }
}
