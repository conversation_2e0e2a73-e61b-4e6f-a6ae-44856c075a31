<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SystemInfoResource\Pages;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;

class SystemInfoResource extends Resource
{
    protected static ?string $model = null;

    protected static ?string $navigationIcon = 'heroicon-o-cpu-chip';
    
    protected static ?string $navigationLabel = 'System Info';
    
    protected static ?string $navigationGroup = 'System';
    
    protected static ?int $navigationSort = 1;

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('metric')
                    ->label('Metric'),
                Tables\Columns\TextColumn::make('value')
                    ->label('Value'),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Good' => 'success',
                        'Warning' => 'warning',
                        'Critical' => 'danger',
                        default => 'gray',
                    }),
            ])
            ->paginated(false)
            ->actions([])
            ->bulkActions([]);
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSystemInfo::route('/'),
        ];
    }

    public static function getSystemInfo(): array
    {
        $data = [];

        // PHP Information
        $data[] = [
            'metric' => 'PHP Version',
            'value' => PHP_VERSION,
            'status' => version_compare(PHP_VERSION, '8.2.0', '>=') ? 'Good' : 'Warning',
        ];

        $data[] = [
            'metric' => 'Memory Limit',
            'value' => ini_get('memory_limit'),
            'status' => 'Good',
        ];

        $data[] = [
            'metric' => 'Max Execution Time',
            'value' => ini_get('max_execution_time') . 's',
            'status' => 'Good',
        ];

        // Laravel Information
        $data[] = [
            'metric' => 'Laravel Version',
            'value' => app()->version(),
            'status' => 'Good',
        ];

        $data[] = [
            'metric' => 'Environment',
            'value' => app()->environment(),
            'status' => app()->environment() === 'production' ? 'Good' : 'Warning',
        ];

        $data[] = [
            'metric' => 'Debug Mode',
            'value' => config('app.debug') ? 'Enabled' : 'Disabled',
            'status' => config('app.debug') ? 'Warning' : 'Good',
        ];

        // Database Information
        try {
            $dbConnection = DB::connection()->getPdo();
            $data[] = [
                'metric' => 'Database Connection',
                'value' => 'Connected',
                'status' => 'Good',
            ];

            $dbSize = DB::select("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")[0]->size ?? 0;
            $data[] = [
                'metric' => 'Database Size',
                'value' => self::formatBytes($dbSize),
                'status' => $dbSize > 100 * 1024 * 1024 ? 'Warning' : 'Good', // 100MB
            ];
        } catch (\Exception $e) {
            $data[] = [
                'metric' => 'Database Connection',
                'value' => 'Failed: ' . $e->getMessage(),
                'status' => 'Critical',
            ];
        }

        // Storage Information
        $storagePath = storage_path();
        $storageSize = self::getDirectorySize($storagePath);
        $data[] = [
            'metric' => 'Storage Size',
            'value' => self::formatBytes($storageSize),
            'status' => $storageSize > 500 * 1024 * 1024 ? 'Warning' : 'Good', // 500MB
        ];

        // Cache Information
        try {
            Cache::put('system_test', 'test', 60);
            $cacheTest = Cache::get('system_test');
            $data[] = [
                'metric' => 'Cache System',
                'value' => $cacheTest === 'test' ? 'Working' : 'Failed',
                'status' => $cacheTest === 'test' ? 'Good' : 'Critical',
            ];
        } catch (\Exception $e) {
            $data[] = [
                'metric' => 'Cache System',
                'value' => 'Failed: ' . $e->getMessage(),
                'status' => 'Critical',
            ];
        }

        // Performance Metrics
        $data[] = [
            'metric' => 'Memory Usage',
            'value' => self::formatBytes(memory_get_usage(true)),
            'status' => memory_get_usage(true) > 128 * 1024 * 1024 ? 'Warning' : 'Good', // 128MB
        ];

        $data[] = [
            'metric' => 'Peak Memory',
            'value' => self::formatBytes(memory_get_peak_usage(true)),
            'status' => 'Good',
        ];

        return $data;
    }

    private static function formatBytes($bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    private static function getDirectorySize($directory): int
    {
        $size = 0;
        
        if (!is_dir($directory)) {
            return 0;
        }

        foreach (new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($directory)) as $file) {
            if ($file->isFile()) {
                $size += $file->getSize();
            }
        }

        return $size;
    }
}
