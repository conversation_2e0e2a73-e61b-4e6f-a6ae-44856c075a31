<?php

namespace App\Filament\Resources\ProjectResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TechnologiesRelationManager extends RelationManager
{
    protected static string $relationship = 'technologies';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('category')
                    ->options([
                        'frontend' => 'Frontend',
                        'backend' => 'Backend',
                        'database' => 'Database',
                        'tools' => 'Tools',
                        'mobile' => 'Mobile',
                        'devops' => 'DevOps',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('color')
                    ->label('Color (Hex)')
                    ->placeholder('#FF0000')
                    ->maxLength(7),
                Forms\Components\FileUpload::make('icon')
                    ->label('Icon')
                    ->image()
                    ->directory('technologies/icons')
                    ->visibility('public'),
            ])->columns(2);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\ImageColumn::make('icon')
                    ->size(30)
                    ->circular(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('category')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'frontend' => 'info',
                        'backend' => 'success',
                        'database' => 'warning',
                        'tools' => 'gray',
                        'mobile' => 'primary',
                        'devops' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\ColorColumn::make('color')
                    ->label('Color'),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'frontend' => 'Frontend',
                        'backend' => 'Backend',
                        'database' => 'Database',
                        'tools' => 'Tools',
                        'mobile' => 'Mobile',
                        'devops' => 'DevOps',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->reorderable('id');
    }
}
