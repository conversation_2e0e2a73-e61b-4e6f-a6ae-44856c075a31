<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AboutSection extends Model
{
    protected $fillable = [
        'content',
        'profile_image',
        'signature_text',
        'signature_image',
        'resume_file',
        'stats',
        'is_active',
    ];

    protected $casts = [
        'stats' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active about sections.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
