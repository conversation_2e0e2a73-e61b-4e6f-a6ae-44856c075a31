<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ContactInfo extends Model
{
    protected $table = 'contact_info';

    protected $fillable = [
        'email',
        'phone',
        'location',
        'address',
        'availability_status',
        'availability_message',
        'working_hours',
        'is_active',
    ];

    protected $casts = [
        'working_hours' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include active contact info.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
