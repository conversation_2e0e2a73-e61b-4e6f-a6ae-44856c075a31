<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\HeroController;
use App\Http\Controllers\Api\ProjectController;
use App\Http\Controllers\Api\AboutController;
use App\Http\Controllers\Api\SkillController;
use App\Http\Controllers\Api\ContactController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public API routes for frontend consumption
Route::prefix('v1')->group(function () {
    
    // Hero Section
    Route::get('/hero', [HeroController::class, 'index']);
    
    // Projects
    Route::get('/projects', [ProjectController::class, 'index']);
    Route::get('/projects/featured', [ProjectController::class, 'featured']);
    Route::get('/projects/{slug}', [ProjectController::class, 'show']);
    
    // About Section
    Route::get('/about', [AboutController::class, 'index']);
    Route::get('/timeline', [AboutController::class, 'timeline']);
    
    // Skills
    Route::get('/skills', [SkillController::class, 'index']);
    Route::get('/skills/categories', [SkillController::class, 'categories']);
    
    // Contact
    Route::get('/contact', [ContactController::class, 'index']);
    Route::post('/contact', [ContactController::class, 'store']);
    Route::get('/social-links', [ContactController::class, 'socialLinks']);
    
});

// Protected API routes (require authentication)
Route::middleware('auth:sanctum')->prefix('v1/admin')->group(function () {
    
    // Hero Section Management
    Route::apiResource('hero', HeroController::class)->except(['index']);
    
    // Projects Management
    Route::apiResource('projects', ProjectController::class)->except(['index', 'show']);
    
    // About Section Management
    Route::apiResource('about', AboutController::class)->except(['index']);
    
    // Skills Management
    Route::apiResource('skills', SkillController::class)->except(['index']);
    
    // Contact Management
    Route::apiResource('contact', ContactController::class)->except(['index', 'store']);
    
});
