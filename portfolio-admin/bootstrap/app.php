<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Global middleware
        $middleware->append(\App\Http\Middleware\SecurityHeaders::class);

        // API middleware
        $middleware->api(append: [
            \App\Http\Middleware\ApiRateLimiter::class . ':api',
        ]);

        // Alias middleware
        $middleware->alias([
            'rate.limit' => \App\Http\Middleware\ApiRateLimiter::class,
            'secure.upload' => \App\Http\Middleware\SecureFileUpload::class,
            'security.headers' => \App\Http\Middleware\SecurityHeaders::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
