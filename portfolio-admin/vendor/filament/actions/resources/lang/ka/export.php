<?php

return [

    'label' => 'ექსპორტი :label',

    'modal' => [

        'heading' => 'ექსპორტდება :label',

        'form' => [

            'columns' => [

                'label' => 'სვეტები',

                'form' => [

                    'is_enabled' => [
                        'label' => ':column ჩართულია',
                    ],

                    'label' => [
                        'label' => ':column დასახელება',
                    ],

                ],

            ],

        ],

        'actions' => [

            'export' => [
                'label' => 'ექსპორტი',
            ],

        ],

    ],

    'notifications' => [

        'completed' => [

            'title' => 'ექსპორტი დასრულებულია',

            'actions' => [

                'download_csv' => [
                    'label' => 'გადმოწერეთ .csv',
                ],

                'download_xlsx' => [
                    'label' => 'გადმოწერეთ .xlsx',
                ],

            ],

        ],

        'max_rows' => [
            'title' => 'ექსპორტი ზედმეტად დიდია',
            'body' => 'შეგიძლიათ მაქსიმუმ 1 ჩანაწერის ექსპორტი ერთდროულად.|შეგიძლიათ მაქსიმუმ :count ჩანაწერის ექსპორტი ერთდროულად.',
        ],

        'started' => [
            'title' => 'ექსპორტი დაწყებულია',
            'body' => 'თქვენი ექსპორტი დაიწყო და 1 ჩანაწერი დამუშავდება სერვერის მიერ. თქვენ მიიღებთ შეტყობინებას გადმოწერის ბმულთან ერთად, როდესაც ის დასრულდება.|თქვენი ექსპორტი დაიწყო და :count ჩანაწერი დამუშავდება სერვერზე. თქვენ მიიღებთ შეტყობინებას გადმოწერის ბმულთან ერთად, როდესაც ის დასრულდება.',
        ],

    ],

    'file_name' => 'ექსპორტი-:export_id-:model',

];
