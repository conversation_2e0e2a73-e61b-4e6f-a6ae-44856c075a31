<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->index(['is_published', 'sort_order']);
            $table->index(['project_type', 'is_published']);
            $table->index('slug');
        });

        Schema::table('skills', function (Blueprint $table) {
            $table->index(['skill_category_id', 'sort_order']);
            $table->index('is_featured');
        });

        Schema::table('skill_categories', function (Blueprint $table) {
            $table->index(['is_active', 'sort_order']);
            $table->index('slug');
        });

        Schema::table('social_links', function (Blueprint $table) {
            $table->index(['is_active', 'sort_order']);
            $table->index('platform');
        });

        Schema::table('contact_submissions', function (Blueprint $table) {
            $table->index('status');
            $table->index('created_at');
            $table->index(['status', 'created_at']);
        });

        Schema::table('timeline_items', function (Blueprint $table) {
            $table->index(['type', 'start_date']);
            $table->index('is_current');
        });

        Schema::table('site_settings', function (Blueprint $table) {
            $table->index('group');
            $table->index(['group', 'key']);
        });

        Schema::table('hero_sections', function (Blueprint $table) {
            $table->index('is_active');
        });

        Schema::table('about_sections', function (Blueprint $table) {
            $table->index('is_active');
        });

        Schema::table('contact_info', function (Blueprint $table) {
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropIndex(['is_published', 'sort_order']);
            $table->dropIndex(['project_type', 'is_published']);
            $table->dropIndex(['slug']);
        });

        Schema::table('skills', function (Blueprint $table) {
            $table->dropIndex(['skill_category_id', 'sort_order']);
            $table->dropIndex(['is_featured']);
        });

        Schema::table('skill_categories', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'sort_order']);
            $table->dropIndex(['slug']);
        });

        Schema::table('social_links', function (Blueprint $table) {
            $table->dropIndex(['is_active', 'sort_order']);
            $table->dropIndex(['platform']);
        });

        Schema::table('contact_submissions', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropIndex(['created_at']);
            $table->dropIndex(['status', 'created_at']);
        });

        Schema::table('timeline_items', function (Blueprint $table) {
            $table->dropIndex(['type', 'start_date']);
            $table->dropIndex(['is_current']);
        });

        Schema::table('site_settings', function (Blueprint $table) {
            $table->dropIndex(['group']);
            $table->dropIndex(['group', 'key']);
        });

        Schema::table('hero_sections', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
        });

        Schema::table('about_sections', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
        });

        Schema::table('contact_info', function (Blueprint $table) {
            $table->dropIndex(['is_active']);
        });
    }
};
