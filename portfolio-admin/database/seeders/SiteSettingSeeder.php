<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'John Developer Portfolio',
                'type' => 'text',
                'group' => 'general',
                'description' => 'The name of the website displayed in browser title and headers',
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Full-Stack Developer & UI/UX Designer',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Short tagline or subtitle for the website',
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'maintenance',
                'description' => 'Enable maintenance mode to show a coming soon page',
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'We are currently updating our website. Please check back soon!',
                'type' => 'textarea',
                'group' => 'maintenance',
                'description' => 'Message to display when maintenance mode is enabled',
            ],

            // SEO Settings
            [
                'key' => 'seo_title',
                'value' => 'John Developer - Full-Stack Developer & UI/UX Designer',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default SEO title for the website',
            ],
            [
                'key' => 'seo_description',
                'value' => 'Experienced full-stack developer specializing in modern web technologies. Creating amazing digital experiences with React, Laravel, and cutting-edge design.',
                'type' => 'textarea',
                'group' => 'seo',
                'description' => 'Default SEO meta description',
            ],
            [
                'key' => 'seo_keywords',
                'value' => 'full-stack developer, web developer, UI/UX designer, React, Laravel, JavaScript, PHP',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'SEO keywords for the website',
            ],

            // Theme Settings
            [
                'key' => 'default_theme',
                'value' => 'dark',
                'type' => 'text',
                'group' => 'theme',
                'description' => 'Default theme (light or dark)',
            ],
            [
                'key' => 'primary_color',
                'value' => '#fecf8b',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Primary brand color',
            ],
            [
                'key' => 'secondary_color',
                'value' => '#45523e',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Secondary brand color',
            ],

            // Analytics
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Google Analytics tracking ID (e.g., GA-XXXXXXXXX)',
            ],
            [
                'key' => 'google_tag_manager_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Google Tag Manager container ID',
            ],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
