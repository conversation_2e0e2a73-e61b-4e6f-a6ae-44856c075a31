<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SiteSetting;

class SiteSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Settings
            [
                'key' => 'site_name',
                'value' => 'John Developer Portfolio',
                'type' => 'text',
                'group' => 'general',
                'description' => 'The name of the website displayed in browser title and headers',
            ],
            [
                'key' => 'site_tagline',
                'value' => 'Full-Stack Developer & UI/UX Designer',
                'type' => 'text',
                'group' => 'general',
                'description' => 'Short tagline or subtitle for the website',
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'group' => 'maintenance',
                'description' => 'Enable maintenance mode to show a coming soon page',
            ],
            [
                'key' => 'maintenance_message',
                'value' => 'We are currently updating our website. Please check back soon!',
                'type' => 'textarea',
                'group' => 'maintenance',
                'description' => 'Message to display when maintenance mode is enabled',
            ],

            // SEO Settings
            [
                'key' => 'seo_title',
                'value' => 'John Developer - Full-Stack Developer & UI/UX Designer',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Default SEO title for the website',
            ],
            [
                'key' => 'seo_description',
                'value' => 'Experienced full-stack developer specializing in modern web technologies. Creating amazing digital experiences with React, Laravel, and cutting-edge design.',
                'type' => 'textarea',
                'group' => 'seo',
                'description' => 'Default SEO meta description',
            ],
            [
                'key' => 'seo_keywords',
                'value' => 'full-stack developer, web developer, UI/UX designer, React, Laravel, JavaScript, PHP',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'SEO keywords for the website',
            ],

            // Theme Settings
            [
                'key' => 'default_theme',
                'value' => 'dark',
                'type' => 'text',
                'group' => 'theme',
                'description' => 'Default theme (light or dark)',
            ],
            [
                'key' => 'primary_color',
                'value' => '#fecf8b',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Primary brand color',
            ],
            [
                'key' => 'secondary_color',
                'value' => '#45523e',
                'type' => 'color',
                'group' => 'theme',
                'description' => 'Secondary brand color',
            ],

            // SEO Meta Tags
            [
                'key' => 'meta_author',
                'value' => 'John Developer',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Website author name',
            ],
            [
                'key' => 'meta_robots',
                'value' => 'index, follow',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Robots meta tag instructions',
            ],
            [
                'key' => 'meta_viewport',
                'value' => 'width=device-width, initial-scale=1',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Viewport meta tag for responsive design',
            ],
            [
                'key' => 'og_title',
                'value' => 'John Developer - Full-Stack Developer Portfolio',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Open Graph title for social media sharing',
            ],
            [
                'key' => 'og_description',
                'value' => 'Experienced full-stack developer creating amazing digital experiences with modern technologies.',
                'type' => 'textarea',
                'group' => 'seo',
                'description' => 'Open Graph description for social media sharing',
            ],
            [
                'key' => 'og_image',
                'value' => '',
                'type' => 'image',
                'group' => 'seo',
                'description' => 'Open Graph image for social media sharing (1200x630px recommended)',
            ],
            [
                'key' => 'twitter_card',
                'value' => 'summary_large_image',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Twitter card type (summary, summary_large_image, etc.)',
            ],
            [
                'key' => 'twitter_site',
                'value' => '@johndeveloper',
                'type' => 'text',
                'group' => 'seo',
                'description' => 'Twitter username for the website',
            ],
            [
                'key' => 'canonical_url',
                'value' => 'https://johndeveloper.com',
                'type' => 'url',
                'group' => 'seo',
                'description' => 'Canonical URL for the website',
            ],
            [
                'key' => 'structured_data',
                'value' => '{"@context":"https://schema.org","@type":"Person","name":"John Developer","jobTitle":"Full-Stack Developer","url":"https://johndeveloper.com"}',
                'type' => 'json',
                'group' => 'seo',
                'description' => 'JSON-LD structured data for search engines',
            ],

            // Analytics
            [
                'key' => 'google_analytics_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Google Analytics tracking ID (e.g., GA-XXXXXXXXX)',
            ],
            [
                'key' => 'google_tag_manager_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Google Tag Manager container ID',
            ],
            [
                'key' => 'facebook_pixel_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Facebook Pixel ID for tracking',
            ],
            [
                'key' => 'hotjar_id',
                'value' => '',
                'type' => 'text',
                'group' => 'analytics',
                'description' => 'Hotjar tracking ID for user behavior analytics',
            ],

            // Performance
            [
                'key' => 'enable_caching',
                'value' => '1',
                'type' => 'boolean',
                'group' => 'general',
                'description' => 'Enable API response caching for better performance',
            ],
            [
                'key' => 'cache_duration',
                'value' => '3600',
                'type' => 'number',
                'group' => 'general',
                'description' => 'Cache duration in seconds (3600 = 1 hour)',
            ],
        ];

        foreach ($settings as $setting) {
            SiteSetting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
