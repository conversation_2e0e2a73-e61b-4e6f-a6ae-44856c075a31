<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SkillCategory;

class SkillCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Frontend Development',
                'slug' => 'frontend-development',
                'description' => 'Client-side technologies and frameworks for building user interfaces',
                'color' => '#61DAFB',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'Backend Development',
                'slug' => 'backend-development',
                'description' => 'Server-side technologies and database management',
                'color' => '#339933',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'Animation & Graphics',
                'slug' => 'animation-graphics',
                'description' => 'Animation libraries and graphics programming',
                'color' => '#88CE02',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'name' => 'Tools & DevOps',
                'slug' => 'tools-devops',
                'description' => 'Development tools, deployment, and infrastructure',
                'color' => '#FF6B35',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'name' => 'Design & UI/UX',
                'slug' => 'design-uiux',
                'description' => 'Design tools and user experience methodologies',
                'color' => '#FF0080',
                'sort_order' => 5,
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            SkillCategory::updateOrCreate(
                ['slug' => $category['slug']],
                $category
            );
        }
    }
}
