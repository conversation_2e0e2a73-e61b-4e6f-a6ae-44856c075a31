<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Portfolio Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        // Create a demo user for testing
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'password' => Hash::make('demo123'),
                'email_verified_at' => now(),
            ]
        );
    }
}
