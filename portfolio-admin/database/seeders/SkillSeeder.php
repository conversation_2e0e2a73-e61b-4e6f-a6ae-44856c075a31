<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Skill;
use App\Models\SkillCategory;

class SkillSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get categories
        $frontend = SkillCategory::where('slug', 'frontend-development')->first();
        $backend = SkillCategory::where('slug', 'backend-development')->first();
        $animation = SkillCategory::where('slug', 'animation-graphics')->first();
        $tools = SkillCategory::where('slug', 'tools-devops')->first();
        $design = SkillCategory::where('slug', 'design-uiux')->first();

        $skills = [
            // Frontend Development
            [
                'skill_category_id' => $frontend?->id,
                'name' => 'React',
                'description' => 'Modern JavaScript library for building user interfaces',
                'proficiency_level' => 95,
                'years_experience' => 5,
                'is_featured' => true,
                'sort_order' => 1,
            ],
            [
                'skill_category_id' => $frontend?->id,
                'name' => 'Next.js',
                'description' => 'React framework for production applications',
                'proficiency_level' => 90,
                'years_experience' => 3,
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'skill_category_id' => $frontend?->id,
                'name' => 'TypeScript',
                'description' => 'Typed superset of JavaScript',
                'proficiency_level' => 88,
                'years_experience' => 4,
                'is_featured' => true,
                'sort_order' => 3,
            ],
            [
                'skill_category_id' => $frontend?->id,
                'name' => 'Tailwind CSS',
                'description' => 'Utility-first CSS framework',
                'proficiency_level' => 92,
                'years_experience' => 3,
                'is_featured' => false,
                'sort_order' => 4,
            ],
            [
                'skill_category_id' => $frontend?->id,
                'name' => 'Vue.js',
                'description' => 'Progressive JavaScript framework',
                'proficiency_level' => 80,
                'years_experience' => 2,
                'is_featured' => false,
                'sort_order' => 5,
            ],

            // Backend Development
            [
                'skill_category_id' => $backend?->id,
                'name' => 'Laravel',
                'description' => 'PHP framework for web artisans',
                'proficiency_level' => 90,
                'years_experience' => 4,
                'is_featured' => true,
                'sort_order' => 1,
            ],
            [
                'skill_category_id' => $backend?->id,
                'name' => 'Node.js',
                'description' => 'JavaScript runtime for server-side development',
                'proficiency_level' => 85,
                'years_experience' => 3,
                'is_featured' => true,
                'sort_order' => 2,
            ],
            [
                'skill_category_id' => $backend?->id,
                'name' => 'PHP',
                'description' => 'Server-side scripting language',
                'proficiency_level' => 88,
                'years_experience' => 5,
                'is_featured' => false,
                'sort_order' => 3,
            ],
            [
                'skill_category_id' => $backend?->id,
                'name' => 'MySQL',
                'description' => 'Relational database management system',
                'proficiency_level' => 82,
                'years_experience' => 4,
                'is_featured' => false,
                'sort_order' => 4,
            ],
            [
                'skill_category_id' => $backend?->id,
                'name' => 'Python',
                'description' => 'High-level programming language',
                'proficiency_level' => 75,
                'years_experience' => 2,
                'is_featured' => false,
                'sort_order' => 5,
            ],

            // Animation & Graphics
            [
                'skill_category_id' => $animation?->id,
                'name' => 'GSAP',
                'description' => 'Professional-grade animation library',
                'proficiency_level' => 85,
                'years_experience' => 3,
                'is_featured' => true,
                'sort_order' => 1,
            ],
            [
                'skill_category_id' => $animation?->id,
                'name' => 'Framer Motion',
                'description' => 'Motion library for React',
                'proficiency_level' => 80,
                'years_experience' => 2,
                'is_featured' => false,
                'sort_order' => 2,
            ],
            [
                'skill_category_id' => $animation?->id,
                'name' => 'Three.js',
                'description' => '3D graphics library for the web',
                'proficiency_level' => 70,
                'years_experience' => 1,
                'is_featured' => false,
                'sort_order' => 3,
            ],

            // Tools & DevOps
            [
                'skill_category_id' => $tools?->id,
                'name' => 'Git',
                'description' => 'Version control system',
                'proficiency_level' => 90,
                'years_experience' => 5,
                'is_featured' => false,
                'sort_order' => 1,
            ],
            [
                'skill_category_id' => $tools?->id,
                'name' => 'Docker',
                'description' => 'Containerization platform',
                'proficiency_level' => 75,
                'years_experience' => 2,
                'is_featured' => false,
                'sort_order' => 2,
            ],
            [
                'skill_category_id' => $tools?->id,
                'name' => 'AWS',
                'description' => 'Cloud computing services',
                'proficiency_level' => 70,
                'years_experience' => 2,
                'is_featured' => false,
                'sort_order' => 3,
            ],

            // Design & UI/UX
            [
                'skill_category_id' => $design?->id,
                'name' => 'Figma',
                'description' => 'Collaborative design tool',
                'proficiency_level' => 85,
                'years_experience' => 3,
                'is_featured' => false,
                'sort_order' => 1,
            ],
            [
                'skill_category_id' => $design?->id,
                'name' => 'Adobe Creative Suite',
                'description' => 'Professional design software suite',
                'proficiency_level' => 80,
                'years_experience' => 4,
                'is_featured' => false,
                'sort_order' => 2,
            ],
        ];

        foreach ($skills as $skill) {
            if ($skill['skill_category_id']) {
                Skill::updateOrCreate(
                    [
                        'name' => $skill['name'],
                        'skill_category_id' => $skill['skill_category_id']
                    ],
                    $skill
                );
            }
        }
    }
}
