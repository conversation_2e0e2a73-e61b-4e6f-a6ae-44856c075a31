<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Project;
use App\Models\ProjectTechnology;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample projects
        $project1 = Project::create([
            'title' => 'E-Commerce Platform',
            'slug' => 'e-commerce-platform',
            'description' => '<p>A modern, responsive e-commerce platform built with Laravel and React. Features include user authentication, product catalog, shopping cart, payment integration, and admin dashboard.</p><p>Key features:</p><ul><li>User registration and authentication</li><li>Product catalog with search and filtering</li><li>Shopping cart and checkout process</li><li>Payment integration with Stripe</li><li>Admin dashboard for managing products and orders</li><li>Responsive design for mobile and desktop</li></ul>',
            'short_description' => 'A modern e-commerce platform with <PERSON><PERSON> backend and React frontend.',
            'project_type' => 'featured',
            'live_url' => 'https://demo-ecommerce.example.com',
            'github_url' => 'https://github.com/johndoe/ecommerce-platform',
            'client' => 'TechCorp Inc.',
            'completion_date' => '2024-06-15',
            'sort_order' => 1,
            'is_published' => true,
            'meta_data' => [
                'duration' => '3 months',
                'team_size' => '4 developers',
                'budget' => '$50,000',
            ],
        ]);

        // Add technologies for project 1
        $technologies1 = [
            ['name' => 'Laravel', 'category' => 'backend', 'color' => '#FF2D20'],
            ['name' => 'React', 'category' => 'frontend', 'color' => '#61DAFB'],
            ['name' => 'MySQL', 'category' => 'database', 'color' => '#4479A1'],
            ['name' => 'Tailwind CSS', 'category' => 'frontend', 'color' => '#06B6D4'],
            ['name' => 'Stripe', 'category' => 'tools', 'color' => '#635BFF'],
        ];

        foreach ($technologies1 as $tech) {
            ProjectTechnology::create([
                'project_id' => $project1->id,
                'name' => $tech['name'],
                'category' => $tech['category'],
                'color' => $tech['color'],
            ]);
        }

        $project2 = Project::create([
            'title' => 'Task Management App',
            'slug' => 'task-management-app',
            'description' => '<p>A collaborative task management application built with Next.js and Node.js. Perfect for teams to organize projects, assign tasks, and track progress.</p><p>Features include:</p><ul><li>Project and task creation</li><li>Team collaboration tools</li><li>Real-time notifications</li><li>File attachments</li><li>Progress tracking and reporting</li></ul>',
            'short_description' => 'Collaborative task management app for teams.',
            'project_type' => 'normal',
            'live_url' => 'https://taskapp.example.com',
            'github_url' => 'https://github.com/johndoe/task-manager',
            'completion_date' => '2024-04-20',
            'sort_order' => 2,
            'is_published' => true,
        ]);

        // Add technologies for project 2
        $technologies2 = [
            ['name' => 'Next.js', 'category' => 'frontend', 'color' => '#000000'],
            ['name' => 'Node.js', 'category' => 'backend', 'color' => '#339933'],
            ['name' => 'MongoDB', 'category' => 'database', 'color' => '#47A248'],
            ['name' => 'Socket.io', 'category' => 'tools', 'color' => '#010101'],
        ];

        foreach ($technologies2 as $tech) {
            ProjectTechnology::create([
                'project_id' => $project2->id,
                'name' => $tech['name'],
                'category' => $tech['category'],
                'color' => $tech['color'],
            ]);
        }
    }
}
