<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ContactInfo;

class ContactInfoSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        ContactInfo::create([
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'location' => 'San Francisco, CA',
            'address' => '123 Tech Street, San Francisco, CA 94105',
            'availability_status' => 'available',
            'availability_message' => 'Currently accepting new projects and collaborations. Let\'s build something amazing together!',
            'working_hours' => [
                'Monday' => '9:00 AM - 6:00 PM PST',
                'Tuesday' => '9:00 AM - 6:00 PM PST',
                'Wednesday' => '9:00 AM - 6:00 PM PST',
                'Thursday' => '9:00 AM - 6:00 PM PST',
                'Friday' => '9:00 AM - 5:00 PM PST',
                'Saturday' => 'Available for urgent projects',
                'Sunday' => 'Rest day',
            ],
            'is_active' => true,
        ]);
    }
}
