<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SocialLink;

class SocialLinkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $socialLinks = [
            [
                'platform' => 'github',
                'url' => 'https://github.com/johndoe',
                'username' => '@johndoe',
                'color' => '#181717',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'platform' => 'linkedin',
                'url' => 'https://linkedin.com/in/johndoe',
                'username' => 'johndoe',
                'color' => '#0A66C2',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'platform' => 'twitter',
                'url' => 'https://twitter.com/johndoe',
                'username' => '@johndoe',
                'color' => '#1DA1F2',
                'sort_order' => 3,
                'is_active' => true,
            ],
            [
                'platform' => 'dribbble',
                'url' => 'https://dribbble.com/johndoe',
                'username' => 'johndoe',
                'color' => '#EA4C89',
                'sort_order' => 4,
                'is_active' => true,
            ],
            [
                'platform' => 'behance',
                'url' => 'https://behance.net/johndoe',
                'username' => 'johndoe',
                'color' => '#1769FF',
                'sort_order' => 5,
                'is_active' => true,
            ],
            [
                'platform' => 'medium',
                'url' => 'https://medium.com/@johndoe',
                'username' => '@johndoe',
                'color' => '#000000',
                'sort_order' => 6,
                'is_active' => false,
            ],
        ];

        foreach ($socialLinks as $link) {
            SocialLink::create($link);
        }
    }
}
