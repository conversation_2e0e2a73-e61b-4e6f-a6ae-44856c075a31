[2025-07-13 21:55:43] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from "contact_infos") (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from \"contact_infos\") (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(40): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#76 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from \"contact_infos\") (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Database\\QueryException), 3)
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(40): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(Illuminate\\Database\\QueryException), 3)
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/support/src/Components/ViewComponent.php(115): Illuminate\\View\\View->render()
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Support/helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/b7f44ef6930d816767392140fbb1c156.php(42): e(Object(Filament\\Tables\\Table))
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#77 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#79 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#81 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#85 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#86 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from \"contact_infos\") at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3310): Illuminate\\Database\\Query\\Builder->get()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3269): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Table/Concerns/HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/2b1ac5dd50010ea575832b7a7eb962cc.php(66): Filament\\Tables\\Table->getRecords()
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/support/src/Components/ViewComponent.php(115): Illuminate\\View\\View->render()
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Support/helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/b7f44ef6930d816767392140fbb1c156.php(42): e(Object(Filament\\Tables\\Table))
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#78 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#81 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#83 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#85 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#87 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#89 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#91 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#93 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#95 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#96 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#97 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#98 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: contact_infos at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select count(*)...')
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select count(*)...', Array)
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3310): Illuminate\\Database\\Query\\Builder->get()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3269): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Table/Concerns/HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/2b1ac5dd50010ea575832b7a7eb962cc.php(66): Filament\\Tables\\Table->getRecords()
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/support/src/Components/ViewComponent.php(115): Illuminate\\View\\View->render()
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Support/helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/b7f44ef6930d816767392140fbb1c156.php(42): e(Object(Filament\\Tables\\Table))
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#77 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#80 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#83 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#85 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#87 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#89 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#91 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#93 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#95 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#96 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#97 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#98 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#99 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#100 {main}
"} 
[2025-07-13 21:56:21] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: social_links.platform (Connection: sqlite, SQL: insert into "social_links" ("updated_at", "created_at") values (2025-07-13 21:56:21, 2025-07-13 21:56:21)) {"userId":1,"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: social_links.platform (Connection: sqlite, SQL: insert into \"social_links\" (\"updated_at\", \"created_at\") values (2025-07-13 21:56:21, 2025-07-13 21:56:21)) at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"so...', Array, Object(Closure))
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"so...', Array, Object(Closure))
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"so...', Array)
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"so...', Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"so...', Array, 'id')
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Resources/Pages/CreateRecord.php(175): Illuminate\\Database\\Eloquent\\Model->save()
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Resources/Pages/CreateRecord.php(92): Filament\\Resources\\Pages\\CreateRecord->handleRecordCreation(Array)
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Filament\\Resources\\Pages\\CreateRecord->create(false)
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(474): Livewire\\Wrapped->__call('create', Array)
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\SocialLinkResource\\Pages\\CreateSocialLink), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#69 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: social_links.platform at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:568)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(568): PDOStatement->execute()
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}('insert into \"so...', Array)
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"so...', Array, Object(Closure))
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"so...', Array, Object(Closure))
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"so...', Array)
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Processors/Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"so...', Array)
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"so...', Array, 'id')
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Resources/Pages/CreateRecord.php(175): Illuminate\\Database\\Eloquent\\Model->save()
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Resources/Pages/CreateRecord.php(92): Filament\\Resources\\Pages\\CreateRecord->handleRecordCreation(Array)
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Filament\\Resources\\Pages\\CreateRecord->create(false)
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(474): Livewire\\Wrapped->__call('create', Array)
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(101): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->callMethods(Object(App\\Filament\\Resources\\SocialLinkResource\\Pages\\CreateSocialLink), Array, Object(Livewire\\Mechanisms\\HandleComponents\\ComponentContext))
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(102): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->update(Array, Array, Array)
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php(94): Livewire\\LivewireManager->update(Array, Array, Array)
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Mechanisms\\HandleRequests\\HandleRequests->handleUpdate()
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Livewire\\Mechanisms\\HandleRequests\\HandleRequests), 'handleUpdate')
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(47): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#71 {main}
"} 
[2025-07-13 21:56:28] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from "contact_infos") (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) {"userId":1,"exception":"[object] (Illuminate\\View\\ViewException(code: 0): SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from \"contact_infos\") (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(40): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(Illuminate\\View\\ViewException), 1)
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#76 {main}

[previous exception] [object] (Illuminate\\View\\ViewException(code: 0): SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from \"contact_infos\") (View: /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/resources/views/index.blade.php) at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(58): Illuminate\\View\\Engines\\CompilerEngine->handleViewException(Object(Illuminate\\Database\\QueryException), 3)
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(40): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->handleViewException(Object(Illuminate\\Database\\QueryException), 3)
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/support/src/Components/ViewComponent.php(115): Illuminate\\View\\View->render()
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Support/helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/b7f44ef6930d816767392140fbb1c156.php(42): e(Object(Filament\\Tables\\Table))
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#77 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#79 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#81 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#82 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#83 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#84 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#85 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#86 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: contact_infos (Connection: sqlite, SQL: select count(*) as aggregate from \"contact_infos\") at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3310): Illuminate\\Database\\Query\\Builder->get()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3269): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Table/Concerns/HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/2b1ac5dd50010ea575832b7a7eb962cc.php(66): Filament\\Tables\\Table->getRecords()
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/support/src/Components/ViewComponent.php(115): Illuminate\\View\\View->render()
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Support/helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/b7f44ef6930d816767392140fbb1c156.php(42): e(Object(Filament\\Tables\\Table))
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#78 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#81 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#83 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#85 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#87 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#89 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#91 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#93 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#95 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#96 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#97 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#98 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: contact_infos at /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php:404)
[stacktrace]
#0 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): PDO->prepare('select count(*)...')
#1 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::select():395}('select count(*)...', Array)
#2 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#3 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#4 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#5 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->{closure:Illuminate\\Database\\Query\\Builder::get():3115}()
#7 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3310): Illuminate\\Database\\Query\\Builder->get()
#9 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3269): Illuminate\\Database\\Query\\Builder->runPaginationCountQuery(Array)
#10 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/CanPaginateRecords.php(34): Illuminate\\Database\\Query\\Builder->getCountForPagination()
#11 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Concerns/HasRecords.php(111): Filament\\Resources\\Pages\\ListRecords->paginateTableQuery(Object(Illuminate\\Database\\Eloquent\\Builder))
#12 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/tables/src/Table/Concerns/HasRecords.php(66): Filament\\Resources\\Pages\\ListRecords->getTableRecords()
#13 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/2b1ac5dd50010ea575832b7a7eb962cc.php(66): Filament\\Tables\\Table->getRecords()
#14 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#15 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#16 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#17 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#18 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#19 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#20 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#21 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/support/src/Components/ViewComponent.php(115): Illuminate\\View\\View->render()
#22 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Support/helpers.php(134): Filament\\Support\\Components\\ViewComponent->toHtml()
#23 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/storage/framework/views/b7f44ef6930d816767392140fbb1c156.php(42): e(Object(Filament\\Tables\\Table))
#24 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(37): include('/Applications/X...')
#25 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(38): App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos->{closure:Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine::evaluatePath():35}()
#26 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(76): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('/Applications/X...', Array)
#27 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php(16): Illuminate\\View\\Engines\\CompilerEngine->get('/Applications/X...', Array)
#28 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(208): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('/Applications/X...', Array)
#29 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(191): Illuminate\\View\\View->getContents()
#30 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/View.php(160): Illuminate\\View\\View->renderContents()
#31 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(241): Illuminate\\View\\View->render(Object(Closure))
#32 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(285): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->{closure:Livewire\\Mechanisms\\HandleComponents\\HandleComponents::render():233}()
#33 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(233): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->trackInRenderStack(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), Object(Closure))
#34 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Mechanisms/HandleComponents/HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '<div></div>')
#35 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('App\\\\Filament\\\\Re...', Array, NULL)
#36 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(17): Livewire\\LivewireManager->mount('App\\\\Filament\\\\Re...', Array)
#37 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/SupportPageComponents.php(117): Livewire\\Component->{closure:Livewire\\Features\\SupportPageComponents\\HandlesPageComponents::__invoke():14}()
#38 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportPageComponents/HandlesPageComponents.php(14): Livewire\\Features\\SupportPageComponents\\SupportPageComponents::interceptTheRenderOfTheComponentAndRetreiveTheLayoutConfiguration(Object(Closure))
#39 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(46): Livewire\\Component->__invoke()
#40 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Filament\\Resources\\ContactInfoResource\\Pages\\ListContactInfos), '__invoke')
#41 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Route.php(211): Illuminate\\Routing\\Route->runController()
#42 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#43 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}(Object(Illuminate\\Http\\Request))
#44 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DispatchServingFilamentEvent.php(15): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#45 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DispatchServingFilamentEvent->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/DisableBladeIconComponents.php(14): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#47 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\DisableBladeIconComponents->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#49 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#51 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/AuthenticateSession.php(66): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#53 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Auth/Middleware/Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#55 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#57 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(120): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#59 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#60 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#62 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#64 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/filament/filament/src/Http/Middleware/SetUpPanel.php(19): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#66 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Filament\\Http\\Middleware\\SetUpPanel->handle(Object(Illuminate\\Http\\Request), Object(Closure), Object(Filament\\Panel))
#67 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#68 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#70 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#71 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#72 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#73 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}(Object(Illuminate\\Http\\Request))
#74 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}(Object(Illuminate\\Http\\Request))
#75 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#77 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#80 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#83 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#84 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#85 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#87 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#89 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#91 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#93 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#94 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}(Object(Illuminate\\Http\\Request))
#95 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#96 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#97 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#98 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#99 /Applications/XAMPP/xamppfiles/htdocs/portfolio/portfolio-admin/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Applications/X...')
#100 {main}
"} 
