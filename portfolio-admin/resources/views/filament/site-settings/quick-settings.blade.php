<x-filament-panels::page>
    <div class="space-y-6">
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div class="mb-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Quick Settings Management
                </h2>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Easily manage your most important site settings in one place.
                </p>
            </div>

            <form wire:submit="save">
                {{ $this->form }}
                
                <div class="mt-6 flex justify-end space-x-3">
                    {{ $this->resetAction }}
                    {{ $this->saveAction }}
                </div>
            </form>
        </div>

        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Quick Tips
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Changes are automatically applied to your frontend</li>
                            <li>Analytics IDs will start tracking immediately after saving</li>
                            <li>SEO settings improve your search engine visibility</li>
                            <li>Theme colors update your website's appearance</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    API Endpoints
                </h3>
                <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                    <div><code>/api/v1/settings</code></div>
                    <div><code>/api/v1/meta-tags</code></div>
                    <div><code>/api/v1/analytics</code></div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Cache Status
                </h3>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                    Settings are cached for performance.<br>
                    Cache clears automatically on save.
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
                <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Need Help?
                </h3>
                <div class="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                    <div>• Check the full settings list</div>
                    <div>• View API documentation</div>
                    <div>• Test your analytics setup</div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
