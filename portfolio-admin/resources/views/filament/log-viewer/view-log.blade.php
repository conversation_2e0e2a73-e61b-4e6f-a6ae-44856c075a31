<div class="space-y-4">
    <div class="grid grid-cols-2 gap-4">
        <div>
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Level</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                @switch(strtolower($record->level))
                    @case('emergency')
                    @case('alert')
                    @case('critical')
                    @case('error')
                        bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                        @break
                    @case('warning')
                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                        @break
                    @case('notice')
                    @case('info')
                        bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                        @break
                    @case('debug')
                        bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                        @break
                    @default
                        bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                @endswitch
            ">
                {{ ucfirst($record->level) }}
            </span>
        </div>
        
        <div>
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100">Date/Time</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $record->datetime->format('Y-m-d H:i:s') }}</p>
        </div>
    </div>
    
    <div>
        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Message</h3>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ $record->message }}</pre>
        </div>
    </div>
    
    @if($record->context)
    <div>
        <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">Context</h3>
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ $record->context }}</pre>
        </div>
    </div>
    @endif
</div>
