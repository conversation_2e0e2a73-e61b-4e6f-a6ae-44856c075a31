<x-filament-panels::page>
    <div class="space-y-6">
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Application Logs
                </h3>
                
                @php
                    $logs = \App\Filament\Resources\LogViewerResource::getLogEntries();
                @endphp
                
                @if(empty($logs))
                    <div class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">No log entries found.</p>
                    </div>
                @else
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Level
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Date/Time
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        Message
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                                @foreach(array_slice($logs, 0, 50) as $log)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                @switch(strtolower($log['level']))
                                                    @case('emergency')
                                                    @case('alert')
                                                    @case('critical')
                                                    @case('error')
                                                        bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                                        @break
                                                    @case('warning')
                                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                                        @break
                                                    @case('notice')
                                                    @case('info')
                                                        bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                                        @break
                                                    @case('debug')
                                                        bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200
                                                        @break
                                                    @default
                                                        bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                                @endswitch
                                            ">
                                                {{ ucfirst($log['level']) }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                            {{ $log['datetime']->format('Y-m-d H:i:s') }}
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                                            <div class="max-w-xs truncate" title="{{ $log['message'] }}">
                                                {{ $log['message'] }}
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if(count($logs) > 50)
                        <div class="mt-4 text-center">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                Showing latest 50 entries out of {{ count($logs) }} total entries.
                            </p>
                        </div>
                    @endif
                @endif
            </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                    Log Management
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button onclick="window.location.reload()" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Refresh Logs
                    </button>
                    <a href="{{ url('/admin/system-info') }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        System Info
                    </a>
                    <a href="{{ url('/api/status') }}" target="_blank"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        API Status
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
