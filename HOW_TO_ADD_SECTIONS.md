# 🎯 How to Add New Sections to Your Portfolio

## 📋 **Step-by-Step Guide**

### **1. Create the Section Component**

Create a new file in `src/components/sections/`:

```typescript
// src/components/sections/ServicesSection.tsx
'use client'

import { useEffect, useRef, useState } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { motion } from 'framer-motion'
import Typography from '@/components/ui/Typography'
import Container from '@/components/ui/Container'
import Card from '@/components/ui/Card'

gsap.registerPlugin(ScrollTrigger)

const ServicesSection = () => {
  const sectionRef = useRef<HTMLElement>(null)

  const services = [
    {
      title: 'Web Development',
      description: 'Custom websites and web applications',
      icon: '🌐'
    },
    {
      title: 'UI/UX Design',
      description: 'Beautiful and intuitive user interfaces',
      icon: '🎨'
    },
    {
      title: 'Mobile Apps',
      description: 'Native and cross-platform mobile solutions',
      icon: '📱'
    }
  ]

  useEffect(() => {
    const section = sectionRef.current
    if (!section) return

    // Animate service cards
    gsap.fromTo('.service-card', 
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        scrollTrigger: {
          trigger: section,
          start: 'top 80%',
        }
      }
    )
  }, [])

  return (
    <section id="services" ref={sectionRef} className="py-20 bg-white">
      <Container size="xl">
        <div className="text-center mb-16">
          <Typography variant="h2" font="clash" weight="bold" className="mb-4">
            My <span className="text-primary-green">Services</span>
          </Typography>
          <Typography variant="body" color="muted" className="max-w-2xl mx-auto">
            I offer a range of services to help bring your ideas to life
          </Typography>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={index}
              className="service-card"
              whileHover={{ y: -10 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="text-center p-8 h-full">
                <div className="text-4xl mb-4">{service.icon}</div>
                <Typography variant="h4" font="satoshi" weight="semibold" className="mb-4">
                  {service.title}
                </Typography>
                <Typography variant="body" color="muted">
                  {service.description}
                </Typography>
              </Card>
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  )
}

export default ServicesSection
```

### **2. Add to Main Page**

Update `src/app/page.tsx`:

```typescript
import ServicesSection from '@/components/sections/ServicesSection'

export default function Home() {
  return (
    <main>
      <HeroSection />
      <AboutSection />
      <ServicesSection />  {/* Add your new section here */}
      <ProjectsSection />
      <SkillsSection />
      <ContactSection />
    </main>
  )
}
```

### **3. Add Navigation Link**

Update `src/components/layout/Navigation.tsx`:

```typescript
const navItems = [
  { href: '#home', label: 'Home' },
  { href: '#about', label: 'About' },
  { href: '#services', label: 'Services' },  // Add navigation item
  { href: '#projects', label: 'Projects' },
  { href: '#skills', label: 'Skills' },
  { href: '#contact', label: 'Contact' },
]
```

### **4. Create Admin Panel (Optional)**

If you want to manage the section content through the admin panel:

1. **Create Migration**:
```bash
cd portfolio-admin
php artisan make:migration create_services_table
```

2. **Create Model**:
```bash
php artisan make:model Service
```

3. **Create Filament Resource**:
```bash
php artisan make:filament-resource Service
```

4. **Add API Endpoint** in `routes/api.php`:
```php
Route::get('/services', function () {
    return response()->json([
        'message' => 'Services retrieved successfully',
        'data' => \App\Models\Service::where('is_active', true)->get()
    ]);
});
```

5. **Update Frontend** to fetch from API:
```typescript
import { fetchServices } from '@/lib/api'

const [services, setServices] = useState([])

useEffect(() => {
  const loadServices = async () => {
    const data = await fetchServices()
    setServices(data)
  }
  loadServices()
}, [])
```

## 🎨 **Section Ideas**

- **Services** - What you offer
- **Testimonials** - Client reviews
- **Blog** - Latest articles
- **Certifications** - Your credentials
- **Process** - How you work
- **FAQ** - Common questions
- **Team** - If you work with others
- **Pricing** - Service packages

## 🚀 **Quick Tips**

1. **Use consistent styling** - Follow the existing design patterns
2. **Add animations** - Use GSAP and Framer Motion like other sections
3. **Make it responsive** - Test on mobile devices
4. **Add to navigation** - Don't forget the nav menu
5. **Consider admin panel** - For dynamic content management
