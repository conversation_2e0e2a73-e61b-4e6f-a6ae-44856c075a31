# 🎛️ Complete Admin Panel Guide

## 🚀 **Getting Started**

### **Access Your Admin Panel**
1. **URL**: http://localhost:8000/admin
2. **Email**: <EMAIL>
3. **Password**: password123

---

## 🎨 **1. HERO SECTION ANIMATIONS**

### **How to Configure Hero Animations**
1. Go to **Admin Panel → Hero Sections**
2. Click **Edit** on your hero section
3. **Enable Animations**: Toggle ON/OFF
4. **Animation Settings** (JSON format):

```json
{
  "fade_in_duration": 1500,
  "slide_distance": 120,
  "stagger_delay": 0.15,
  "background_scale": 1.3,
  "enable_parallax": true,
  "enable_hover_effects": true
}
```

### **Animation Options Explained**
- **fade_in_duration**: How long animations take (milliseconds)
- **slide_distance**: How far elements slide in (pixels)
- **stagger_delay**: Delay between element animations (seconds)
- **background_scale**: Background zoom effect (1.0 = no zoom)
- **enable_parallax**: Scroll-based movement effects
- **enable_hover_effects**: Mouse interaction effects

---

## 📝 **2. ABOUT SECTION MANAGEMENT**

### **Update About Content**
1. Go to **Admin Panel → About Sections**
2. Click **Edit** on your about section
3. **Content**: Rich text editor for your story
4. **Profile Image**: Upload your photo
5. **Signature**: Add signature text or image
6. **Resume**: Upload PDF resume file
7. **Stats**: JSON format for statistics

### **Stats Example**
```json
{
  "projects_completed": 50,
  "years_experience": 5,
  "happy_clients": 30,
  "awards_won": 8
}
```

### **Timeline Management**
1. Go to **Admin Panel → Timeline Items**
2. Click **Create Timeline Item**
3. **Fields**:
   - **Title**: Job title or achievement
   - **Company**: Company or institution name
   - **Description**: What you did
   - **Start Date**: When you started
   - **End Date**: When you finished (leave empty for current)
   - **Type**: Work, Education, or Achievement
   - **Icon**: Upload custom icon (optional)

---

## 📧 **3. CONTACT FORM & MESSAGES**

### **View Contact Messages**
1. Go to **Admin Panel → Contact → Contact Submissions**
2. **See all messages** with status indicators:
   - 🟡 **New** - Unread messages
   - 🔵 **Read** - Opened messages
   - 🟢 **Replied** - Responded to
   - ⚪ **Archived** - Completed conversations

### **Manage Contact Messages**
1. **Click on any message** to view details
2. **Mark as Read**: Click the eye icon
3. **Mark as Replied**: Click the reply icon
4. **Add Admin Notes**: Internal notes for follow-up
5. **Archive**: Move to archived status

### **Update Contact Information**
1. Go to **Admin Panel → Contact → Contact Info**
2. **Update**:
   - Email address
   - Phone number
   - Location/Address
   - Availability status
   - Working hours (JSON format)

### **Working Hours Example**
```json
{
  "Monday": "9:00 AM - 6:00 PM PST",
  "Tuesday": "9:00 AM - 6:00 PM PST",
  "Wednesday": "9:00 AM - 6:00 PM PST",
  "Thursday": "9:00 AM - 6:00 PM PST",
  "Friday": "9:00 AM - 5:00 PM PST",
  "Saturday": "Available for urgent projects",
  "Sunday": "Rest day"
}
```

---

## 🔗 **4. SOCIAL LINKS MANAGEMENT**

### **Add/Edit Social Links**
1. Go to **Admin Panel → Contact → Social Links**
2. **Create New Social Link**:
   - **Platform**: github, linkedin, twitter, etc.
   - **URL**: Full URL to your profile
   - **Username**: Your username (optional)
   - **Color**: Brand color for the platform
   - **Sort Order**: Display order (1, 2, 3...)
   - **Active**: Toggle visibility

### **Popular Platforms**
- **GitHub**: #181717
- **LinkedIn**: #0A66C2
- **Twitter**: #1DA1F2
- **Dribbble**: #EA4C89
- **Behance**: #1769FF
- **Instagram**: #E4405F

---

## 📁 **5. PROJECTS MANAGEMENT**

### **Add New Project**
1. Go to **Admin Panel → Projects**
2. Click **Create Project**
3. **Fill in details**:
   - **Title**: Project name
   - **Slug**: URL-friendly name (auto-generated)
   - **Description**: Full project description
   - **Short Description**: Brief summary for cards
   - **Featured Image**: Main project image
   - **Gallery**: Additional project images
   - **Project Type**: Featured or Normal
   - **Live URL**: Link to live project
   - **GitHub URL**: Link to source code
   - **Client**: Client name (optional)
   - **Completion Date**: When project was finished

### **Add Technologies**
1. **In project edit page**, scroll to **Technologies**
2. **Create Technology** if not exists:
   - Name, Icon, Color, Category
3. **Attach to project** using the relation manager

---

## 🛠️ **6. SKILLS MANAGEMENT**

### **Organize Skills by Categories**
1. **Create Categories** first:
   - Go to **Admin Panel → Skill Categories**
   - Add: Frontend, Backend, Tools, etc.

2. **Add Skills**:
   - Go to **Admin Panel → Skills**
   - Assign to categories
   - Set proficiency level (1-100)
   - Mark as featured for homepage

---

## ⚙️ **7. SITE SETTINGS**

### **Configure Global Settings**
1. Go to **Admin Panel → Settings → Site Settings**
2. **Key Settings**:
   - **site_name**: Website title
   - **seo_title**: SEO page title
   - **seo_description**: Meta description
   - **primary_color**: Brand color
   - **google_analytics_id**: GA tracking ID

### **Setting Types**
- **Text**: Simple text values
- **Boolean**: True/false toggles
- **Color**: Color picker values
- **JSON**: Complex data structures
- **File/Image**: Upload files

---

## 🎯 **Quick Actions**

### **Daily Tasks**
1. **Check Messages**: Review new contact submissions
2. **Update Projects**: Add new work to portfolio
3. **Monitor Analytics**: Check site performance

### **Weekly Tasks**
1. **Update About**: Keep timeline current
2. **Review Settings**: Ensure SEO is optimized
3. **Backup Data**: Export important content

### **Monthly Tasks**
1. **Add New Skills**: Update skill proficiencies
2. **Refresh Content**: Update hero messaging
3. **Performance Review**: Check loading speeds

---

## 🚨 **Troubleshooting**

### **Common Issues**
1. **Images not showing**: Check file upload permissions
2. **API errors**: Ensure Laravel server is running
3. **Changes not visible**: Clear browser cache
4. **Login issues**: Check database connection

### **Support**
- **API Documentation**: http://localhost:8000/api/docs
- **Laravel Logs**: `portfolio-admin/storage/logs/`
- **Browser Console**: Check for JavaScript errors
