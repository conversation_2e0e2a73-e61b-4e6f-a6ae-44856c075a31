{"c": ["app/layout", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!"]}