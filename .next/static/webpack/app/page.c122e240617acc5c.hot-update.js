"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ContactSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/ContactSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_7__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger);\nconst ContactSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__.useScrollAnimations)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        subject: '',\n        message: '',\n        phone: '',\n        company: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [contactInfo, setContactInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [socialLinks, setSocialLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch contact data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactSection.useEffect\": ()=>{\n            const loadContactData = {\n                \"ContactSection.useEffect.loadContactData\": async ()=>{\n                    try {\n                        const [contact, social] = await Promise.all([\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.fetchContactInfo)(),\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.fetchSocialLinks)()\n                        ]);\n                        setContactInfo(contact);\n                        setSocialLinks(social);\n                    } catch (error) {\n                        console.error('Failed to load contact data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ContactSection.useEffect.loadContactData\"];\n            loadContactData();\n        }\n    }[\"ContactSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            if (!section) return;\n            // Animate contact items\n            staggerAnimation('.contact-item', 'fadeIn', {\n                trigger: section,\n                start: 'top 80%',\n                stagger: 0.2\n            });\n            // Animate form inputs\n            staggerAnimation('.form-input', 'slideUp', {\n                trigger: section,\n                start: 'top 70%',\n                stagger: 0.1\n            });\n            return ({\n                \"ContactSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger.getAll().forEach({\n                        \"ContactSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ContactSection.useEffect\"]);\n                }\n            })[\"ContactSection.useEffect\"];\n        }\n    }[\"ContactSection.useEffect\"], [\n        staggerAnimation\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            // Submit to Laravel API\n            await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.submitContactForm)({\n                name: formData.name,\n                email: formData.email,\n                subject: formData.subject,\n                message: formData.message,\n                phone: formData.phone,\n                company: formData.company\n            });\n            // Reset form\n            setFormData({\n                name: '',\n                email: '',\n                subject: '',\n                message: '',\n                phone: '',\n                company: ''\n            });\n            setSubmitStatus('success');\n            // Reset status after 5 seconds\n            setTimeout(()=>setSubmitStatus('idle'), 5000);\n        } catch (error) {\n            console.error('Contact form submission failed:', error);\n            setSubmitStatus('error');\n            setTimeout(()=>setSubmitStatus('idle'), 5000);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        ref: sectionRef,\n        className: \"py-20 bg-primary-neutral relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 right-0 w-72 h-72 bg-primary-peach/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 left-0 w-96 h-96 bg-primary-green/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"contact-item text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"h2\",\n                                font: \"clash\",\n                                weight: \"bold\",\n                                className: \"mb-4\",\n                                children: [\n                                    \"Let's \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-peach\",\n                                        children: \"Connect\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"body\",\n                                color: \"muted\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"Ready to bring your next project to life? Let's discuss how we can create something amazing together.\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"contact-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"h3\",\n                                        font: \"satoshi\",\n                                        weight: \"bold\",\n                                        className: \"mb-8\",\n                                        children: \"Get in Touch\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8 mb-12\",\n                                        children: loading ? [\n                                            ...Array(3)\n                                        ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-xl animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-200 rounded animate-pulse w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-3 bg-gray-200 rounded animate-pulse w-32\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, undefined)) : (()=>{\n                                            const contactItems = [\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"22,6 12,13 2,6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    title: 'Email',\n                                                    value: (contactInfo === null || contactInfo === void 0 ? void 0 : contactInfo.email) || '<EMAIL>',\n                                                    color: 'bg-primary-peach'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    title: 'Phone',\n                                                    value: (contactInfo === null || contactInfo === void 0 ? void 0 : contactInfo.phone) || '+****************',\n                                                    color: 'bg-primary-green'\n                                                },\n                                                {\n                                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        width: \"20\",\n                                                        height: \"20\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                cx: \"12\",\n                                                                cy: \"10\",\n                                                                r: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    title: 'Location',\n                                                    value: (contactInfo === null || contactInfo === void 0 ? void 0 : contactInfo.location) || 'San Francisco, CA',\n                                                    color: 'bg-primary-black'\n                                                }\n                                            ];\n                                            return contactItems.filter((item)=>item.value).map((contact, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                    className: \"flex items-center space-x-4\",\n                                                    whileHover: {\n                                                        x: 4\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 \".concat(contact.color, \" rounded-full flex items-center justify-center text-white\"),\n                                                            children: contact.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"body\",\n                                                                    weight: \"medium\",\n                                                                    className: \"mb-1\",\n                                                                    children: contact.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    color: \"muted\",\n                                                                    children: contact.value\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, contact.title, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined));\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                variant: \"h6\",\n                                                font: \"satoshi\",\n                                                weight: \"semibold\",\n                                                className: \"mb-4\",\n                                                children: \"Follow Me\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: loading ? [\n                                                    ...Array(4)\n                                                ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"\n                                                    }, index, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 21\n                                                    }, undefined)) : socialLinks.filter((link)=>link.url).map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                                                        href: social.url,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"w-12 h-12 rounded-full flex items-center justify-center text-white hover:bg-primary-peach transition-colors duration-300\",\n                                                        style: {\n                                                            backgroundColor: social.color || '#010101'\n                                                        },\n                                                        whileHover: {\n                                                            scale: 1.1,\n                                                            y: -2\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        \"data-cursor\": \"pointer\",\n                                                        title: \"Follow me on \".concat(social.platform),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-bold\",\n                                                            children: social.platform.slice(0, 2).toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, social.id, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"contact-item\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    ref: formRef,\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.input, {\n                                                type: \"text\",\n                                                name: \"name\",\n                                                placeholder: \"Your Name\",\n                                                value: formData.name,\n                                                onChange: handleChange,\n                                                className: \"w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 focus:shadow-lg\",\n                                                required: true,\n                                                whileFocus: {\n                                                    scale: 1.02\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.input, {\n                                                type: \"email\",\n                                                name: \"email\",\n                                                placeholder: \"Your Email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                className: \"w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 focus:shadow-lg\",\n                                                required: true,\n                                                whileFocus: {\n                                                    scale: 1.02\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.textarea, {\n                                                name: \"message\",\n                                                placeholder: \"Your Message\",\n                                                value: formData.message,\n                                                onChange: handleChange,\n                                                rows: 5,\n                                                className: \"w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 resize-none focus:shadow-lg\",\n                                                required: true,\n                                                whileFocus: {\n                                                    scale: 1.02\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                type: \"submit\",\n                                                variant: \"primary\",\n                                                size: \"lg\",\n                                                className: \"w-full\",\n                                                isLoading: isSubmitting,\n                                                children: isSubmitting ? 'Sending...' : 'Send Message'\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"p-4 bg-green-100 text-green-800 rounded-xl text-center\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            children: \"Message sent successfully! I'll get back to you soon.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"p-4 bg-red-100 text-red-800 rounded-xl text-center\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            children: \"Something went wrong. Please try again.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContactSection, \"R90Yycrvlpg0VbaNFiVyXXyeufI=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__.useScrollAnimations\n    ];\n});\n_c = ContactSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContactSection);\nvar _c;\n$RefreshReg$(_c, \"ContactSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ContactSection.tsx\n"));

/***/ })

});