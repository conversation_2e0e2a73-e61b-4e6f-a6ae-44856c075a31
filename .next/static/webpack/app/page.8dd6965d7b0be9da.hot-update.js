"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/preloader.ts":
/*!*********************************!*\
  !*** ./src/config/preloader.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   preloaderConfig: () => (/* binding */ preloaderConfig)\n/* harmony export */ });\n// Preloader Configuration\n// Switch between different preloader styles\nconst preloaderConfig = {\n    // Choose preloader style: 'awwwards' | 'minimal' | 'simple' | 'disabled'\n    style: 'simple',\n    // Duration in milliseconds\n    duration: 3000,\n    // Enable/disable preloader entirely\n    enabled: true,\n    // Show preloader only on first visit (uses localStorage)\n    showOnlyOnFirstVisit: false,\n    // Custom loading messages for Awwwards style\n    loadingWords: [\n        'LOADING',\n        'CREATING',\n        'CRAFTING',\n        'BUILDING',\n        'DESIGNING',\n        'ANIMATING',\n        'READY'\n    ],\n    // Animation settings\n    animations: {\n        // Logo entrance delay\n        logoDelay: 0.2,\n        // Progress bar smoothness (lower = smoother, higher = choppier)\n        progressUpdateInterval: 50,\n        // Exit animation duration\n        exitDuration: 1000\n    },\n    // Visual settings\n    visual: {\n        // Show floating particles\n        showParticles: true,\n        // Show animated background\n        showAnimatedBackground: true,\n        // Show geometric shapes\n        showGeometricShapes: true,\n        // Particle count\n        particleCount: 20\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (preloaderConfig);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/preloader.ts\n"));

/***/ })

});