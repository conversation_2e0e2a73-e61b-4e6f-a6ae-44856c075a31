"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_6__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_7__.ScrollTrigger);\nconst AboutSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timelineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn, fadeIn } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__.useScrollAnimations)();\n    const [aboutData, setAboutData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timelineData, setTimelineData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch about and timeline data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const loadAboutData = {\n                \"AboutSection.useEffect.loadAboutData\": async ()=>{\n                    try {\n                        const [about, timeline] = await Promise.all([\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_5__.fetchAboutSection)(),\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_5__.fetchTimeline)()\n                        ]);\n                        setAboutData(about);\n                        setTimelineData(timeline);\n                    } catch (error) {\n                        console.error('Failed to load about data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"AboutSection.useEffect.loadAboutData\"];\n            loadAboutData();\n        }\n    }[\"AboutSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            const timelineEl = timelineRef.current;\n            if (!section || !timelineEl) return;\n            // Animate main content\n            staggerAnimation('.about-content > *', 'fadeIn', {\n                trigger: section,\n                start: 'top 80%',\n                stagger: 0.2\n            });\n            // Animate timeline items\n            staggerAnimation('.timeline-item', 'slideUp', {\n                trigger: timelineEl,\n                start: 'top 80%',\n                stagger: 0.15\n            });\n            // Animate timeline line\n            gsap__WEBPACK_IMPORTED_MODULE_6__.gsap.fromTo('.timeline-line', {\n                height: '0%'\n            }, {\n                height: '100%',\n                duration: 2,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: timelineEl,\n                    start: 'top 70%',\n                    end: 'bottom 30%',\n                    scrub: 1\n                }\n            });\n            return ({\n                \"AboutSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_7__.ScrollTrigger.getAll().forEach({\n                        \"AboutSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"AboutSection.useEffect\"]);\n                }\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: sectionRef,\n        className: \"py-20 bg-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-0 w-64 h-64 bg-primary-peach/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-0 w-80 h-80 bg-primary-green/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h2\",\n                                    font: \"clash\",\n                                    weight: \"bold\",\n                                    className: \"mb-6\",\n                                    children: [\n                                        \"About \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-green\",\n                                            children: \"Me\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, undefined),\n                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-5/6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-4/6\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, undefined) : (aboutData === null || aboutData === void 0 ? void 0 : aboutData.content) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 leading-relaxed text-primary-black/70\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: aboutData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"body\",\n                                            color: \"muted\",\n                                            className: \"mb-6 leading-relaxed\",\n                                            children: \"I'm a passionate developer who creates award-winning digital experiences. With expertise in modern web technologies and a keen eye for design, I bring ideas to life through code.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"body\",\n                                            color: \"muted\",\n                                            className: \"mb-8 leading-relaxed\",\n                                            children: \"My journey spans over 5 years of crafting innovative solutions for clients worldwide. I specialize in creating performant, accessible, and visually stunning web applications that push the boundaries of what's possible.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-6 mb-8\",\n                                    children: loading ? [\n                                        ...Array(3)\n                                    ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 bg-gray-200 rounded animate-pulse mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 bg-gray-200 rounded animate-pulse w-16 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, undefined)) : (()=>{\n                                        const stats = (aboutData === null || aboutData === void 0 ? void 0 : aboutData.stats) || {\n                                            projects_completed: 50,\n                                            years_experience: 5,\n                                            happy_clients: 15\n                                        };\n                                        return [\n                                            {\n                                                number: \"\".concat(stats.projects_completed || 50, \"+\"),\n                                                label: 'Projects'\n                                            },\n                                            {\n                                                number: \"\".concat(stats.years_experience || 5, \"+\"),\n                                                label: 'Years'\n                                            },\n                                            {\n                                                number: \"\".concat(stats.happy_clients || 15, \"+\"),\n                                                label: 'Clients'\n                                            }\n                                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        variant: \"h3\",\n                                                        font: \"clash\",\n                                                        weight: \"bold\",\n                                                        className: \"text-primary-peach mb-1\",\n                                                        children: stat.number\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        variant: \"caption\",\n                                                        color: \"muted\",\n                                                        className: \"text-sm\",\n                                                        children: stat.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, stat.label, true, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 21\n                                            }, undefined));\n                                    })()\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 bg-primary-black text-white rounded-full font-medium hover:bg-primary-green transition-colors duration-300\",\n                                        children: \"Download Resume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: timelineRef,\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h3\",\n                                    font: \"clash\",\n                                    weight: \"bold\",\n                                    className: \"mb-8 text-center lg:text-left\",\n                                    children: [\n                                        \"My \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-peach\",\n                                            children: \"Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 18\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-6 top-0 w-0.5 bg-primary-neutral\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"timeline-line w-full bg-primary-peach origin-top\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-8\",\n                                            children: loading ? // Loading skeleton\n                                            [\n                                                ...Array(3)\n                                            ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"timeline-item relative pl-16\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-4 top-2 w-4 h-4 bg-gray-300 rounded-full animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-200 rounded-xl p-6 animate-pulse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-300 rounded mb-2 w-16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-5 bg-gray-300 rounded mb-2 w-32\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-300 rounded w-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, undefined)) : timelineData.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    className: \"timeline-item relative pl-16\",\n                                                    whileHover: {\n                                                        x: 4\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-4 top-2 w-4 h-4 rounded-full border-4 border-white shadow-lg \".concat(item.is_current ? 'bg-primary-green animate-pulse' : 'bg-primary-peach')\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary-neutral/50 rounded-xl p-6 hover:bg-primary-neutral/70 transition-colors duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            variant: \"h6\",\n                                                                            font: \"clash\",\n                                                                            weight: \"bold\",\n                                                                            className: \"text-primary-green\",\n                                                                            children: [\n                                                                                new Date(item.start_date).getFullYear(),\n                                                                                item.end_date && !item.is_current && \" - \".concat(new Date(item.end_date).getFullYear()),\n                                                                                item.is_current && ' - Present'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                            lineNumber: 240,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            variant: \"h6\",\n                                                                            font: \"satoshi\",\n                                                                            weight: \"semibold\",\n                                                                            children: item.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        item.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            color: \"muted\",\n                                                                            className: \"text-primary-black/60\",\n                                                                            children: item.company\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                            lineNumber: 258,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    color: \"muted\",\n                                                                    className: \"leading-relaxed\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, item.id, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n        lineNumber: 84,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AboutSection, \"51QlByCUaisqxiKm/H0eQFYipKg=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__.useScrollAnimations\n    ];\n});\n_c = AboutSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutSection);\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});