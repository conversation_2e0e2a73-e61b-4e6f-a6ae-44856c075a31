"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/SkillsSection.tsx":
/*!***************************************************!*\
  !*** ./src/components/sections/SkillsSection.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_3__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__.ScrollTrigger);\nconst SkillsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_2__.useScrollAnimations)();\n    const skillCategories = [\n        {\n            title: 'Frontend',\n            skills: [\n                {\n                    name: 'React',\n                    level: 95,\n                    color: '#61DAFB'\n                },\n                {\n                    name: 'Next.js',\n                    level: 90,\n                    color: '#000000'\n                },\n                {\n                    name: 'TypeScript',\n                    level: 88,\n                    color: '#3178C6'\n                },\n                {\n                    name: 'Tailwind CSS',\n                    level: 92,\n                    color: '#06B6D4'\n                }\n            ]\n        },\n        {\n            title: 'Animation',\n            skills: [\n                {\n                    name: 'GSAP',\n                    level: 85,\n                    color: '#88CE02'\n                },\n                {\n                    name: 'Framer Motion',\n                    level: 80,\n                    color: '#0055FF'\n                },\n                {\n                    name: 'Three.js',\n                    level: 75,\n                    color: '#000000'\n                },\n                {\n                    name: 'WebGL',\n                    level: 70,\n                    color: '#990000'\n                }\n            ]\n        },\n        {\n            title: 'Backend',\n            skills: [\n                {\n                    name: 'Node.js',\n                    level: 85,\n                    color: '#339933'\n                },\n                {\n                    name: 'Python',\n                    level: 80,\n                    color: '#3776AB'\n                },\n                {\n                    name: 'PHP',\n                    level: 75,\n                    color: '#777BB4'\n                },\n                {\n                    name: 'MySQL',\n                    level: 82,\n                    color: '#4479A1'\n                }\n            ]\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SkillsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            const title = titleRef.current;\n            if (!section || !title) return;\n            // Animate title\n            slideIn(title, 'up', {\n                start: 'top 90%'\n            });\n            // Stagger animate skill categories\n            staggerAnimation('.skill-category', 'fadeIn', {\n                trigger: section,\n                start: 'top 70%',\n                stagger: 0.2\n            });\n            // Animate skill bars\n            section.querySelectorAll('.skill-bar').forEach({\n                \"SkillsSection.useEffect\": (bar, index)=>{\n                    const level = bar.getAttribute('data-level');\n                    gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.fromTo(bar, {\n                        width: '0%'\n                    }, {\n                        width: \"\".concat(level, \"%\"),\n                        duration: 1.5,\n                        ease: 'power2.out',\n                        delay: index * 0.1,\n                        scrollTrigger: {\n                            trigger: bar,\n                            start: 'top 85%',\n                            toggleActions: 'play none none reverse'\n                        }\n                    });\n                }\n            }[\"SkillsSection.useEffect\"]);\n            return ({\n                \"SkillsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__.ScrollTrigger.getAll().forEach({\n                        \"SkillsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"SkillsSection.useEffect\"]);\n                }\n            })[\"SkillsSection.useEffect\"];\n        }\n    }[\"SkillsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 px-6 bg-primary-neutral\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"font-clash text-5xl md:text-6xl font-bold text-center mb-16\",\n                    children: [\n                        \"Skills & \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary-peach\",\n                            children: \"Expertise\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 20\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                    children: skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"skill-item bg-white rounded-xl p-6 text-center hover:bg-primary-peach/10 transition-colors duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-satoshi text-lg font-semibold\",\n                                children: skill\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, undefined)\n                        }, skill, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SkillsSection, \"FHwL+k52gKiFB/Q+AiLaj9AYIe8=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_2__.useScrollAnimations\n    ];\n});\n_c = SkillsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SkillsSection);\nvar _c;\n$RefreshReg$(_c, \"SkillsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/SkillsSection.tsx\n"));

/***/ })

});