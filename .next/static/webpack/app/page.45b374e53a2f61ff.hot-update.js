"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ProjectsSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/ProjectsSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nconst ProjectsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            if (!section) return;\n            // Animate section on scroll\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(section.querySelectorAll('.project-card'), {\n                opacity: 0,\n                y: 100\n            }, {\n                opacity: 1,\n                y: 0,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: section,\n                    start: 'top 80%',\n                    end: 'bottom 20%',\n                    toggleActions: 'play none none reverse'\n                }\n            });\n            return ({\n                \"ProjectsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.getAll().forEach({\n                        \"ProjectsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ProjectsSection.useEffect\"]);\n                }\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 px-6 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"font-clash text-5xl md:text-6xl font-bold text-center mb-16\",\n                    children: [\n                        \"Featured \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary-green\",\n                            children: \"Projects\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 20\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"project-card bg-primary-neutral rounded-2xl p-6 hover:shadow-xl transition-shadow duration-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video bg-primary-peach/20 rounded-lg mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-satoshi text-xl font-semibold mb-2\",\n                                    children: [\n                                        \"Project \",\n                                        project\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-primary-black/70 text-sm\",\n                                    children: \"A stunning web application built with modern technologies.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, project, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectsSection, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = ProjectsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectsSection);\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\n"));

/***/ })

});