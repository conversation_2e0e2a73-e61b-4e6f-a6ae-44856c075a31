"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nconst Footer = ()=>{\n    _s();\n    const footerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Footer.useEffect\": ()=>{\n            const footer = footerRef.current;\n            if (!footer) return;\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(footer.querySelectorAll('.footer-item'), {\n                opacity: 0,\n                y: 30\n            }, {\n                opacity: 1,\n                y: 0,\n                duration: 0.6,\n                stagger: 0.1,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: footer,\n                    start: 'top 90%',\n                    toggleActions: 'play none none reverse'\n                }\n            });\n            return ({\n                \"Footer.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.getAll().forEach({\n                        \"Footer.useEffect\": (trigger)=>trigger.kill()\n                    }[\"Footer.useEffect\"]);\n                }\n            })[\"Footer.useEffect\"];\n        }\n    }[\"Footer.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        ref: footerRef,\n        className: \"py-12 px-6 bg-primary-black text-primary-neutral\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"footer-item\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-clash text-2xl font-bold mb-4\",\n                                    children: \"Your Name\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-primary-neutral/70 text-sm leading-relaxed\",\n                                    children: \"Creating award-winning digital experiences with passion and precision.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"footer-item\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-satoshi text-lg font-semibold mb-4\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#projects\",\n                                                className: \"text-primary-neutral/70 hover:text-primary-peach transition-colors\",\n                                                children: \"Projects\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#skills\",\n                                                className: \"text-primary-neutral/70 hover:text-primary-peach transition-colors\",\n                                                children: \"Skills\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#about\",\n                                                className: \"text-primary-neutral/70 hover:text-primary-peach transition-colors\",\n                                                children: \"About\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"#contact\",\n                                                className: \"text-primary-neutral/70 hover:text-primary-peach transition-colors\",\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"footer-item\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-satoshi text-lg font-semibold mb-4\",\n                                    children: \"Connect\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-4\",\n                                    children: [\n                                        'GitHub',\n                                        'LinkedIn',\n                                        'Twitter',\n                                        'Dribbble'\n                                    ].map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"w-10 h-10 bg-primary-neutral/10 rounded-full flex items-center justify-center text-xs font-medium hover:bg-primary-peach hover:text-primary-black transition-colors duration-300\",\n                                            \"data-cursor\": \"pointer\",\n                                            children: social.slice(0, 2)\n                                        }, social, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"footer-item border-t border-primary-neutral/20 pt-8 flex flex-col md:flex-row justify-between items-center text-sm text-primary-neutral/70\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Your Name. All rights reserved.\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 md:mt-0\",\n                            children: \"Designed & Developed with ❤️\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Footer.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Footer, \"eCegZDAHZVFkZflyjZmUJahdfxk=\");\n_c = Footer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Footer);\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Footer.tsx\n"));

/***/ })

});