"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ProjectsSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/ProjectsSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_5__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger);\nconst ProjectsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            if (!section) return;\n            // Animate section on scroll\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo(section.querySelectorAll('.project-card'), {\n                opacity: 0,\n                y: 100\n            }, {\n                opacity: 1,\n                y: 0,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: section,\n                    start: 'top 80%',\n                    end: 'bottom 20%',\n                    toggleActions: 'play none none reverse'\n                }\n            });\n            return ({\n                \"ProjectsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger.getAll().forEach({\n                        \"ProjectsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ProjectsSection.useEffect\"]);\n                }\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            size: \"xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    variant: \"h2\",\n                    font: \"clash\",\n                    weight: \"bold\",\n                    className: \"text-center mb-16\",\n                    children: [\n                        \"Featured \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary-green\",\n                            children: \"Projects\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 20\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"project-card bg-primary-neutral\",\n                            variant: \"default\",\n                            padding: \"md\",\n                            rounded: \"xl\",\n                            hover: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video bg-primary-peach/20 rounded-lg mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h5\",\n                                    font: \"satoshi\",\n                                    weight: \"semibold\",\n                                    className: \"mb-2\",\n                                    children: [\n                                        \"Project \",\n                                        project\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"caption\",\n                                    color: \"muted\",\n                                    children: \"A stunning web application built with modern technologies.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, project, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectsSection, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = ProjectsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectsSection);\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Card = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = (param, ref)=>{\n    let { className, variant = 'default', padding = 'md', rounded = 'lg', hover = false, children, ...props } = param;\n    const variants = {\n        default: 'bg-white shadow-sm',\n        elevated: 'bg-white shadow-lg',\n        outlined: 'bg-white border border-primary-black/10',\n        ghost: 'bg-transparent'\n    };\n    const paddings = {\n        none: '',\n        sm: 'p-4',\n        md: 'p-6',\n        lg: 'p-8'\n    };\n    const roundings = {\n        none: '',\n        sm: 'rounded-sm',\n        md: 'rounded-md',\n        lg: 'rounded-lg',\n        xl: 'rounded-xl',\n        full: 'rounded-full'\n    };\n    const hoverStyles = hover ? 'hover:shadow-xl hover:-translate-y-1 transition-all duration-300' : '';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(variants[variant], paddings[padding], roundings[rounded], hoverStyles, className),\n        whileHover: hover ? {\n            y: -4,\n            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'\n        } : undefined,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/Card.tsx\",\n        lineNumber: 51,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Card;\nCard.displayName = 'Card';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Card);\nvar _c, _c1;\n$RefreshReg$(_c, \"Card$forwardRef\");\n$RefreshReg$(_c1, \"Card\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/Card.tsx\n"));

/***/ })

});