"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/HeroSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/sections/HeroSection.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _components_ui_AnimatedText__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/AnimatedText */ \"(app-pages-browser)/./src/components/ui/AnimatedText.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_7__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger);\nconst HeroSection = ()=>{\n    _s();\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const subtitleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const backgroundRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [heroData, setHeroData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch hero data from Laravel API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            const loadHeroData = {\n                \"HeroSection.useEffect.loadHeroData\": async ()=>{\n                    try {\n                        const data = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.fetchHeroSection)();\n                        setHeroData(data);\n                    } catch (error) {\n                        console.error('Failed to load hero data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"HeroSection.useEffect.loadHeroData\"];\n            loadHeroData();\n        }\n    }[\"HeroSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            const hero = heroRef.current;\n            const title = titleRef.current;\n            const subtitle = subtitleRef.current;\n            const background = backgroundRef.current;\n            if (!hero || !title || !subtitle || !background || loading) return;\n            // Get animation settings from admin panel or use defaults\n            const animationSettings = (heroData === null || heroData === void 0 ? void 0 : heroData.animation_settings) || {\n                fade_in_duration: 1000,\n                slide_distance: 100,\n                stagger_delay: 0.1,\n                background_scale: 1.2,\n                enable_parallax: true,\n                enable_hover_effects: true\n            };\n            const enableAnimations = (heroData === null || heroData === void 0 ? void 0 : heroData.enable_animations) !== false;\n            if (!enableAnimations) {\n                // If animations are disabled, just show content\n                gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.set([\n                    title,\n                    subtitle,\n                    background\n                ], {\n                    opacity: 1,\n                    y: 0,\n                    scale: 1\n                });\n                return;\n            }\n            // Initial animation timeline with admin settings\n            const tl = gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.timeline();\n            // Set initial states based on admin settings\n            gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.set([\n                title,\n                subtitle\n            ], {\n                opacity: 0,\n                y: animationSettings.slide_distance || 100,\n                rotationX: 45\n            });\n            gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.set(background, {\n                scale: animationSettings.background_scale || 1.2,\n                opacity: 0,\n                rotation: 5\n            });\n            // Animate in with admin-configured timing\n            const duration = (animationSettings.fade_in_duration || 1000) / 1000;\n            tl.to(background, {\n                opacity: 0.15,\n                scale: 1,\n                rotation: 0,\n                duration: duration * 2.5,\n                ease: 'power3.out'\n            }).to(title, {\n                opacity: 1,\n                y: 0,\n                rotationX: 0,\n                duration: duration * 1.5,\n                ease: 'back.out(1.7)',\n                stagger: animationSettings.stagger_delay || 0.1\n            }, '-=2').to(subtitle, {\n                opacity: 1,\n                y: 0,\n                rotationX: 0,\n                duration: duration * 1.2,\n                ease: 'power3.out'\n            }, '-=1');\n            // Enhanced mouse move parallax effect\n            const handleMouseMove = {\n                \"HeroSection.useEffect.handleMouseMove\": (e)=>{\n                    const { clientX, clientY } = e;\n                    const { innerWidth, innerHeight } = window;\n                    const xPos = (clientX / innerWidth - 0.5) * 2;\n                    const yPos = (clientY / innerHeight - 0.5) * 2;\n                    // Multi-layer parallax\n                    gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.to(background, {\n                        x: xPos * 30,\n                        y: yPos * 30,\n                        rotation: xPos * 2,\n                        duration: 0.8,\n                        ease: 'power2.out'\n                    });\n                    gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.to(title, {\n                        x: xPos * 15,\n                        y: yPos * 15,\n                        rotationY: xPos * 5,\n                        duration: 0.5,\n                        ease: 'power2.out'\n                    });\n                    gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.to(subtitle, {\n                        x: xPos * 8,\n                        y: yPos * 8,\n                        duration: 0.6,\n                        ease: 'power2.out'\n                    });\n                }\n            }[\"HeroSection.useEffect.handleMouseMove\"];\n            // Scroll-triggered animations\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger.create({\n                trigger: hero,\n                start: 'top top',\n                end: 'bottom top',\n                scrub: 1,\n                onUpdate: {\n                    \"HeroSection.useEffect\": (self)=>{\n                        const progress = self.progress;\n                        gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.to(title, {\n                            y: progress * -100,\n                            opacity: 1 - progress * 0.5,\n                            duration: 0.3\n                        });\n                        gsap__WEBPACK_IMPORTED_MODULE_7__.gsap.to(subtitle, {\n                            y: progress * -50,\n                            opacity: 1 - progress * 0.8,\n                            duration: 0.3\n                        });\n                    }\n                }[\"HeroSection.useEffect\"]\n            });\n            hero.addEventListener('mousemove', handleMouseMove);\n            return ({\n                \"HeroSection.useEffect\": ()=>{\n                    hero.removeEventListener('mousemove', handleMouseMove);\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger.getAll().forEach({\n                        \"HeroSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"HeroSection.useEffect\"]);\n                }\n            })[\"HeroSection.useEffect\"];\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"home\",\n        ref: heroRef,\n        className: \"relative h-screen flex items-center justify-center overflow-hidden bg-primary-neutral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: backgroundRef,\n                className: \"absolute inset-0 bg-gradient-to-br from-primary-peach/20 via-primary-green/10 to-primary-black/5\"\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"absolute bg-primary-peach rounded-full\",\n                            style: {\n                                width: \"\".concat(4 + i % 3 * 2, \"px\"),\n                                height: \"\".concat(4 + i % 3 * 2, \"px\"),\n                                left: \"\".concat(15 + i * 12, \"%\"),\n                                top: \"\".concat(25 + i % 4 * 15, \"%\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -30 - i * 5,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    Math.sin(i) * 10,\n                                    0\n                                ],\n                                opacity: [\n                                    0.2,\n                                    0.8,\n                                    0.2\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 4 + i * 0.3,\n                                repeat: Infinity,\n                                ease: 'easeInOut',\n                                delay: i * 0.2\n                            }\n                        }, \"dot-\".concat(i), false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"absolute border border-primary-green/30\",\n                            style: {\n                                width: \"\".concat(20 + i * 10, \"px\"),\n                                height: \"\".concat(20 + i * 10, \"px\"),\n                                left: \"\".concat(70 + i * 8, \"%\"),\n                                top: \"\".concat(20 + i * 20, \"%\"),\n                                borderRadius: i % 2 === 0 ? '50%' : '0%'\n                            },\n                            animate: {\n                                rotate: [\n                                    0,\n                                    360\n                                ],\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ]\n                            },\n                            transition: {\n                                duration: 8 + i * 2,\n                                repeat: Infinity,\n                                ease: 'linear',\n                                delay: i * 0.5\n                            }\n                        }, \"shape-\".concat(i), false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"absolute rounded-full blur-sm\",\n                            style: {\n                                width: \"\".concat(60 + i * 20, \"px\"),\n                                height: \"\".concat(60 + i * 20, \"px\"),\n                                left: \"\".concat(10 + i * 30, \"%\"),\n                                top: \"\".concat(60 + i * 10, \"%\"),\n                                background: \"radial-gradient(circle, \".concat(i === 0 ? '#fecf8b20' : i === 1 ? '#45523e15' : '#01010110', \", transparent)\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -20,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    10,\n                                    0\n                                ],\n                                scale: [\n                                    1,\n                                    1.05,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 6 + i * 1.5,\n                                repeat: Infinity,\n                                ease: 'easeInOut',\n                                delay: i * 1\n                            }\n                        }, \"orb-\".concat(i), false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10 text-center\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 bg-primary-black/10 rounded mb-4\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 bg-primary-green/10 rounded mb-8\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-6 bg-primary-black/5 rounded max-w-2xl mx-auto mb-12\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-12 bg-primary-peach/10 rounded w-48 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: titleRef,\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h1\",\n                                    font: \"clash\",\n                                    weight: \"bold\",\n                                    className: \"block mb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AnimatedText__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (heroData === null || heroData === void 0 ? void 0 : heroData.title_line_1) || \"Hello, I'm\",\n                                        delay: 0.5\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h1\",\n                                    font: \"clash\",\n                                    weight: \"bold\",\n                                    className: \"block text-primary-green\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AnimatedText__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        text: (heroData === null || heroData === void 0 ? void 0 : heroData.title_line_2) || \"Creative Developer\",\n                                        delay: 1.2\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: subtitleRef,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"body\",\n                                font: \"inter\",\n                                color: \"muted\",\n                                className: \"max-w-2xl mx-auto mb-12\",\n                                children: (heroData === null || heroData === void 0 ? void 0 : heroData.subtitle) || \"Crafting award-winning digital experiences with cutting-edge technology and innovative design solutions.\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            variant: \"primary\",\n                            size: \"lg\",\n                            className: \"mt-4\",\n                            onClick: ()=>{\n                                if (heroData === null || heroData === void 0 ? void 0 : heroData.cta_link) {\n                                    if (heroData.cta_link.startsWith('#')) {\n                                        var _document_querySelector;\n                                        (_document_querySelector = document.querySelector(heroData.cta_link)) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.scrollIntoView({\n                                            behavior: 'smooth'\n                                        });\n                                    } else {\n                                        window.open(heroData.cta_link, '_blank');\n                                    }\n                                } else {\n                                    var _document_querySelector1;\n                                    (_document_querySelector1 = document.querySelector('#projects')) === null || _document_querySelector1 === void 0 ? void 0 : _document_querySelector1.scrollIntoView({\n                                        behavior: 'smooth'\n                                    });\n                                }\n                            },\n                            children: (heroData === null || heroData === void 0 ? void 0 : heroData.cta_text) || \"Explore My Work\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"w-6 h-10 border-2 border-primary-black rounded-full flex justify-center relative overflow-hidden\",\n                        animate: {\n                            opacity: [\n                                1,\n                                0.5,\n                                1\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                            className: \"w-1 h-3 bg-primary-black rounded-full mt-2\",\n                            animate: {\n                                y: [\n                                    0,\n                                    16,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: 'easeInOut'\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                        className: \"text-xs text-primary-black/60 mt-2 font-medium tracking-wider\",\n                        animate: {\n                            opacity: [\n                                0.6,\n                                1,\n                                0.6\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity,\n                            delay: 0.5\n                        },\n                        children: \"SCROLL\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                        className: \"w-px h-8 bg-gradient-to-b from-primary-black/20 to-transparent mt-2\",\n                        animate: {\n                            scaleY: [\n                                0,\n                                1,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity,\n                            delay: 1\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeroSection, \"m+KiHyMGgV/gf0zjpjsh6j7xjHM=\");\n_c = HeroSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroSection);\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/HeroSection.tsx\n"));

/***/ })

});