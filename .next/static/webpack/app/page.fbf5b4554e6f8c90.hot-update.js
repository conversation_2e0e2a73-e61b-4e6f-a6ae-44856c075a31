"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/HeroSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/sections/HeroSection.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nconst HeroSection = ()=>{\n    _s();\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const subtitleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const backgroundRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            const hero = heroRef.current;\n            const title = titleRef.current;\n            const subtitle = subtitleRef.current;\n            const background = backgroundRef.current;\n            if (!hero || !title || !subtitle || !background) return;\n            // Initial animation timeline\n            const tl = gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.timeline();\n            // Set initial states\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.set([\n                title,\n                subtitle\n            ], {\n                opacity: 0,\n                y: 100\n            });\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.set(background, {\n                scale: 1.1,\n                opacity: 0\n            });\n            // Animate in\n            tl.to(background, {\n                opacity: 0.1,\n                scale: 1,\n                duration: 2,\n                ease: 'power2.out'\n            }).to(title, {\n                opacity: 1,\n                y: 0,\n                duration: 1.2,\n                ease: 'power3.out'\n            }, '-=1.5').to(subtitle, {\n                opacity: 1,\n                y: 0,\n                duration: 1,\n                ease: 'power2.out'\n            }, '-=0.8');\n            // Mouse move parallax effect\n            const handleMouseMove = {\n                \"HeroSection.useEffect.handleMouseMove\": (e)=>{\n                    const { clientX, clientY } = e;\n                    const { innerWidth, innerHeight } = window;\n                    const xPos = (clientX / innerWidth - 0.5) * 2;\n                    const yPos = (clientY / innerHeight - 0.5) * 2;\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(background, {\n                        x: xPos * 20,\n                        y: yPos * 20,\n                        duration: 0.5,\n                        ease: 'power2.out'\n                    });\n                    gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(title, {\n                        x: xPos * 10,\n                        y: yPos * 10,\n                        duration: 0.3,\n                        ease: 'power2.out'\n                    });\n                }\n            }[\"HeroSection.useEffect.handleMouseMove\"];\n            // Scroll-triggered animations\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.create({\n                trigger: hero,\n                start: 'top top',\n                end: 'bottom top',\n                scrub: 1,\n                onUpdate: {\n                    \"HeroSection.useEffect\": (self)=>{\n                        const progress = self.progress;\n                        gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(title, {\n                            y: progress * -100,\n                            opacity: 1 - progress * 0.5,\n                            duration: 0.3\n                        });\n                        gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.to(subtitle, {\n                            y: progress * -50,\n                            opacity: 1 - progress * 0.8,\n                            duration: 0.3\n                        });\n                    }\n                }[\"HeroSection.useEffect\"]\n            });\n            hero.addEventListener('mousemove', handleMouseMove);\n            return ({\n                \"HeroSection.useEffect\": ()=>{\n                    hero.removeEventListener('mousemove', handleMouseMove);\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.getAll().forEach({\n                        \"HeroSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"HeroSection.useEffect\"]);\n                }\n            })[\"HeroSection.useEffect\"];\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: heroRef,\n        className: \"relative h-screen flex items-center justify-center overflow-hidden bg-primary-neutral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: backgroundRef,\n                className: \"absolute inset-0 bg-gradient-to-br from-primary-peach/20 via-primary-green/10 to-primary-black/5\"\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"absolute w-2 h-2 bg-primary-peach rounded-full\",\n                        style: {\n                            left: \"\".concat(20 + i * 15, \"%\"),\n                            top: \"\".concat(30 + i * 10, \"%\")\n                        },\n                        animate: {\n                            y: [\n                                0,\n                                -20,\n                                0\n                            ],\n                            opacity: [\n                                0.3,\n                                0.8,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 3 + i * 0.5,\n                            repeat: Infinity,\n                            ease: 'easeInOut'\n                        }\n                    }, i, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center max-w-6xl mx-auto px-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        ref: titleRef,\n                        className: \"font-clash text-6xl md:text-8xl lg:text-9xl font-bold leading-none mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block\",\n                                children: \"Creative\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block text-primary-green\",\n                                children: \"Developer\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        ref: subtitleRef,\n                        className: \"font-inter text-lg md:text-xl text-primary-black/70 max-w-2xl mx-auto leading-relaxed\",\n                        children: \"Crafting award-winning digital experiences with cutting-edge technology and innovative design solutions.\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                        className: \"mt-12 px-8 py-4 bg-primary-black text-primary-neutral rounded-full font-medium hover:bg-primary-green transition-colors duration-300\",\n                        whileHover: {\n                            scale: 1.05\n                        },\n                        whileTap: {\n                            scale: 0.95\n                        },\n                        \"data-cursor\": \"pointer\",\n                        children: \"Explore My Work\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    className: \"w-6 h-10 border-2 border-primary-black rounded-full flex justify-center\",\n                    animate: {\n                        opacity: [\n                            1,\n                            0.3,\n                            1\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"w-1 h-3 bg-primary-black rounded-full mt-2\",\n                        animate: {\n                            y: [\n                                0,\n                                12,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeroSection, \"bs9/FbJta6GpI8cYfXwlPKMar1U=\");\n_c = HeroSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroSection);\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/HeroSection.tsx\n"));

/***/ })

});