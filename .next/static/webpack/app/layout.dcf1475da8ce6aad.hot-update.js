"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"77ea9494f51e\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzdlYTk0OTRmNTFlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/ThemeToggle.tsx":
/*!*******************************************!*\
  !*** ./src/components/ui/ThemeToggle.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst ThemeToggle = ()=>{\n    _s();\n    const { theme, toggleTheme } = (0,_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeToggle.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ThemeToggle.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n        onClick: toggleTheme,\n        className: \"fixed top-6 right-6 z-50 w-12 h-12 bg-primary-black dark:bg-primary-neutral rounded-full flex items-center justify-center text-primary-neutral dark:text-primary-black transition-colors duration-300\",\n        whileHover: {\n            scale: 1.1\n        },\n        whileTap: {\n            scale: 0.9\n        },\n        \"data-cursor\": \"pointer\",\n        \"aria-label\": \"Toggle theme\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            initial: false,\n            animate: {\n                rotate: theme === 'dark' ? 180 : 0\n            },\n            transition: {\n                duration: 0.3\n            },\n            children: theme === 'light' ? // Moon icon\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ThemeToggle.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ThemeToggle.tsx\",\n                lineNumber: 35,\n                columnNumber: 11\n            }, undefined) : // Sun icon\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                width: \"20\",\n                height: \"20\",\n                viewBox: \"0 0 24 24\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"5\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ThemeToggle.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ThemeToggle.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ThemeToggle.tsx\",\n                lineNumber: 49,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ThemeToggle.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ThemeToggle.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ThemeToggle, \"iaaoD1LWaTNugaH6cPmI/NfxUYo=\", false, function() {\n    return [\n        _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c = ThemeToggle;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ThemeToggle);\nvar _c;\n$RefreshReg$(_c, \"ThemeToggle\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ThemeToggle.tsx\n"));

/***/ })

});