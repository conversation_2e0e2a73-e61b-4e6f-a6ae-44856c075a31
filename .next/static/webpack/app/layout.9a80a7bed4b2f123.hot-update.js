/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/globals.css */ \"(app-pages-browser)/./src/app/globals.css\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/ClientLayout.tsx */ \"(app-pages-browser)/./src/components/layout/ClientLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ThemeProvider.tsx */ \"(app-pages-browser)/./src/components/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/seo/GTMNoScript.tsx */ \"(app-pages-browser)/./src/components/seo/GTMNoScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/seo/MetaHead.tsx */ \"(app-pages-browser)/./src/components/seo/MetaHead.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"35da0bcfa018\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzVkYTBiY2ZhMDE4XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/seo/GTMNoScript.tsx":
/*!********************************************!*\
  !*** ./src/components/seo/GTMNoScript.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GTMNoScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GTMNoScript() {\n    _s();\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GTMNoScript.useEffect\": ()=>{\n            const loadAnalytics = {\n                \"GTMNoScript.useEffect.loadAnalytics\": async ()=>{\n                    try {\n                        const analyticsData = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.fetchAnalyticsSettings)();\n                        setAnalytics(analyticsData);\n                    } catch (error) {\n                        console.error('Failed to load analytics settings:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"GTMNoScript.useEffect.loadAnalytics\"];\n            loadAnalytics();\n        }\n    }[\"GTMNoScript.useEffect\"], []);\n    if (loading || !analytics.google_tag_manager_id) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n            src: \"https://www.googletagmanager.com/ns.html?id=\".concat(analytics.google_tag_manager_id),\n            height: \"0\",\n            width: \"0\",\n            style: {\n                display: 'none',\n                visibility: 'hidden'\n            }\n        }, void 0, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/GTMNoScript.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/GTMNoScript.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n_s(GTMNoScript, \"EwscKDEid7qLV9cbFsi5Mv4D080=\");\n_c = GTMNoScript;\nvar _c;\n$RefreshReg$(_c, \"GTMNoScript\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/seo/GTMNoScript.tsx\n"));

/***/ })

});