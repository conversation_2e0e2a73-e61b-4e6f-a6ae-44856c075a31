"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/HeroSection */ \"(app-pages-browser)/./src/components/sections/HeroSection.tsx\");\n/* harmony import */ var _components_sections_ProjectsSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/ProjectsSection */ \"(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\");\n/* harmony import */ var _components_sections_SkillsSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/SkillsSection */ \"(app-pages-browser)/./src/components/sections/SkillsSection.tsx\");\n/* harmony import */ var _components_sections_AboutSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/AboutSection */ \"(app-pages-browser)/./src/components/sections/AboutSection.tsx\");\n/* harmony import */ var _components_sections_ContactSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/ContactSection */ \"(app-pages-browser)/./src/components/sections/ContactSection.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_ui_CustomCursor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomCursor */ \"(app-pages-browser)/./src/components/ui/CustomCursor.tsx\");\n/* harmony import */ var _components_ui_ScrollProgress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ScrollProgress */ \"(app-pages-browser)/./src/components/ui/ScrollProgress.tsx\");\n/* harmony import */ var _lib_smoothScroll__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/smoothScroll */ \"(app-pages-browser)/./src/lib/smoothScroll.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Initialize smooth scroll\n            const cleanup = (0,_lib_smoothScroll__WEBPACK_IMPORTED_MODULE_10__.initSmoothScroll)();\n            return cleanup;\n        }\n    }[\"Home.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomCursor__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollProgress__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_ProjectsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_SkillsSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_AboutSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_ContactSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQzBCO0FBQ1E7QUFDSjtBQUNGO0FBQ0k7QUFDbEI7QUFDUTtBQUNJO0FBRU47QUFFdEMsU0FBU1U7O0lBQ3RCVixnREFBU0E7MEJBQUM7WUFDUiwyQkFBMkI7WUFDM0IsTUFBTVcsVUFBVUYsb0VBQWdCQTtZQUVoQyxPQUFPRTtRQUNUO3lCQUFHLEVBQUU7SUFFTCxxQkFDRSw4REFBQ0M7UUFBS0MsV0FBVTs7MEJBQ2QsOERBQUNOLG1FQUFZQTs7Ozs7MEJBQ2IsOERBQUNDLHFFQUFjQTs7Ozs7MEJBRWYsOERBQUNQLHdFQUFXQTs7Ozs7MEJBQ1osOERBQUNDLDRFQUFlQTs7Ozs7MEJBQ2hCLDhEQUFDQywwRUFBYUE7Ozs7OzBCQUNkLDhEQUFDQyx5RUFBWUE7Ozs7OzBCQUNiLDhEQUFDQywyRUFBY0E7Ozs7OzBCQUNmLDhEQUFDQyxpRUFBTUE7Ozs7Ozs7Ozs7O0FBR2I7R0FyQndCSTtLQUFBQSIsInNvdXJjZXMiOlsiL0FwcGxpY2F0aW9ucy9YQU1QUC94YW1wcGZpbGVzL2h0ZG9jcy9wb3J0Zm9saW8vc3JjL2FwcC9wYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgSGVyb1NlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL3NlY3Rpb25zL0hlcm9TZWN0aW9uJ1xuaW1wb3J0IFByb2plY3RzU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvc2VjdGlvbnMvUHJvamVjdHNTZWN0aW9uJ1xuaW1wb3J0IFNraWxsc1NlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL3NlY3Rpb25zL1NraWxsc1NlY3Rpb24nXG5pbXBvcnQgQWJvdXRTZWN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9zZWN0aW9ucy9BYm91dFNlY3Rpb24nXG5pbXBvcnQgQ29udGFjdFNlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL3NlY3Rpb25zL0NvbnRhY3RTZWN0aW9uJ1xuaW1wb3J0IEZvb3RlciBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L0Zvb3RlcidcbmltcG9ydCBDdXN0b21DdXJzb3IgZnJvbSAnQC9jb21wb25lbnRzL3VpL0N1c3RvbUN1cnNvcidcbmltcG9ydCBTY3JvbGxQcm9ncmVzcyBmcm9tICdAL2NvbXBvbmVudHMvdWkvU2Nyb2xsUHJvZ3Jlc3MnXG5pbXBvcnQgVGhlbWVUb2dnbGUgZnJvbSAnQC9jb21wb25lbnRzL3VpL1RoZW1lVG9nZ2xlJ1xuaW1wb3J0IHsgaW5pdFNtb290aFNjcm9sbCB9IGZyb20gJ0AvbGliL3Ntb290aFNjcm9sbCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBJbml0aWFsaXplIHNtb290aCBzY3JvbGxcbiAgICBjb25zdCBjbGVhbnVwID0gaW5pdFNtb290aFNjcm9sbCgpXG4gICAgXG4gICAgcmV0dXJuIGNsZWFudXBcbiAgfSwgW10pXG5cbiAgcmV0dXJuIChcbiAgICA8bWFpbiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgPEN1c3RvbUN1cnNvciAvPlxuICAgICAgPFNjcm9sbFByb2dyZXNzIC8+XG4gICAgICBcbiAgICAgIDxIZXJvU2VjdGlvbiAvPlxuICAgICAgPFByb2plY3RzU2VjdGlvbiAvPlxuICAgICAgPFNraWxsc1NlY3Rpb24gLz5cbiAgICAgIDxBYm91dFNlY3Rpb24gLz5cbiAgICAgIDxDb250YWN0U2VjdGlvbiAvPlxuICAgICAgPEZvb3RlciAvPlxuICAgIDwvbWFpbj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsIkhlcm9TZWN0aW9uIiwiUHJvamVjdHNTZWN0aW9uIiwiU2tpbGxzU2VjdGlvbiIsIkFib3V0U2VjdGlvbiIsIkNvbnRhY3RTZWN0aW9uIiwiRm9vdGVyIiwiQ3VzdG9tQ3Vyc29yIiwiU2Nyb2xsUHJvZ3Jlc3MiLCJpbml0U21vb3RoU2Nyb2xsIiwiSG9tZSIsImNsZWFudXAiLCJtYWluIiwiY2xhc3NOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});