"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAboutSection: () => (/* binding */ fetchAboutSection),\n/* harmony export */   fetchAnalyticsSettings: () => (/* binding */ fetchAnalyticsSettings),\n/* harmony export */   fetchContactInfo: () => (/* binding */ fetchContactInfo),\n/* harmony export */   fetchFeaturedProjects: () => (/* binding */ fetchFeaturedProjects),\n/* harmony export */   fetchHeroSection: () => (/* binding */ fetchHeroSection),\n/* harmony export */   fetchMetaTags: () => (/* binding */ fetchMetaTags),\n/* harmony export */   fetchProject: () => (/* binding */ fetchProject),\n/* harmony export */   fetchProjects: () => (/* binding */ fetchProjects),\n/* harmony export */   fetchSettingsByGroup: () => (/* binding */ fetchSettingsByGroup),\n/* harmony export */   fetchSiteSettings: () => (/* binding */ fetchSiteSettings),\n/* harmony export */   fetchSkillCategories: () => (/* binding */ fetchSkillCategories),\n/* harmony export */   fetchSkills: () => (/* binding */ fetchSkills),\n/* harmony export */   fetchSocialLinks: () => (/* binding */ fetchSocialLinks),\n/* harmony export */   fetchTimeline: () => (/* binding */ fetchTimeline),\n/* harmony export */   filterProjectsByType: () => (/* binding */ filterProjectsByType),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getAllTechnologies: () => (/* binding */ getAllTechnologies),\n/* harmony export */   getAssetUrl: () => (/* binding */ getAssetUrl),\n/* harmony export */   getProjectYear: () => (/* binding */ getProjectYear),\n/* harmony export */   getTechnologyCategories: () => (/* binding */ getTechnologyCategories),\n/* harmony export */   isFeaturedProject: () => (/* binding */ isFeaturedProject),\n/* harmony export */   searchProjects: () => (/* binding */ searchProjects),\n/* harmony export */   sortProjectsByDate: () => (/* binding */ sortProjectsByDate),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm)\n/* harmony export */ });\n// API utilities for fetching data from Laravel Admin Panel\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// Generic API fetch function\nasync function apiRequest(endpoint) {\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL).concat(endpoint), {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return result.data;\n    } catch (error) {\n        console.error(\"Error fetching \".concat(endpoint, \":\"), error);\n        throw error;\n    }\n}\n// Hero Section API\nasync function fetchHeroSection() {\n    try {\n        return await apiRequest('/hero');\n    } catch (error) {\n        console.error('Error fetching hero section:', error);\n        return null;\n    }\n}\n// Projects API\nasync function fetchProjects() {\n    try {\n        return await apiRequest('/projects');\n    } catch (error) {\n        console.error('Error fetching projects:', error);\n        return getFallbackProjects();\n    }\n}\nasync function fetchFeaturedProjects() {\n    try {\n        return await apiRequest('/projects/featured');\n    } catch (error) {\n        console.error('Error fetching featured projects:', error);\n        return getFallbackProjects().slice(0, 3);\n    }\n}\nasync function fetchProject(slug) {\n    try {\n        return await apiRequest(\"/projects/\".concat(slug));\n    } catch (error) {\n        console.error(\"Error fetching project \".concat(slug, \":\"), error);\n        return null;\n    }\n}\n// About Section API\nasync function fetchAboutSection() {\n    try {\n        return await apiRequest('/about');\n    } catch (error) {\n        console.error('Error fetching about section:', error);\n        return null;\n    }\n}\nasync function fetchTimeline() {\n    try {\n        return await apiRequest('/timeline');\n    } catch (error) {\n        console.error('Error fetching timeline:', error);\n        return [];\n    }\n}\n// Skills API\nasync function fetchSkills() {\n    try {\n        return await apiRequest('/skills');\n    } catch (error) {\n        console.error('Error fetching skills:', error);\n        return [];\n    }\n}\nasync function fetchSkillCategories() {\n    try {\n        return await apiRequest('/skills/categories');\n    } catch (error) {\n        console.error('Error fetching skill categories:', error);\n        return [];\n    }\n}\n// Contact API\nasync function fetchContactInfo() {\n    try {\n        return await apiRequest('/contact');\n    } catch (error) {\n        console.error('Error fetching contact info:', error);\n        return null;\n    }\n}\nasync function fetchSocialLinks() {\n    try {\n        return await apiRequest('/social-links');\n    } catch (error) {\n        console.error('Error fetching social links:', error);\n        return [];\n    }\n}\nasync function submitContactForm(data) {\n    try {\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/contact\"), {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"HTTP error! status: \".concat(response.status));\n        }\n        const result = await response.json();\n        return result.data;\n    } catch (error) {\n        console.error('Error submitting contact form:', error);\n        throw error;\n    }\n}\n// Settings API\nasync function fetchSiteSettings() {\n    try {\n        return await apiRequest('/settings');\n    } catch (error) {\n        console.error('Error fetching site settings:', error);\n        return {};\n    }\n}\nasync function fetchSettingsByGroup(group) {\n    try {\n        return await apiRequest(\"/settings/group/\".concat(group));\n    } catch (error) {\n        console.error(\"Error fetching settings for group \".concat(group, \":\"), error);\n        return {};\n    }\n}\nasync function fetchMetaTags() {\n    try {\n        return await apiRequest('/meta-tags');\n    } catch (error) {\n        console.error('Error fetching meta tags:', error);\n        return {};\n    }\n}\nasync function fetchAnalyticsSettings() {\n    try {\n        return await apiRequest('/analytics');\n    } catch (error) {\n        console.error('Error fetching analytics settings:', error);\n        return {};\n    }\n}\n// Fallback projects data (matching Laravel API structure)\nfunction getFallbackProjects() {\n    return [\n        {\n            id: 1,\n            title: 'E-commerce Platform',\n            slug: 'e-commerce-platform',\n            description: 'A modern e-commerce platform built with Laravel backend and React frontend, featuring secure payment processing, inventory management, and real-time analytics.',\n            short_description: 'A modern e-commerce platform with Laravel backend and React frontend.',\n            featured_image: '/images/project1.jpg',\n            gallery_images: [\n                '/images/project1-1.jpg',\n                '/images/project1-2.jpg'\n            ],\n            project_video: null,\n            project_type: 'featured',\n            live_url: 'https://example.com',\n            github_url: 'https://github.com/username/project1',\n            client: 'Tech Startup Inc.',\n            completion_date: '2024-06-15T00:00:00.000000Z',\n            technologies: [\n                {\n                    id: 1,\n                    name: 'Laravel',\n                    icon: null,\n                    color: '#FF2D20',\n                    category: 'backend'\n                },\n                {\n                    id: 2,\n                    name: 'React',\n                    icon: null,\n                    color: '#61DAFB',\n                    category: 'frontend'\n                },\n                {\n                    id: 3,\n                    name: 'MySQL',\n                    icon: null,\n                    color: '#4479A1',\n                    category: 'database'\n                },\n                {\n                    id: 4,\n                    name: 'Tailwind CSS',\n                    icon: null,\n                    color: '#06B6D4',\n                    category: 'frontend'\n                }\n            ],\n            created_at: '2024-01-15T00:00:00.000000Z',\n            updated_at: '2024-06-15T00:00:00.000000Z'\n        },\n        {\n            id: 2,\n            title: 'Task Management System',\n            slug: 'task-management-system',\n            description: 'A comprehensive task management system with team collaboration features, real-time updates, and advanced project tracking capabilities.',\n            short_description: 'A comprehensive task management system with team collaboration features.',\n            featured_image: '/images/project2.jpg',\n            gallery_images: [\n                '/images/project2-1.jpg',\n                '/images/project2-2.jpg'\n            ],\n            project_video: null,\n            project_type: 'normal',\n            live_url: 'https://tasks.example.com',\n            github_url: 'https://github.com/username/task-manager',\n            client: 'Remote Team Solutions',\n            completion_date: '2024-03-20T00:00:00.000000Z',\n            technologies: [\n                {\n                    id: 5,\n                    name: 'Vue.js',\n                    icon: null,\n                    color: '#4FC08D',\n                    category: 'frontend'\n                },\n                {\n                    id: 6,\n                    name: 'Node.js',\n                    icon: null,\n                    color: '#339933',\n                    category: 'backend'\n                },\n                {\n                    id: 7,\n                    name: 'MongoDB',\n                    icon: null,\n                    color: '#47A248',\n                    category: 'database'\n                },\n                {\n                    id: 8,\n                    name: 'Socket.io',\n                    icon: null,\n                    color: '#010101',\n                    category: 'realtime'\n                }\n            ],\n            created_at: '2024-01-10T00:00:00.000000Z',\n            updated_at: '2024-03-20T00:00:00.000000Z'\n        }\n    ];\n}\n// Utility functions for working with API data\n// Get unique technology categories from projects\nfunction getTechnologyCategories(projects) {\n    const categories = projects.flatMap((project)=>project.technologies.map((tech)=>tech.category || 'other'));\n    return Array.from(new Set(categories));\n}\n// Filter projects by type\nfunction filterProjectsByType(projects, type) {\n    if (type === 'all') return projects;\n    return projects.filter((project)=>project.project_type === type);\n}\n// Sort projects by completion date (newest first)\nfunction sortProjectsByDate(projects) {\n    return [\n        ...projects\n    ].sort((a, b)=>{\n        const dateA = new Date(a.completion_date || a.created_at);\n        const dateB = new Date(b.completion_date || b.created_at);\n        return dateB.getTime() - dateA.getTime();\n    });\n}\n// Get all technologies from projects\nfunction getAllTechnologies(projects) {\n    const allTechs = projects.flatMap((project)=>project.technologies);\n    const uniqueTechs = allTechs.filter((tech, index, self)=>index === self.findIndex((t)=>t.id === tech.id));\n    return uniqueTechs;\n}\n// Format date for display\nfunction formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\n// Get asset URL (handle both relative and absolute URLs)\nfunction getAssetUrl(path) {\n    var _process_env_NEXT_PUBLIC_API_URL;\n    if (!path) return null;\n    if (path.startsWith('http')) return path;\n    return \"\".concat(((_process_env_NEXT_PUBLIC_API_URL = \"http://localhost:8000/api/v1\") === null || _process_env_NEXT_PUBLIC_API_URL === void 0 ? void 0 : _process_env_NEXT_PUBLIC_API_URL.replace('/api/v1', '')) || 'http://localhost:8000', \"/storage/\").concat(path);\n}\n// Check if project is featured\nfunction isFeaturedProject(project) {\n    return project.project_type === 'featured';\n}\n// Get project completion year\nfunction getProjectYear(project) {\n    const date = new Date(project.completion_date || project.created_at);\n    return date.getFullYear();\n}\n// Search projects by title or description\nfunction searchProjects(projects, query) {\n    const lowercaseQuery = query.toLowerCase();\n    return projects.filter((project)=>{\n        var _project_short_description;\n        return project.title.toLowerCase().includes(lowercaseQuery) || project.description.toLowerCase().includes(lowercaseQuery) || ((_project_short_description = project.short_description) === null || _project_short_description === void 0 ? void 0 : _project_short_description.toLowerCase().includes(lowercaseQuery)) || project.technologies.some((tech)=>tech.name.toLowerCase().includes(lowercaseQuery));\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});