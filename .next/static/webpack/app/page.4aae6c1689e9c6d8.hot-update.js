"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ProjectsSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/ProjectsSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_5__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger);\nconst ProjectsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            if (!section) return;\n            // Animate section on scroll\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo(section.querySelectorAll('.project-card'), {\n                opacity: 0,\n                y: 100\n            }, {\n                opacity: 1,\n                y: 0,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: section,\n                    start: 'top 80%',\n                    end: 'bottom 20%',\n                    toggleActions: 'play none none reverse'\n                }\n            });\n            return ({\n                \"ProjectsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger.getAll().forEach({\n                        \"ProjectsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ProjectsSection.useEffect\"]);\n                }\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            size: \"xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    variant: \"h2\",\n                    font: \"clash\",\n                    weight: \"bold\",\n                    className: \"text-center mb-16\",\n                    children: [\n                        \"Featured \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary-green\",\n                            children: \"Projects\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 20\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((project)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"project-card bg-primary-neutral\",\n                            variant: \"default\",\n                            padding: \"md\",\n                            rounded: \"xl\",\n                            hover: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video bg-primary-peach/20 rounded-lg mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h5\",\n                                    font: \"satoshi\",\n                                    weight: \"semibold\",\n                                    className: \"mb-2\",\n                                    children: [\n                                        \"Project \",\n                                        project\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"caption\",\n                                    color: \"muted\",\n                                    children: \"A stunning web application built with modern technologies.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, project, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectsSection, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = ProjectsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectsSection);\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\n"));

/***/ })

});