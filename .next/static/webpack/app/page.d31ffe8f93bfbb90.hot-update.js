"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/HeroSection.tsx":
/*!*************************************************!*\
  !*** ./src/components/sections/HeroSection.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_5__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger);\nconst HeroSection = ()=>{\n    _s();\n    const heroRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const subtitleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const backgroundRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            const hero = heroRef.current;\n            const title = titleRef.current;\n            const subtitle = subtitleRef.current;\n            const background = backgroundRef.current;\n            if (!hero || !title || !subtitle || !background) return;\n            // Initial animation timeline\n            const tl = gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.timeline();\n            // Set initial states\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.set([\n                title,\n                subtitle\n            ], {\n                opacity: 0,\n                y: 100,\n                rotationX: 45\n            });\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.set(background, {\n                scale: 1.2,\n                opacity: 0,\n                rotation: 5\n            });\n            // Animate in with more sophisticated effects\n            tl.to(background, {\n                opacity: 0.15,\n                scale: 1,\n                rotation: 0,\n                duration: 2.5,\n                ease: 'power3.out'\n            }).to(title, {\n                opacity: 1,\n                y: 0,\n                rotationX: 0,\n                duration: 1.5,\n                ease: 'back.out(1.7)',\n                stagger: 0.1\n            }, '-=2').to(subtitle, {\n                opacity: 1,\n                y: 0,\n                rotationX: 0,\n                duration: 1.2,\n                ease: 'power3.out'\n            }, '-=1');\n            // Enhanced mouse move parallax effect\n            const handleMouseMove = {\n                \"HeroSection.useEffect.handleMouseMove\": (e)=>{\n                    const { clientX, clientY } = e;\n                    const { innerWidth, innerHeight } = window;\n                    const xPos = (clientX / innerWidth - 0.5) * 2;\n                    const yPos = (clientY / innerHeight - 0.5) * 2;\n                    // Multi-layer parallax\n                    gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(background, {\n                        x: xPos * 30,\n                        y: yPos * 30,\n                        rotation: xPos * 2,\n                        duration: 0.8,\n                        ease: 'power2.out'\n                    });\n                    gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(title, {\n                        x: xPos * 15,\n                        y: yPos * 15,\n                        rotationY: xPos * 5,\n                        duration: 0.5,\n                        ease: 'power2.out'\n                    });\n                    gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(subtitle, {\n                        x: xPos * 8,\n                        y: yPos * 8,\n                        duration: 0.6,\n                        ease: 'power2.out'\n                    });\n                }\n            }[\"HeroSection.useEffect.handleMouseMove\"];\n            // Scroll-triggered animations\n            gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger.create({\n                trigger: hero,\n                start: 'top top',\n                end: 'bottom top',\n                scrub: 1,\n                onUpdate: {\n                    \"HeroSection.useEffect\": (self)=>{\n                        const progress = self.progress;\n                        gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(title, {\n                            y: progress * -100,\n                            opacity: 1 - progress * 0.5,\n                            duration: 0.3\n                        });\n                        gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.to(subtitle, {\n                            y: progress * -50,\n                            opacity: 1 - progress * 0.8,\n                            duration: 0.3\n                        });\n                    }\n                }[\"HeroSection.useEffect\"]\n            });\n            hero.addEventListener('mousemove', handleMouseMove);\n            return ({\n                \"HeroSection.useEffect\": ()=>{\n                    hero.removeEventListener('mousemove', handleMouseMove);\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger.getAll().forEach({\n                        \"HeroSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"HeroSection.useEffect\"]);\n                }\n            })[\"HeroSection.useEffect\"];\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: heroRef,\n        className: \"relative h-screen flex items-center justify-center overflow-hidden bg-primary-neutral\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: backgroundRef,\n                className: \"absolute inset-0 bg-gradient-to-br from-primary-peach/20 via-primary-green/10 to-primary-black/5\"\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    [\n                        ...Array(8)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"absolute bg-primary-peach rounded-full\",\n                            style: {\n                                width: \"\".concat(4 + i % 3 * 2, \"px\"),\n                                height: \"\".concat(4 + i % 3 * 2, \"px\"),\n                                left: \"\".concat(15 + i * 12, \"%\"),\n                                top: \"\".concat(25 + i % 4 * 15, \"%\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -30 - i * 5,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    Math.sin(i) * 10,\n                                    0\n                                ],\n                                opacity: [\n                                    0.2,\n                                    0.8,\n                                    0.2\n                                ],\n                                scale: [\n                                    1,\n                                    1.2,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 4 + i * 0.3,\n                                repeat: Infinity,\n                                ease: 'easeInOut',\n                                delay: i * 0.2\n                            }\n                        }, \"dot-\".concat(i), false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"absolute border border-primary-green/30\",\n                            style: {\n                                width: \"\".concat(20 + i * 10, \"px\"),\n                                height: \"\".concat(20 + i * 10, \"px\"),\n                                left: \"\".concat(70 + i * 8, \"%\"),\n                                top: \"\".concat(20 + i * 20, \"%\"),\n                                borderRadius: i % 2 === 0 ? '50%' : '0%'\n                            },\n                            animate: {\n                                rotate: [\n                                    0,\n                                    360\n                                ],\n                                scale: [\n                                    1,\n                                    1.1,\n                                    1\n                                ],\n                                opacity: [\n                                    0.1,\n                                    0.3,\n                                    0.1\n                                ]\n                            },\n                            transition: {\n                                duration: 8 + i * 2,\n                                repeat: Infinity,\n                                ease: 'linear',\n                                delay: i * 0.5\n                            }\n                        }, \"shape-\".concat(i), false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, undefined)),\n                    [\n                        ...Array(3)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            className: \"absolute rounded-full blur-sm\",\n                            style: {\n                                width: \"\".concat(60 + i * 20, \"px\"),\n                                height: \"\".concat(60 + i * 20, \"px\"),\n                                left: \"\".concat(10 + i * 30, \"%\"),\n                                top: \"\".concat(60 + i * 10, \"%\"),\n                                background: \"radial-gradient(circle, \".concat(i === 0 ? '#fecf8b20' : i === 1 ? '#45523e15' : '#01010110', \", transparent)\")\n                            },\n                            animate: {\n                                y: [\n                                    0,\n                                    -20,\n                                    0\n                                ],\n                                x: [\n                                    0,\n                                    10,\n                                    0\n                                ],\n                                scale: [\n                                    1,\n                                    1.05,\n                                    1\n                                ]\n                            },\n                            transition: {\n                                duration: 6 + i * 1.5,\n                                repeat: Infinity,\n                                ease: 'easeInOut',\n                                delay: i * 1\n                            }\n                        }, \"orb-\".concat(i), false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"h1\",\n                            font: \"clash\",\n                            weight: \"bold\",\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block\",\n                                    children: \"Creative\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"block text-primary-green\",\n                                    children: \"Developer\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: subtitleRef,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            variant: \"body\",\n                            font: \"inter\",\n                            color: \"muted\",\n                            className: \"max-w-2xl mx-auto mb-12\",\n                            children: \"Crafting award-winning digital experiences with cutting-edge technology and innovative design solutions.\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        variant: \"primary\",\n                        size: \"lg\",\n                        className: \"mt-4\",\n                        children: \"Explore My Work\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    className: \"w-6 h-10 border-2 border-primary-black rounded-full flex justify-center\",\n                    animate: {\n                        opacity: [\n                            1,\n                            0.3,\n                            1\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        className: \"w-1 h-3 bg-primary-black rounded-full mt-2\",\n                        animate: {\n                            y: [\n                                0,\n                                12,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/HeroSection.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\n_s(HeroSection, \"bs9/FbJta6GpI8cYfXwlPKMar1U=\");\n_c = HeroSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HeroSection);\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/HeroSection.tsx\n"));

/***/ })

});