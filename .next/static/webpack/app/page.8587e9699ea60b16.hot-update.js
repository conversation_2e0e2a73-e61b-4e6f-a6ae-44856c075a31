"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/SimplePreloader.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/SimplePreloader.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst SimplePreloader = (param)=>{\n    let { onComplete, duration = 3000 } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isComplete, setIsComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimplePreloader.useEffect\": ()=>{\n            // Simple progress animation\n            const interval = setInterval({\n                \"SimplePreloader.useEffect.interval\": ()=>{\n                    setProgress({\n                        \"SimplePreloader.useEffect.interval\": (prev)=>{\n                            const newProgress = prev + 2;\n                            if (newProgress >= 100) {\n                                clearInterval(interval);\n                                setTimeout({\n                                    \"SimplePreloader.useEffect.interval\": ()=>{\n                                        setIsComplete(true);\n                                        setTimeout({\n                                            \"SimplePreloader.useEffect.interval\": ()=>{\n                                                onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                                            }\n                                        }[\"SimplePreloader.useEffect.interval\"], 500);\n                                    }\n                                }[\"SimplePreloader.useEffect.interval\"], 300);\n                                return 100;\n                            }\n                            return newProgress;\n                        }\n                    }[\"SimplePreloader.useEffect.interval\"]);\n                }\n            }[\"SimplePreloader.useEffect.interval\"], duration / 50);\n            return ({\n                \"SimplePreloader.useEffect\": ()=>clearInterval(interval)\n            })[\"SimplePreloader.useEffect\"];\n        }\n    }[\"SimplePreloader.useEffect\"], [\n        onComplete,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.AnimatePresence, {\n        children: !isComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n            className: \"fixed inset-0 z-[9999] bg-white flex items-center justify-center\",\n            initial: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-bold text-black mb-2\",\n                                children: \"YN\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-black/60 text-sm tracking-widest\",\n                                children: \"CREATIVE DEVELOPER\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-orange-500 text-sm font-medium tracking-wider\",\n                            children: \"LOADING\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-64 mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full h-1 bg-gray-200 rounded\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full bg-orange-500 rounded transition-all duration-100\",\n                                style: {\n                                    width: \"\".concat(progress, \"%\")\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-black text-sm font-mono\",\n                        children: [\n                            progress,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n                lineNumber: 46,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n            lineNumber: 40,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SimplePreloader.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimplePreloader, \"aWKCuD/IFqH8AjkN5xDive3KztM=\");\n_c = SimplePreloader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SimplePreloader);\nvar _c;\n$RefreshReg$(_c, \"SimplePreloader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL1NpbXBsZVByZWxvYWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFMkM7QUFDWTtBQU92RCxNQUFNSSxrQkFBa0I7UUFBQyxFQUFFQyxVQUFVLEVBQUVDLFdBQVcsSUFBSSxFQUF3Qjs7SUFDNUUsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUdQLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ1EsWUFBWUMsY0FBYyxHQUFHVCwrQ0FBUUEsQ0FBQztJQUU3Q0QsZ0RBQVNBO3FDQUFDO1lBQ1IsNEJBQTRCO1lBQzVCLE1BQU1XLFdBQVdDO3NEQUFZO29CQUMzQko7OERBQVlLLENBQUFBOzRCQUNWLE1BQU1DLGNBQWNELE9BQU87NEJBQzNCLElBQUlDLGVBQWUsS0FBSztnQ0FDdEJDLGNBQWNKO2dDQUNkSzswRUFBVzt3Q0FDVE4sY0FBYzt3Q0FDZE07a0ZBQVc7Z0RBQ1RYLHVCQUFBQSxpQ0FBQUE7NENBQ0Y7aUZBQUc7b0NBQ0w7eUVBQUc7Z0NBQ0gsT0FBTzs0QkFDVDs0QkFDQSxPQUFPUzt3QkFDVDs7Z0JBQ0Y7cURBQUdSLFdBQVc7WUFFZDs2Q0FBTyxJQUFNUyxjQUFjSjs7UUFDN0I7b0NBQUc7UUFBQ047UUFBWUM7S0FBUztJQUV6QixxQkFDRSw4REFBQ0gsMERBQWVBO2tCQUNiLENBQUNNLDRCQUNBLDhEQUFDUCxpREFBTUEsQ0FBQ2UsR0FBRztZQUNUQyxXQUFVO1lBQ1ZDLFNBQVM7Z0JBQUVDLFNBQVM7WUFBRTtZQUN0QkMsTUFBTTtnQkFBRUQsU0FBUztZQUFFO1lBQ25CRSxZQUFZO2dCQUFFaEIsVUFBVTtZQUFJO3NCQUU1Qiw0RUFBQ1c7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNLO2dDQUFHTCxXQUFVOzBDQUFxQzs7Ozs7OzBDQUNuRCw4REFBQ007Z0NBQUVOLFdBQVU7MENBQXdDOzs7Ozs7Ozs7Ozs7a0NBSXZELDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ007NEJBQUVOLFdBQVU7c0NBQXFEOzs7Ozs7Ozs7OztrQ0FJcEUsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUNiLDRFQUFDRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQ0NDLFdBQVU7Z0NBQ1ZPLE9BQU87b0NBQUVDLE9BQU8sR0FBWSxPQUFUbkIsVUFBUztnQ0FBRzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNckMsOERBQUNVO3dCQUFJQyxXQUFVOzs0QkFDWlg7NEJBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT3hCO0dBbEVNSDtLQUFBQTtBQW9FTixpRUFBZUEsZUFBZUEsRUFBQSIsInNvdXJjZXMiOlsiL0FwcGxpY2F0aW9ucy9YQU1QUC94YW1wcGZpbGVzL2h0ZG9jcy9wb3J0Zm9saW8vc3JjL2NvbXBvbmVudHMvdWkvU2ltcGxlUHJlbG9hZGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJ1xuXG5pbnRlcmZhY2UgU2ltcGxlUHJlbG9hZGVyUHJvcHMge1xuICBvbkNvbXBsZXRlPzogKCkgPT4gdm9pZFxuICBkdXJhdGlvbj86IG51bWJlclxufVxuXG5jb25zdCBTaW1wbGVQcmVsb2FkZXIgPSAoeyBvbkNvbXBsZXRlLCBkdXJhdGlvbiA9IDMwMDAgfTogU2ltcGxlUHJlbG9hZGVyUHJvcHMpID0+IHtcbiAgY29uc3QgW3Byb2dyZXNzLCBzZXRQcm9ncmVzc10gPSB1c2VTdGF0ZSgwKVxuICBjb25zdCBbaXNDb21wbGV0ZSwgc2V0SXNDb21wbGV0ZV0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFNpbXBsZSBwcm9ncmVzcyBhbmltYXRpb25cbiAgICBjb25zdCBpbnRlcnZhbCA9IHNldEludGVydmFsKCgpID0+IHtcbiAgICAgIHNldFByb2dyZXNzKHByZXYgPT4ge1xuICAgICAgICBjb25zdCBuZXdQcm9ncmVzcyA9IHByZXYgKyAyXG4gICAgICAgIGlmIChuZXdQcm9ncmVzcyA+PSAxMDApIHtcbiAgICAgICAgICBjbGVhckludGVydmFsKGludGVydmFsKVxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgc2V0SXNDb21wbGV0ZSh0cnVlKVxuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgIG9uQ29tcGxldGU/LigpXG4gICAgICAgICAgICB9LCA1MDApXG4gICAgICAgICAgfSwgMzAwKVxuICAgICAgICAgIHJldHVybiAxMDBcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3UHJvZ3Jlc3NcbiAgICAgIH0pXG4gICAgfSwgZHVyYXRpb24gLyA1MClcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKVxuICB9LCBbb25Db21wbGV0ZSwgZHVyYXRpb25dKVxuXG4gIHJldHVybiAoXG4gICAgPEFuaW1hdGVQcmVzZW5jZT5cbiAgICAgIHshaXNDb21wbGV0ZSAmJiAoXG4gICAgICAgIDxtb3Rpb24uZGl2XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LVs5OTk5XSBiZy13aGl0ZSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiXG4gICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgICAgZXhpdD17eyBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgdHJhbnNpdGlvbj17eyBkdXJhdGlvbjogMC41IH19XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICB7LyogTG9nbyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItOFwiPlxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC02eGwgZm9udC1ib2xkIHRleHQtYmxhY2sgbWItMlwiPllOPC9oMT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibGFjay82MCB0ZXh0LXNtIHRyYWNraW5nLXdpZGVzdFwiPkNSRUFUSVZFIERFVkVMT1BFUjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogTG9hZGluZyB0ZXh0ICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtb3JhbmdlLTUwMCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYWNraW5nLXdpZGVyXCI+TE9BRElORzwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUHJvZ3Jlc3MgYmFyICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTY0IG14LWF1dG8gbWItNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLTEgYmctZ3JheS0yMDAgcm91bmRlZFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLWZ1bGwgYmctb3JhbmdlLTUwMCByb3VuZGVkIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTEwMFwiXG4gICAgICAgICAgICAgICAgICBzdHlsZT17eyB3aWR0aDogYCR7cHJvZ3Jlc3N9JWAgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQ291bnRlciAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ibGFjayB0ZXh0LXNtIGZvbnQtbW9ub1wiPlxuICAgICAgICAgICAgICB7cHJvZ3Jlc3N9JVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICl9XG4gICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gIClcbn1cblxuZXhwb3J0IGRlZmF1bHQgU2ltcGxlUHJlbG9hZGVyXG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJTaW1wbGVQcmVsb2FkZXIiLCJvbkNvbXBsZXRlIiwiZHVyYXRpb24iLCJwcm9ncmVzcyIsInNldFByb2dyZXNzIiwiaXNDb21wbGV0ZSIsInNldElzQ29tcGxldGUiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwicHJldiIsIm5ld1Byb2dyZXNzIiwiY2xlYXJJbnRlcnZhbCIsInNldFRpbWVvdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJpbml0aWFsIiwib3BhY2l0eSIsImV4aXQiLCJ0cmFuc2l0aW9uIiwiaDEiLCJwIiwic3R5bGUiLCJ3aWR0aCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SimplePreloader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/SmartPreloader.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/SmartPreloader.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AwwardsPreloader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AwwardsPreloader */ \"(app-pages-browser)/./src/components/ui/AwwardsPreloader.tsx\");\n/* harmony import */ var _MinimalPreloader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MinimalPreloader */ \"(app-pages-browser)/./src/components/ui/MinimalPreloader.tsx\");\n/* harmony import */ var _SimplePreloader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SimplePreloader */ \"(app-pages-browser)/./src/components/ui/SimplePreloader.tsx\");\n/* harmony import */ var _config_preloader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/preloader */ \"(app-pages-browser)/./src/config/preloader.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SmartPreloader = (param)=>{\n    let { onComplete } = param;\n    _s();\n    const [shouldShow, setShouldShow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartPreloader.useEffect\": ()=>{\n            // Check if preloader is enabled\n            if (!_config_preloader__WEBPACK_IMPORTED_MODULE_5__[\"default\"].enabled) {\n                onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                return;\n            }\n            // Check if should show only on first visit\n            if (_config_preloader__WEBPACK_IMPORTED_MODULE_5__[\"default\"].showOnlyOnFirstVisit) {\n                const hasVisited = localStorage.getItem('hasVisited');\n                if (hasVisited) {\n                    onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                    return;\n                } else {\n                    localStorage.setItem('hasVisited', 'true');\n                }\n            }\n            setShouldShow(true);\n        }\n    }[\"SmartPreloader.useEffect\"], [\n        onComplete\n    ]);\n    const handleComplete = ()=>{\n        setShouldShow(false);\n        onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n    };\n    if (!shouldShow) {\n        return null;\n    }\n    // Render based on configuration\n    switch(_config_preloader__WEBPACK_IMPORTED_MODULE_5__[\"default\"].style){\n        case 'awwwards':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AwwardsPreloader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onComplete: handleComplete,\n                duration: _config_preloader__WEBPACK_IMPORTED_MODULE_5__[\"default\"].duration\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SmartPreloader.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined);\n        case 'minimal':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MinimalPreloader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onComplete: handleComplete,\n                duration: _config_preloader__WEBPACK_IMPORTED_MODULE_5__[\"default\"].duration\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SmartPreloader.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, undefined);\n        case 'simple':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimplePreloader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                onComplete: handleComplete,\n                duration: _config_preloader__WEBPACK_IMPORTED_MODULE_5__[\"default\"].duration\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SmartPreloader.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined);\n        case 'disabled':\n        default:\n            return null;\n    }\n};\n_s(SmartPreloader, \"3BwMrp5cygsfSyEVOwpl2OS+lw0=\");\n_c = SmartPreloader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SmartPreloader);\nvar _c;\n$RefreshReg$(_c, \"SmartPreloader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SmartPreloader.tsx\n"));

/***/ })

});