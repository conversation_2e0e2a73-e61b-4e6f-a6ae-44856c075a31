"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nconst AboutSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            if (!section) return;\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(section.querySelectorAll('.about-content > *'), {\n                opacity: 0,\n                y: 50\n            }, {\n                opacity: 1,\n                y: 0,\n                duration: 0.8,\n                stagger: 0.2,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: section,\n                    start: 'top 80%',\n                    toggleActions: 'play none none reverse'\n                }\n            });\n            return ({\n                \"AboutSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.getAll().forEach({\n                        \"AboutSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"AboutSection.useEffect\"]);\n                }\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 px-6 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"about-content text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"font-clash text-5xl md:text-6xl font-bold mb-8\",\n                        children: [\n                            \"About \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-primary-green\",\n                                children: \"Me\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-inter text-lg md:text-xl text-primary-black/80 leading-relaxed mb-8\",\n                        children: \"I'm a passionate developer who creates award-winning digital experiences. With expertise in modern web technologies and a keen eye for design, I bring ideas to life through code.\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-inter text-base text-primary-black/70 leading-relaxed mb-12\",\n                        children: \"My journey spans over 5 years of crafting innovative solutions for clients worldwide. I specialize in creating performant, accessible, and visually stunning web applications that push the boundaries of what's possible.\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-clash text-3xl font-bold text-primary-peach mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-primary-black/70\",\n                                        children: \"Projects Completed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 65,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-clash text-3xl font-bold text-primary-green mb-2\",\n                                        children: \"5+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-primary-black/70\",\n                                        children: \"Years Experience\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-clash text-3xl font-bold text-primary-black mb-2\",\n                                        children: \"15+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-primary-black/70\",\n                                        children: \"Happy Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AboutSection, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = AboutSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutSection);\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});