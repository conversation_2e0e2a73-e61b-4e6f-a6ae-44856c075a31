"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ui/SmartPreloader.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/SmartPreloader.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AwwardsPreloader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AwwardsPreloader */ \"(app-pages-browser)/./src/components/ui/AwwardsPreloader.tsx\");\n/* harmony import */ var _MinimalPreloader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MinimalPreloader */ \"(app-pages-browser)/./src/components/ui/MinimalPreloader.tsx\");\n/* harmony import */ var _config_preloader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/preloader */ \"(app-pages-browser)/./src/config/preloader.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst SmartPreloader = (param)=>{\n    let { onComplete } = param;\n    _s();\n    const [shouldShow, setShouldShow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SmartPreloader.useEffect\": ()=>{\n            // Check if preloader is enabled\n            if (!_config_preloader__WEBPACK_IMPORTED_MODULE_4__[\"default\"].enabled) {\n                onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                return;\n            }\n            // Check if should show only on first visit\n            if (_config_preloader__WEBPACK_IMPORTED_MODULE_4__[\"default\"].showOnlyOnFirstVisit) {\n                const hasVisited = localStorage.getItem('hasVisited');\n                if (hasVisited) {\n                    onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n                    return;\n                } else {\n                    localStorage.setItem('hasVisited', 'true');\n                }\n            }\n            setShouldShow(true);\n        }\n    }[\"SmartPreloader.useEffect\"], [\n        onComplete\n    ]);\n    const handleComplete = ()=>{\n        setShouldShow(false);\n        onComplete === null || onComplete === void 0 ? void 0 : onComplete();\n    };\n    if (!shouldShow) {\n        return null;\n    }\n    // Render based on configuration\n    switch(_config_preloader__WEBPACK_IMPORTED_MODULE_4__[\"default\"].style){\n        case 'awwwards':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AwwardsPreloader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                onComplete: handleComplete,\n                duration: _config_preloader__WEBPACK_IMPORTED_MODULE_4__[\"default\"].duration\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SmartPreloader.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, undefined);\n        case 'minimal':\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MinimalPreloader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onComplete: handleComplete,\n                duration: _config_preloader__WEBPACK_IMPORTED_MODULE_4__[\"default\"].duration\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/SmartPreloader.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, undefined);\n        case 'disabled':\n        default:\n            return null;\n    }\n};\n_s(SmartPreloader, \"3BwMrp5cygsfSyEVOwpl2OS+lw0=\");\n_c = SmartPreloader;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SmartPreloader);\nvar _c;\n$RefreshReg$(_c, \"SmartPreloader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/SmartPreloader.tsx\n"));

/***/ })

});