"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs ***!
  \************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PopChild: () => (/* binding */ PopChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* __next_internal_client_entry_do_not_use__ PopChild auto */ var _s = $RefreshSig$();\n\n\n\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */ class PopChildMeasure extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */ componentDidUpdate() {}\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild(param) {\n    let { children, isPresent } = param;\n    _s();\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const size = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n    });\n    const { nonce } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_2__.MotionConfigContext);\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */ (0,react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect)({\n        \"PopChild.useInsertionEffect\": ()=>{\n            const { width, height, top, left } = size.current;\n            if (isPresent || !ref.current || !width || !height) return;\n            ref.current.dataset.motionPopId = id;\n            const style = document.createElement(\"style\");\n            if (nonce) style.nonce = nonce;\n            document.head.appendChild(style);\n            if (style.sheet) {\n                style.sheet.insertRule('\\n          [data-motion-pop-id=\"'.concat(id, '\"] {\\n            position: absolute !important;\\n            width: ').concat(width, \"px !important;\\n            height: \").concat(height, \"px !important;\\n            top: \").concat(top, \"px !important;\\n            left: \").concat(left, \"px !important;\\n          }\\n        \"));\n            }\n            return ({\n                \"PopChild.useInsertionEffect\": ()=>{\n                    document.head.removeChild(style);\n                }\n            })[\"PopChild.useInsertionEffect\"];\n        }\n    }[\"PopChild.useInsertionEffect\"], [\n        isPresent\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PopChildMeasure, {\n        isPresent: isPresent,\n        childRef: ref,\n        sizeRef: size,\n        children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.cloneElement(children, {\n            ref\n        })\n    });\n}\n_s(PopChild, \"V7z789Ed2n0+HnmYCJ8kEL0I644=\", false, function() {\n    return [\n        react__WEBPACK_IMPORTED_MODULE_1__.useId,\n        react__WEBPACK_IMPORTED_MODULE_1__.useInsertionEffect\n    ];\n});\n_c = PopChild;\n\nvar _c;\n$RefreshReg$(_c, \"PopChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceChild: () => (/* binding */ PresenceChild)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./PopChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\");\n/* __next_internal_client_entry_do_not_use__ PresenceChild auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\nconst PresenceChild = (param)=>{\n    let { children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode } = param;\n    _s();\n    const presenceChildren = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant)(newChildrenMap);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    const memoizedOnExitComplete = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PresenceChild.useCallback[memoizedOnExitComplete]\": (childId)=>{\n            presenceChildren.set(childId, true);\n            for (const isComplete of presenceChildren.values()){\n                if (!isComplete) return; // can stop searching when any is incomplete\n            }\n            onExitComplete && onExitComplete();\n        }\n    }[\"PresenceChild.useCallback[memoizedOnExitComplete]\"], [\n        presenceChildren,\n        onExitComplete\n    ]);\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo[context]\": ()=>({\n                id,\n                initial,\n                isPresent,\n                custom,\n                onExitComplete: memoizedOnExitComplete,\n                register: ({\n                    \"PresenceChild.useMemo[context]\": (childId)=>{\n                        presenceChildren.set(childId, false);\n                        return ({\n                            \"PresenceChild.useMemo[context]\": ()=>presenceChildren.delete(childId)\n                        })[\"PresenceChild.useMemo[context]\"];\n                    }\n                })[\"PresenceChild.useMemo[context]\"]\n            })\n    }[\"PresenceChild.useMemo[context]\"], /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */ presenceAffectsLayout ? [\n        Math.random(),\n        memoizedOnExitComplete\n    ] : [\n        isPresent,\n        memoizedOnExitComplete\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"PresenceChild.useMemo\": ()=>{\n            presenceChildren.forEach({\n                \"PresenceChild.useMemo\": (_, key)=>presenceChildren.set(key, false)\n            }[\"PresenceChild.useMemo\"]);\n        }\n    }[\"PresenceChild.useMemo\"], [\n        isPresent\n    ]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */ react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"PresenceChild.useEffect\": ()=>{\n            !isPresent && !presenceChildren.size && onExitComplete && onExitComplete();\n        }\n    }[\"PresenceChild.useEffect\"], [\n        isPresent\n    ]);\n    if (mode === \"popLayout\") {\n        children = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PopChild_mjs__WEBPACK_IMPORTED_MODULE_3__.PopChild, {\n            isPresent: isPresent,\n            children: children\n        });\n    }\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_4__.PresenceContext.Provider, {\n        value: context,\n        children: children\n    });\n};\n_s(PresenceChild, \"LhgI4XeWm8AWCb6RH8VCWFcMTPs=\", false, function() {\n    return [\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_2__.useConstant,\n        react__WEBPACK_IMPORTED_MODULE_1__.useId\n    ];\n});\n_c = PresenceChild;\nfunction newChildrenMap() {\n    return new Map();\n}\n\nvar _c;\n$RefreshReg$(_c, \"PresenceChild\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvUHJlc2VuY2VDaGlsZC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUN3QztBQUNUO0FBQ3FCO0FBQ2dCO0FBQ1Q7QUFDakI7QUFFMUMsTUFBTVEsZ0JBQWdCO1FBQUMsRUFBRUMsUUFBUSxFQUFFQyxPQUFPLEVBQUVDLFNBQVMsRUFBRUMsY0FBYyxFQUFFQyxNQUFNLEVBQUVDLHFCQUFxQixFQUFFQyxJQUFJLEVBQUc7O0lBQ3pHLE1BQU1DLG1CQUFtQlYsb0VBQVdBLENBQUNXO0lBQ3JDLE1BQU1DLEtBQUtoQiw0Q0FBS0E7SUFDaEIsTUFBTWlCLHlCQUF5QmhCLGtEQUFXQTs2REFBQyxDQUFDaUI7WUFDeENKLGlCQUFpQkssR0FBRyxDQUFDRCxTQUFTO1lBQzlCLEtBQUssTUFBTUUsY0FBY04saUJBQWlCTyxNQUFNLEdBQUk7Z0JBQ2hELElBQUksQ0FBQ0QsWUFDRCxRQUFRLDRDQUE0QztZQUM1RDtZQUNBVixrQkFBa0JBO1FBQ3RCOzREQUFHO1FBQUNJO1FBQWtCSjtLQUFlO0lBQ3JDLE1BQU1ZLFVBQVVwQiw4Q0FBT0E7MENBQUMsSUFBTztnQkFDM0JjO2dCQUNBUjtnQkFDQUM7Z0JBQ0FFO2dCQUNBRCxnQkFBZ0JPO2dCQUNoQk0sUUFBUTtzREFBRSxDQUFDTDt3QkFDUEosaUJBQWlCSyxHQUFHLENBQUNELFNBQVM7d0JBQzlCOzhEQUFPLElBQU1KLGlCQUFpQlUsTUFBTSxDQUFDTjs7b0JBQ3pDOztZQUNKO3lDQUNBOzs7O0tBSUMsR0FDRE4sd0JBQ007UUFBQ2EsS0FBS0MsTUFBTTtRQUFJVDtLQUF1QixHQUN2QztRQUFDUjtRQUFXUTtLQUF1QjtJQUN6Q2YsOENBQU9BO2lDQUFDO1lBQ0pZLGlCQUFpQmEsT0FBTzt5Q0FBQyxDQUFDQyxHQUFHQyxNQUFRZixpQkFBaUJLLEdBQUcsQ0FBQ1UsS0FBSzs7UUFDbkU7Z0NBQUc7UUFBQ3BCO0tBQVU7SUFDZDs7O0tBR0MsR0FDRFYsNENBQWU7bUNBQUM7WUFDWixDQUFDVSxhQUNHLENBQUNLLGlCQUFpQmlCLElBQUksSUFDdEJyQixrQkFDQUE7UUFDUjtrQ0FBRztRQUFDRDtLQUFVO0lBQ2QsSUFBSUksU0FBUyxhQUFhO1FBQ3RCTixXQUFXVCxzREFBR0EsQ0FBQ08sbURBQVFBLEVBQUU7WUFBRUksV0FBV0E7WUFBV0YsVUFBVUE7UUFBUztJQUN4RTtJQUNBLE9BQVFULHNEQUFHQSxDQUFDSyx5RUFBZUEsQ0FBQzZCLFFBQVEsRUFBRTtRQUFFQyxPQUFPWDtRQUFTZixVQUFVQTtJQUFTO0FBQy9FO0dBL0NNRDs7UUFDdUJGLGdFQUFXQTtRQUN6Qkosd0NBQUtBOzs7S0FGZE07QUFnRE4sU0FBU1M7SUFDTCxPQUFPLElBQUltQjtBQUNmO0FBRXlCIiwic291cmNlcyI6WyIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL2NvbXBvbmVudHMvQW5pbWF0ZVByZXNlbmNlL1ByZXNlbmNlQ2hpbGQubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSAncmVhY3QvanN4LXJ1bnRpbWUnO1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlSWQsIHVzZUNhbGxiYWNrLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUHJlc2VuY2VDb250ZXh0IH0gZnJvbSAnLi4vLi4vY29udGV4dC9QcmVzZW5jZUNvbnRleHQubWpzJztcbmltcG9ydCB7IHVzZUNvbnN0YW50IH0gZnJvbSAnLi4vLi4vdXRpbHMvdXNlLWNvbnN0YW50Lm1qcyc7XG5pbXBvcnQgeyBQb3BDaGlsZCB9IGZyb20gJy4vUG9wQ2hpbGQubWpzJztcblxuY29uc3QgUHJlc2VuY2VDaGlsZCA9ICh7IGNoaWxkcmVuLCBpbml0aWFsLCBpc1ByZXNlbnQsIG9uRXhpdENvbXBsZXRlLCBjdXN0b20sIHByZXNlbmNlQWZmZWN0c0xheW91dCwgbW9kZSwgfSkgPT4ge1xuICAgIGNvbnN0IHByZXNlbmNlQ2hpbGRyZW4gPSB1c2VDb25zdGFudChuZXdDaGlsZHJlbk1hcCk7XG4gICAgY29uc3QgaWQgPSB1c2VJZCgpO1xuICAgIGNvbnN0IG1lbW9pemVkT25FeGl0Q29tcGxldGUgPSB1c2VDYWxsYmFjaygoY2hpbGRJZCkgPT4ge1xuICAgICAgICBwcmVzZW5jZUNoaWxkcmVuLnNldChjaGlsZElkLCB0cnVlKTtcbiAgICAgICAgZm9yIChjb25zdCBpc0NvbXBsZXRlIG9mIHByZXNlbmNlQ2hpbGRyZW4udmFsdWVzKCkpIHtcbiAgICAgICAgICAgIGlmICghaXNDb21wbGV0ZSlcbiAgICAgICAgICAgICAgICByZXR1cm47IC8vIGNhbiBzdG9wIHNlYXJjaGluZyB3aGVuIGFueSBpcyBpbmNvbXBsZXRlXG4gICAgICAgIH1cbiAgICAgICAgb25FeGl0Q29tcGxldGUgJiYgb25FeGl0Q29tcGxldGUoKTtcbiAgICB9LCBbcHJlc2VuY2VDaGlsZHJlbiwgb25FeGl0Q29tcGxldGVdKTtcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlTWVtbygoKSA9PiAoe1xuICAgICAgICBpZCxcbiAgICAgICAgaW5pdGlhbCxcbiAgICAgICAgaXNQcmVzZW50LFxuICAgICAgICBjdXN0b20sXG4gICAgICAgIG9uRXhpdENvbXBsZXRlOiBtZW1vaXplZE9uRXhpdENvbXBsZXRlLFxuICAgICAgICByZWdpc3RlcjogKGNoaWxkSWQpID0+IHtcbiAgICAgICAgICAgIHByZXNlbmNlQ2hpbGRyZW4uc2V0KGNoaWxkSWQsIGZhbHNlKTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiBwcmVzZW5jZUNoaWxkcmVuLmRlbGV0ZShjaGlsZElkKTtcbiAgICAgICAgfSxcbiAgICB9KSwgXG4gICAgLyoqXG4gICAgICogSWYgdGhlIHByZXNlbmNlIG9mIGEgY2hpbGQgYWZmZWN0cyB0aGUgbGF5b3V0IG9mIHRoZSBjb21wb25lbnRzIGFyb3VuZCBpdCxcbiAgICAgKiB3ZSB3YW50IHRvIG1ha2UgYSBuZXcgY29udGV4dCB2YWx1ZSB0byBlbnN1cmUgdGhleSBnZXQgcmUtcmVuZGVyZWRcbiAgICAgKiBzbyB0aGV5IGNhbiBkZXRlY3QgdGhhdCBsYXlvdXQgY2hhbmdlLlxuICAgICAqL1xuICAgIHByZXNlbmNlQWZmZWN0c0xheW91dFxuICAgICAgICA/IFtNYXRoLnJhbmRvbSgpLCBtZW1vaXplZE9uRXhpdENvbXBsZXRlXVxuICAgICAgICA6IFtpc1ByZXNlbnQsIG1lbW9pemVkT25FeGl0Q29tcGxldGVdKTtcbiAgICB1c2VNZW1vKCgpID0+IHtcbiAgICAgICAgcHJlc2VuY2VDaGlsZHJlbi5mb3JFYWNoKChfLCBrZXkpID0+IHByZXNlbmNlQ2hpbGRyZW4uc2V0KGtleSwgZmFsc2UpKTtcbiAgICB9LCBbaXNQcmVzZW50XSk7XG4gICAgLyoqXG4gICAgICogSWYgdGhlcmUncyBubyBgbW90aW9uYCBjb21wb25lbnRzIHRvIGZpcmUgZXhpdCBhbmltYXRpb25zLCB3ZSB3YW50IHRvIHJlbW92ZSB0aGlzXG4gICAgICogY29tcG9uZW50IGltbWVkaWF0ZWx5LlxuICAgICAqL1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgICFpc1ByZXNlbnQgJiZcbiAgICAgICAgICAgICFwcmVzZW5jZUNoaWxkcmVuLnNpemUgJiZcbiAgICAgICAgICAgIG9uRXhpdENvbXBsZXRlICYmXG4gICAgICAgICAgICBvbkV4aXRDb21wbGV0ZSgpO1xuICAgIH0sIFtpc1ByZXNlbnRdKTtcbiAgICBpZiAobW9kZSA9PT0gXCJwb3BMYXlvdXRcIikge1xuICAgICAgICBjaGlsZHJlbiA9IGpzeChQb3BDaGlsZCwgeyBpc1ByZXNlbnQ6IGlzUHJlc2VudCwgY2hpbGRyZW46IGNoaWxkcmVuIH0pO1xuICAgIH1cbiAgICByZXR1cm4gKGpzeChQcmVzZW5jZUNvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IGNvbnRleHQsIGNoaWxkcmVuOiBjaGlsZHJlbiB9KSk7XG59O1xuZnVuY3Rpb24gbmV3Q2hpbGRyZW5NYXAoKSB7XG4gICAgcmV0dXJuIG5ldyBNYXAoKTtcbn1cblxuZXhwb3J0IHsgUHJlc2VuY2VDaGlsZCB9O1xuIl0sIm5hbWVzIjpbImpzeCIsIlJlYWN0IiwidXNlSWQiLCJ1c2VDYWxsYmFjayIsInVzZU1lbW8iLCJQcmVzZW5jZUNvbnRleHQiLCJ1c2VDb25zdGFudCIsIlBvcENoaWxkIiwiUHJlc2VuY2VDaGlsZCIsImNoaWxkcmVuIiwiaW5pdGlhbCIsImlzUHJlc2VudCIsIm9uRXhpdENvbXBsZXRlIiwiY3VzdG9tIiwicHJlc2VuY2VBZmZlY3RzTGF5b3V0IiwibW9kZSIsInByZXNlbmNlQ2hpbGRyZW4iLCJuZXdDaGlsZHJlbk1hcCIsImlkIiwibWVtb2l6ZWRPbkV4aXRDb21wbGV0ZSIsImNoaWxkSWQiLCJzZXQiLCJpc0NvbXBsZXRlIiwidmFsdWVzIiwiY29udGV4dCIsInJlZ2lzdGVyIiwiZGVsZXRlIiwiTWF0aCIsInJhbmRvbSIsImZvckVhY2giLCJfIiwia2V5IiwidXNlRWZmZWN0Iiwic2l6ZSIsIlByb3ZpZGVyIiwidmFsdWUiLCJNYXAiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatePresence: () => (/* binding */ AnimatePresence)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PresenceChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\");\n/* harmony import */ var _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-presence.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs\");\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* __next_internal_client_entry_do_not_use__ AnimatePresence auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */ const AnimatePresence = (param)=>{\n    let { children, custom, initial = true, onExitComplete, presenceAffectsLayout = true, mode = \"sync\", propagate = false } = param;\n    _s();\n    const [isParentPresent, safeToRemove] = (0,_use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence)(propagate);\n    /**\n     * Filter any children that aren't ReactElements. We can only track components\n     * between renders with a props.key.\n     */ const presentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"AnimatePresence.useMemo[presentChildren]\": ()=>(0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(children)\n    }[\"AnimatePresence.useMemo[presentChildren]\"], [\n        children\n    ]);\n    /**\n     * Track the keys of the currently rendered children. This is used to\n     * determine which children are exiting.\n     */ const presentKeys = propagate && !isParentPresent ? [] : presentChildren.map(_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey);\n    /**\n     * If `initial={false}` we only want to pass this to components in the first render.\n     */ const isInitialRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    /**\n     * A ref containing the currently present children. When all exit animations\n     * are complete, we use this to re-render the component with the latest children\n     * *committed* rather than the latest children *rendered*.\n     */ const pendingPresentChildren = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(presentChildren);\n    /**\n     * Track which exiting children have finished animating out.\n     */ const exitComplete = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant)({\n        \"AnimatePresence.useConstant[exitComplete]\": ()=>new Map()\n    }[\"AnimatePresence.useConstant[exitComplete]\"]);\n    /**\n     * Save children to render as React state. To ensure this component is concurrent-safe,\n     * we check for exiting children via an effect.\n     */ const [diffedChildren, setDiffedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    const [renderedChildren, setRenderedChildren] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(presentChildren);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)({\n        \"AnimatePresence.useIsomorphicLayoutEffect\": ()=>{\n            isInitialRender.current = false;\n            pendingPresentChildren.current = presentChildren;\n            /**\n         * Update complete status of exiting children.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n                const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(renderedChildren[i]);\n                if (!presentKeys.includes(key)) {\n                    if (exitComplete.get(key) !== true) {\n                        exitComplete.set(key, false);\n                    }\n                } else {\n                    exitComplete.delete(key);\n                }\n            }\n        }\n    }[\"AnimatePresence.useIsomorphicLayoutEffect\"], [\n        renderedChildren,\n        presentKeys.length,\n        presentKeys.join(\"-\")\n    ]);\n    const exitingChildren = [];\n    if (presentChildren !== diffedChildren) {\n        let nextChildren = [\n            ...presentChildren\n        ];\n        /**\n         * Loop through all the currently rendered components and decide which\n         * are exiting.\n         */ for(let i = 0; i < renderedChildren.length; i++){\n            const child = renderedChildren[i];\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            if (!presentKeys.includes(key)) {\n                nextChildren.splice(i, 0, child);\n                exitingChildren.push(child);\n            }\n        }\n        /**\n         * If we're in \"wait\" mode, and we have exiting children, we want to\n         * only render these until they've all exited.\n         */ if (mode === \"wait\" && exitingChildren.length) {\n            nextChildren = exitingChildren;\n        }\n        setRenderedChildren((0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.onlyElements)(nextChildren));\n        setDiffedChildren(presentChildren);\n        /**\n         * Early return to ensure once we've set state with the latest diffed\n         * children, we can immediately re-render.\n         */ return;\n    }\n    if ( true && mode === \"wait\" && renderedChildren.length > 1) {\n        console.warn('You\\'re attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.');\n    }\n    /**\n     * If we've been provided a forceRender function by the LayoutGroupContext,\n     * we can use it to force a re-render amongst all surrounding components once\n     * all components have finished animating out.\n     */ const { forceRender } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_6__.LayoutGroupContext);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: renderedChildren.map((child)=>{\n            const key = (0,_utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getChildKey)(child);\n            const isPresent = propagate && !isParentPresent ? false : presentChildren === renderedChildren || presentKeys.includes(key);\n            const onExit = ()=>{\n                if (exitComplete.has(key)) {\n                    exitComplete.set(key, true);\n                } else {\n                    return;\n                }\n                let isEveryExitComplete = true;\n                exitComplete.forEach((isExitComplete)=>{\n                    if (!isExitComplete) isEveryExitComplete = false;\n                });\n                if (isEveryExitComplete) {\n                    forceRender === null || forceRender === void 0 ? void 0 : forceRender();\n                    setRenderedChildren(pendingPresentChildren.current);\n                    propagate && (safeToRemove === null || safeToRemove === void 0 ? void 0 : safeToRemove());\n                    onExitComplete && onExitComplete();\n                }\n            };\n            return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, {\n                isPresent: isPresent,\n                initial: !isInitialRender.current || initial ? undefined : false,\n                custom: isPresent ? undefined : custom,\n                presenceAffectsLayout: presenceAffectsLayout,\n                mode: mode,\n                onExitComplete: isPresent ? undefined : onExit,\n                children: child\n            }, key);\n        })\n    });\n};\n_s(AnimatePresence, \"hskVsE2zKTQdrb/joPYe18qtIRg=\", false, function() {\n    return [\n        _use_presence_mjs__WEBPACK_IMPORTED_MODULE_2__.usePresence,\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant,\n        _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect\n    ];\n});\n_c = AnimatePresence;\n\nvar _c;\n$RefreshReg$(_c, \"AnimatePresence\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getChildKey: () => (/* binding */ getChildKey),\n/* harmony export */   onlyElements: () => (/* binding */ onlyElements)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nconst getChildKey = (child) => child.key || \"\";\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (child) => {\n        if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFpRDs7QUFFakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDJDQUFRO0FBQ1osWUFBWSxxREFBYztBQUMxQjtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVxQyIsInNvdXJjZXMiOlsiL0FwcGxpY2F0aW9ucy9YQU1QUC94YW1wcGZpbGVzL2h0ZG9jcy9wb3J0Zm9saW8vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9jb21wb25lbnRzL0FuaW1hdGVQcmVzZW5jZS91dGlscy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQ2hpbGRyZW4sIGlzVmFsaWRFbGVtZW50IH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBnZXRDaGlsZEtleSA9IChjaGlsZCkgPT4gY2hpbGQua2V5IHx8IFwiXCI7XG5mdW5jdGlvbiBvbmx5RWxlbWVudHMoY2hpbGRyZW4pIHtcbiAgICBjb25zdCBmaWx0ZXJlZCA9IFtdO1xuICAgIC8vIFdlIHVzZSBmb3JFYWNoIGhlcmUgaW5zdGVhZCBvZiBtYXAgYXMgbWFwIG11dGF0ZXMgdGhlIGNvbXBvbmVudCBrZXkgYnkgcHJlcHJlbmRpbmcgYC4kYFxuICAgIENoaWxkcmVuLmZvckVhY2goY2hpbGRyZW4sIChjaGlsZCkgPT4ge1xuICAgICAgICBpZiAoaXNWYWxpZEVsZW1lbnQoY2hpbGQpKVxuICAgICAgICAgICAgZmlsdGVyZWQucHVzaChjaGlsZCk7XG4gICAgfSk7XG4gICAgcmV0dXJuIGZpbHRlcmVkO1xufVxuXG5leHBvcnQgeyBnZXRDaGlsZEtleSwgb25seUVsZW1lbnRzIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/utils.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/sections/ProjectsSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/ProjectsSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ProjectModal */ \"(app-pages-browser)/./src/components/ui/ProjectModal.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/animations */ \"(app-pages-browser)/./src/lib/animations.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_9__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_10__.ScrollTrigger);\nconst ProjectsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn, parallax: parallaxHook } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_6__.useScrollAnimations)();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProjects, setFilteredProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch projects on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const loadProjects = {\n                \"ProjectsSection.useEffect.loadProjects\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const projectsData = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.fetchProjects)();\n                        setProjects(projectsData);\n                        setFilteredProjects(projectsData);\n                        const projectCategories = (0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.getProjectCategories)(projectsData);\n                        setCategories([\n                            'All',\n                            ...projectCategories\n                        ]);\n                    } catch (error) {\n                        console.error('Failed to load projects:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectsSection.useEffect.loadProjects\"];\n            loadProjects();\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    // Filter projects when category changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const filtered = (0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.filterProjectsByCategory)(projects, activeCategory);\n            setFilteredProjects(filtered);\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        projects,\n        activeCategory\n    ]);\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n        setIsModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedProject(null);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            const title = titleRef.current;\n            if (!section || !title) return;\n            // Animate title\n            slideIn(title, 'up', {\n                start: 'top 90%'\n            });\n            // Stagger animate project cards\n            staggerAnimation('.project-card', 'fadeIn', {\n                trigger: section,\n                start: 'top 70%',\n                stagger: 0.15\n            });\n            // Add parallax to project images\n            section.querySelectorAll('.project-image').forEach({\n                \"ProjectsSection.useEffect\": (img, index)=>{\n                    (0,_lib_animations__WEBPACK_IMPORTED_MODULE_7__.parallax)(img, {\n                        speed: 0.3 + index % 3 * 0.1\n                    });\n                    (0,_lib_animations__WEBPACK_IMPORTED_MODULE_7__.imageZoom)(img, {\n                        scale: 1.1\n                    });\n                }\n            }[\"ProjectsSection.useEffect\"]);\n            return ({\n                \"ProjectsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_10__.ScrollTrigger.getAll().forEach({\n                        \"ProjectsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ProjectsSection.useEffect\"]);\n                }\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 bg-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-10 w-32 h-32 bg-primary-peach/5 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-10 w-48 h-48 bg-primary-green/5 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleRef,\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"h2\",\n                                font: \"clash\",\n                                weight: \"bold\",\n                                className: \"mb-4\",\n                                children: [\n                                    \"Featured \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-green\",\n                                        children: \"Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 22\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"body\",\n                                color: \"muted\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"A showcase of my latest work, featuring cutting-edge technologies and innovative design solutions.\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                onClick: ()=>setActiveCategory(category),\n                                className: \"px-6 py-2 rounded-full font-medium transition-all duration-300 \".concat(activeCategory === category ? 'bg-primary-black text-white' : 'bg-primary-neutral text-primary-black hover:bg-primary-peach/20'),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                \"data-cursor\": \"pointer\",\n                                children: category\n                            }, category, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-primary-peach border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined),\n                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: filteredProjects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                className: \"project-card group cursor-pointer\",\n                                whileHover: {\n                                    y: -8\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: 'easeOut'\n                                },\n                                onClick: ()=>handleProjectClick(project),\n                                layout: true,\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"bg-primary-neutral h-full overflow-hidden\",\n                                    variant: \"default\",\n                                    padding: \"none\",\n                                    rounded: \"xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gradient-to-br from-primary-peach/20 to-primary-green/20 relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"project-image w-full h-full bg-primary-peach/30 transition-transform duration-700 group-hover:scale-110\",\n                                                    style: {\n                                                        backgroundImage: \"linear-gradient(135deg,\\n                          \".concat(index % 3 === 0 ? '#fecf8b40' : index % 3 === 1 ? '#45523e40' : '#01010140', \",\\n                          transparent)\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-white/90 text-primary-black text-xs font-medium rounded-full\",\n                                                        children: project.category\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-primary-black/0 group-hover:bg-primary-black/20 transition-colors duration-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-white rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"20\",\n                                                                height: \"20\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"h5\",\n                                                    font: \"satoshi\",\n                                                    weight: \"semibold\",\n                                                    className: \"mb-3 group-hover:text-primary-green transition-colors duration-300\",\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"muted\",\n                                                    className: \"mb-4 line-clamp-2\",\n                                                    children: project.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: [\n                                                        project.technologies.slice(0, 3).map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded\",\n                                                                children: tech\n                                                            }, tech, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 25\n                                                            }, undefined)),\n                                                        project.technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded\",\n                                                            children: [\n                                                                \"+\",\n                                                                project.technologies.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, project.id, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        project: selectedProject,\n                        isOpen: isModalOpen,\n                        onClose: handleCloseModal\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectsSection, \"YuYXCux0h4WLsSiqSaYVFLg5sxQ=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_6__.useScrollAnimations\n    ];\n});\n_c = ProjectsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectsSection);\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/ProjectModal.tsx":
/*!********************************************!*\
  !*** ./src/components/ui/ProjectModal.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ProjectModal = (param)=>{\n    let { project, isOpen, onClose } = param;\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectModal.useEffect\": ()=>{\n            if (isOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            return ({\n                \"ProjectModal.useEffect\": ()=>{\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"ProjectModal.useEffect\"];\n        }\n    }[\"ProjectModal.useEffect\"], [\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectModal.useEffect\": ()=>{\n            const handleEscape = {\n                \"ProjectModal.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape') {\n                        onClose();\n                    }\n                }\n            }[\"ProjectModal.useEffect.handleEscape\"];\n            if (isOpen) {\n                document.addEventListener('keydown', handleEscape);\n            }\n            return ({\n                \"ProjectModal.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                }\n            })[\"ProjectModal.useEffect\"];\n        }\n    }[\"ProjectModal.useEffect\"], [\n        isOpen,\n        onClose\n    ]);\n    if (!project) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n        children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            transition: {\n                duration: 0.3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"absolute inset-0 bg-black/80 backdrop-blur-sm\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"relative bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n                    initial: {\n                        scale: 0.9,\n                        opacity: 0,\n                        y: 20\n                    },\n                    animate: {\n                        scale: 1,\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        scale: 0.9,\n                        opacity: 0,\n                        y: 20\n                    },\n                    transition: {\n                        duration: 0.3,\n                        ease: 'easeOut'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"absolute top-4 right-4 z-10 w-10 h-10 bg-white/90 hover:bg-white rounded-full flex items-center justify-center transition-colors duration-200\",\n                            \"data-cursor\": \"pointer\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                width: \"20\",\n                                height: \"20\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"18\",\n                                        y1: \"6\",\n                                        x2: \"6\",\n                                        y2: \"18\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"6\",\n                                        y1: \"6\",\n                                        x2: \"18\",\n                                        y2: \"18\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-video bg-gradient-to-br from-primary-peach/20 to-primary-green/20 relative overflow-hidden rounded-t-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-primary-peach/30\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-6 left-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-white/90 text-primary-black text-sm font-medium rounded-full\",\n                                        children: project.category\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                variant: \"h3\",\n                                                font: \"clash\",\n                                                weight: \"bold\",\n                                                className: \"mb-2\",\n                                                children: project.title\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                variant: \"body\",\n                                                color: \"muted\",\n                                                className: \"text-sm\",\n                                                children: project.year\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"body\",\n                                    color: \"muted\",\n                                    className: \"mb-8 leading-relaxed\",\n                                    children: project.description\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"h6\",\n                                            font: \"satoshi\",\n                                            weight: \"semibold\",\n                                            className: \"mb-4\",\n                                            children: \"Technologies Used\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: project.technologies.map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 bg-primary-neutral text-primary-black text-sm rounded-full\",\n                                                    children: tech\n                                                }, tech, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"h6\",\n                                            font: \"satoshi\",\n                                            weight: \"semibold\",\n                                            className: \"mb-4\",\n                                            children: \"Project Details\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            variant: \"body\",\n                                                            weight: \"medium\",\n                                                            className: \"mb-2\",\n                                                            children: \"Category\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            color: \"muted\",\n                                                            children: project.category\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            variant: \"body\",\n                                                            weight: \"medium\",\n                                                            className: \"mb-2\",\n                                                            children: \"Year\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            color: \"muted\",\n                                                            children: project.year\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4\",\n                                    children: [\n                                        project.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            variant: \"primary\",\n                                            size: \"md\",\n                                            onClick: ()=>window.open(project.url, '_blank'),\n                                            children: \"View Live Site\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        project.github && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            variant: \"outline\",\n                                            size: \"md\",\n                                            onClick: ()=>window.open(project.github, '_blank'),\n                                            children: \"View Code\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n            lineNumber: 49,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/ProjectModal.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectModal, \"3ubReDTFssvu4DHeldAg55cW/CI=\");\n_c = ProjectModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectModal);\nvar _c;\n$RefreshReg$(_c, \"ProjectModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ProjectModal.tsx\n"));

/***/ })

});