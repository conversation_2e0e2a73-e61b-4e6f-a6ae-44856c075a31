"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_5__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger);\nconst AboutSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timelineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn, fadeIn } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__.useScrollAnimations)();\n    const timeline = [\n        {\n            year: '2019',\n            title: 'Started Web Development',\n            description: 'Began my journey with HTML, CSS, and JavaScript, building my first websites.'\n        },\n        {\n            year: '2020',\n            title: 'React & Modern Frameworks',\n            description: 'Mastered React and modern development tools, started building complex applications.'\n        },\n        {\n            year: '2021',\n            title: 'Freelance Success',\n            description: 'Launched freelance career, working with clients worldwide on diverse projects.'\n        },\n        {\n            year: '2022',\n            title: 'Advanced Animations',\n            description: 'Specialized in GSAP and advanced animations, creating award-winning experiences.'\n        },\n        {\n            year: '2023',\n            title: 'Full-Stack Expertise',\n            description: 'Expanded to full-stack development with Node.js, databases, and cloud services.'\n        },\n        {\n            year: '2024',\n            title: 'AI Integration',\n            description: 'Integrated AI technologies and modern tools to create cutting-edge solutions.'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            const timelineEl = timelineRef.current;\n            if (!section || !timelineEl) return;\n            // Animate main content\n            staggerAnimation('.about-content > *', 'fadeIn', {\n                trigger: section,\n                start: 'top 80%',\n                stagger: 0.2\n            });\n            // Animate timeline items\n            staggerAnimation('.timeline-item', 'slideUp', {\n                trigger: timelineEl,\n                start: 'top 80%',\n                stagger: 0.15\n            });\n            // Animate timeline line\n            gsap__WEBPACK_IMPORTED_MODULE_5__.gsap.fromTo('.timeline-line', {\n                height: '0%'\n            }, {\n                height: '100%',\n                duration: 2,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: timelineEl,\n                    start: 'top 70%',\n                    end: 'bottom 30%',\n                    scrub: 1\n                }\n            });\n            return ({\n                \"AboutSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_6__.ScrollTrigger.getAll().forEach({\n                        \"AboutSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"AboutSection.useEffect\"]);\n                }\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"about\",\n        ref: sectionRef,\n        className: \"py-20 bg-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-0 w-64 h-64 bg-primary-peach/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-0 w-80 h-80 bg-primary-green/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"about-content\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h2\",\n                                    font: \"clash\",\n                                    weight: \"bold\",\n                                    className: \"mb-6\",\n                                    children: [\n                                        \"About \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-green\",\n                                            children: \"Me\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"body\",\n                                    color: \"muted\",\n                                    className: \"mb-6 leading-relaxed\",\n                                    children: \"I'm a passionate developer who creates award-winning digital experiences. With expertise in modern web technologies and a keen eye for design, I bring ideas to life through code.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"body\",\n                                    color: \"muted\",\n                                    className: \"mb-8 leading-relaxed\",\n                                    children: \"My journey spans over 5 years of crafting innovative solutions for clients worldwide. I specialize in creating performant, accessible, and visually stunning web applications that push the boundaries of what's possible.\"\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-6 mb-8\",\n                                    children: [\n                                        {\n                                            number: '50+',\n                                            label: 'Projects'\n                                        },\n                                        {\n                                            number: '5+',\n                                            label: 'Years'\n                                        },\n                                        {\n                                            number: '15+',\n                                            label: 'Clients'\n                                        }\n                                    ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"h3\",\n                                                    font: \"clash\",\n                                                    weight: \"bold\",\n                                                    className: \"text-primary-peach mb-1\",\n                                                    children: stat.number\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"muted\",\n                                                    className: \"text-sm\",\n                                                    children: stat.label\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, stat.label, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                    whileHover: {\n                                        scale: 1.02\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 bg-primary-black text-white rounded-full font-medium hover:bg-primary-green transition-colors duration-300\",\n                                        children: \"Download Resume\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: timelineRef,\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    variant: \"h3\",\n                                    font: \"clash\",\n                                    weight: \"bold\",\n                                    className: \"mb-8 text-center lg:text-left\",\n                                    children: [\n                                        \"My \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-primary-peach\",\n                                            children: \"Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 18\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute left-6 top-0 w-0.5 bg-primary-neutral\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"timeline-line w-full bg-primary-peach origin-top\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-8\",\n                                            children: timeline.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    className: \"timeline-item relative pl-16\",\n                                                    whileHover: {\n                                                        x: 4\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-4 top-2 w-4 h-4 bg-primary-peach rounded-full border-4 border-white shadow-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-primary-neutral/50 rounded-xl p-6 hover:bg-primary-neutral/70 transition-colors duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            variant: \"h6\",\n                                                                            font: \"clash\",\n                                                                            weight: \"bold\",\n                                                                            className: \"text-primary-green\",\n                                                                            children: item.year\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                            lineNumber: 202,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                            variant: \"h6\",\n                                                                            font: \"satoshi\",\n                                                                            weight: \"semibold\",\n                                                                            children: item.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    color: \"muted\",\n                                                                    className: \"leading-relaxed\",\n                                                                    children: item.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, item.year, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AboutSection, \"WGYKuhgym1zYAZDJFRrErpIIG1U=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__.useScrollAnimations\n    ];\n});\n_c = AboutSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutSection);\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});