"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/SkillsSection.tsx":
/*!***************************************************!*\
  !*** ./src/components/sections/SkillsSection.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_6__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_7__.ScrollTrigger);\nconst SkillsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__.useScrollAnimations)();\n    const [skillCategories, setSkillCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch skills data from Laravel API\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SkillsSection.useEffect\": ()=>{\n            const loadSkills = {\n                \"SkillsSection.useEffect.loadSkills\": async ()=>{\n                    try {\n                        const data = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_5__.fetchSkills)();\n                        setSkillCategories(data);\n                    } catch (error) {\n                        console.error('Failed to load skills:', error);\n                        // Fallback data\n                        setSkillCategories([\n                            {\n                                id: 1,\n                                name: 'Frontend',\n                                slug: 'frontend',\n                                description: 'Frontend technologies',\n                                icon: null,\n                                color: '#61DAFB',\n                                skills: [\n                                    {\n                                        id: 1,\n                                        name: 'React',\n                                        description: null,\n                                        icon: null,\n                                        proficiency_level: 95,\n                                        years_experience: 5,\n                                        is_featured: true\n                                    },\n                                    {\n                                        id: 2,\n                                        name: 'Next.js',\n                                        description: null,\n                                        icon: null,\n                                        proficiency_level: 90,\n                                        years_experience: 3,\n                                        is_featured: true\n                                    },\n                                    {\n                                        id: 3,\n                                        name: 'TypeScript',\n                                        description: null,\n                                        icon: null,\n                                        proficiency_level: 88,\n                                        years_experience: 4,\n                                        is_featured: true\n                                    },\n                                    {\n                                        id: 4,\n                                        name: 'Tailwind CSS',\n                                        description: null,\n                                        icon: null,\n                                        proficiency_level: 92,\n                                        years_experience: 3,\n                                        is_featured: false\n                                    }\n                                ]\n                            }\n                        ]);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"SkillsSection.useEffect.loadSkills\"];\n            loadSkills();\n        }\n    }[\"SkillsSection.useEffect\"], []);\n    // Animation useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SkillsSection.useEffect\": ()=>{\n            if (loading) return; // Don't animate while loading\n            const section = sectionRef.current;\n            const title = titleRef.current;\n            if (!section || !title) return;\n            // Animate title\n            slideIn(title, 'up', {\n                start: 'top 90%'\n            });\n            // Stagger animate skill categories\n            staggerAnimation('.skill-category', 'fadeIn', {\n                trigger: section,\n                start: 'top 80%',\n                stagger: 0.2\n            });\n            // Animate skill bars\n            gsap__WEBPACK_IMPORTED_MODULE_6__.gsap.fromTo('.skill-bar', {\n                width: '0%'\n            }, {\n                width: {\n                    \"SkillsSection.useEffect\": (index, target)=>target.dataset.level + '%'\n                }[\"SkillsSection.useEffect\"],\n                duration: 1.5,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: section,\n                    start: 'top 70%'\n                }\n            });\n            return ({\n                \"SkillsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_7__.ScrollTrigger.getAll().forEach({\n                        \"SkillsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"SkillsSection.useEffect\"]);\n                }\n            })[\"SkillsSection.useEffect\"];\n        }\n    }[\"SkillsSection.useEffect\"], [\n        loading,\n        skillCategories,\n        slideIn,\n        staggerAnimation\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"skills\",\n        ref: sectionRef,\n        className: \"py-20 bg-primary-neutral relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,#010101_1px,transparent_1px)] bg-[length:50px_50px]\"\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleRef,\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"h2\",\n                                font: \"clash\",\n                                weight: \"bold\",\n                                className: \"mb-4\",\n                                children: [\n                                    \"Skills & \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-peach\",\n                                        children: \"Expertise\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 22\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"body\",\n                                color: \"muted\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"A comprehensive overview of my technical skills and proficiency levels across different technologies and frameworks.\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: loading ? // Loading skeleton\n                        [\n                            ...Array(3)\n                        ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-2xl p-8 h-full shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 bg-gray-200 rounded animate-pulse mb-6 w-24 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            ...Array(4)\n                                        ].map((_, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"skill-item\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 bg-gray-200 rounded animate-pulse w-16\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                                lineNumber: 129,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-3 bg-gray-200 rounded animate-pulse w-8\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-200 rounded-full h-2 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, skillIndex, true, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, undefined)) : skillCategories.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"skill-category\",\n                                whileHover: {\n                                    y: -4\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl p-8 h-full shadow-sm hover:shadow-lg transition-shadow duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"h4\",\n                                            font: \"satoshi\",\n                                            weight: \"bold\",\n                                            className: \"mb-6 text-center\",\n                                            style: {\n                                                color: category.color || '#010101'\n                                            },\n                                            children: category.name\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: category.skills.map((skill, skillIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"skill-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"body\",\n                                                                    font: \"satoshi\",\n                                                                    weight: \"medium\",\n                                                                    className: \"text-sm\",\n                                                                    children: skill.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    variant: \"caption\",\n                                                                    color: \"muted\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        skill.proficiency_level,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-primary-neutral rounded-full h-2 overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"skill-bar h-full rounded-full transition-all duration-300\",\n                                                                \"data-level\": skill.proficiency_level,\n                                                                style: {\n                                                                    backgroundColor: category.color || '#010101',\n                                                                    width: \"\".concat(skill.proficiency_level, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                                lineNumber: 179,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, skill.id, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, category.id, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center\",\n                        children: [\n                            {\n                                number: '50+',\n                                label: 'Projects Completed'\n                            },\n                            {\n                                number: '5+',\n                                label: 'Years Experience'\n                            },\n                            {\n                                number: '15+',\n                                label: 'Technologies'\n                            },\n                            {\n                                number: '100%',\n                                label: 'Client Satisfaction'\n                            }\n                        ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"skill-stat\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"h3\",\n                                        font: \"clash\",\n                                        weight: \"bold\",\n                                        className: \"text-primary-peach mb-2\",\n                                        children: stat.number\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"caption\",\n                                        color: \"muted\",\n                                        className: \"text-sm\",\n                                        children: stat.label\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, stat.label, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SkillsSection, \"diEDJlFm3cbauqTi/Dp4Up8ccR4=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_4__.useScrollAnimations\n    ];\n});\n_c = SkillsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SkillsSection);\nvar _c;\n$RefreshReg$(_c, \"SkillsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/SkillsSection.tsx\n"));

/***/ })

});