"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ContactSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/sections/ContactSection.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_7__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger);\nconst ContactSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const formRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__.useScrollAnimations)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        subject: '',\n        message: '',\n        phone: '',\n        company: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const [contactInfo, setContactInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [socialLinks, setSocialLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch contact data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactSection.useEffect\": ()=>{\n            const loadContactData = {\n                \"ContactSection.useEffect.loadContactData\": async ()=>{\n                    try {\n                        const [contact, social] = await Promise.all([\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.fetchContactInfo)(),\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.fetchSocialLinks)()\n                        ]);\n                        setContactInfo(contact);\n                        setSocialLinks(social);\n                    } catch (error) {\n                        console.error('Failed to load contact data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ContactSection.useEffect.loadContactData\"];\n            loadContactData();\n        }\n    }[\"ContactSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            if (!section) return;\n            // Animate contact items\n            staggerAnimation('.contact-item', 'fadeIn', {\n                trigger: section,\n                start: 'top 80%',\n                stagger: 0.2\n            });\n            // Animate form inputs\n            staggerAnimation('.form-input', 'slideUp', {\n                trigger: section,\n                start: 'top 70%',\n                stagger: 0.1\n            });\n            return ({\n                \"ContactSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger.getAll().forEach({\n                        \"ContactSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ContactSection.useEffect\"]);\n                }\n            })[\"ContactSection.useEffect\"];\n        }\n    }[\"ContactSection.useEffect\"], [\n        staggerAnimation\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            // Reset form\n            setFormData({\n                name: '',\n                email: '',\n                message: ''\n            });\n            setSubmitStatus('success');\n            // Reset status after 3 seconds\n            setTimeout(()=>setSubmitStatus('idle'), 3000);\n        } catch (error) {\n            setSubmitStatus('error');\n            setTimeout(()=>setSubmitStatus('idle'), 3000);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"contact\",\n        ref: sectionRef,\n        className: \"py-20 bg-primary-neutral relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 right-0 w-72 h-72 bg-primary-peach/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 left-0 w-96 h-96 bg-primary-green/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"contact-item text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"h2\",\n                                font: \"clash\",\n                                weight: \"bold\",\n                                className: \"mb-4\",\n                                children: [\n                                    \"Let's \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-peach\",\n                                        children: \"Connect\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"body\",\n                                color: \"muted\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"Ready to bring your next project to life? Let's discuss how we can create something amazing together.\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"contact-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        variant: \"h3\",\n                                        font: \"satoshi\",\n                                        weight: \"bold\",\n                                        className: \"mb-8\",\n                                        children: \"Get in Touch\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8 mb-12\",\n                                        children: [\n                                            {\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"20\",\n                                                    height: \"20\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"22,6 12,13 2,6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                title: 'Email',\n                                                value: '<EMAIL>',\n                                                color: 'bg-primary-peach'\n                                            },\n                                            {\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"20\",\n                                                    height: \"20\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        d: \"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                title: 'Phone',\n                                                value: '+****************',\n                                                color: 'bg-primary-green'\n                                            },\n                                            {\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    width: \"20\",\n                                                    height: \"20\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"10\",\n                                                            r: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                title: 'Location',\n                                                value: 'San Francisco, CA',\n                                                color: 'bg-primary-black'\n                                            }\n                                        ].map((contact, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                                className: \"flex items-center space-x-4\",\n                                                whileHover: {\n                                                    x: 4\n                                                },\n                                                transition: {\n                                                    duration: 0.2\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 \".concat(contact.color, \" rounded-full flex items-center justify-center text-white\"),\n                                                        children: contact.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                variant: \"body\",\n                                                                weight: \"medium\",\n                                                                className: \"mb-1\",\n                                                                children: contact.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                color: \"muted\",\n                                                                children: contact.value\n                                                            }, void 0, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                                lineNumber: 194,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, contact.title, true, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                variant: \"h6\",\n                                                font: \"satoshi\",\n                                                weight: \"semibold\",\n                                                className: \"mb-4\",\n                                                children: \"Follow Me\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    {\n                                                        name: 'GitHub',\n                                                        url: '#'\n                                                    },\n                                                    {\n                                                        name: 'LinkedIn',\n                                                        url: '#'\n                                                    },\n                                                    {\n                                                        name: 'Twitter',\n                                                        url: '#'\n                                                    },\n                                                    {\n                                                        name: 'Dribbble',\n                                                        url: '#'\n                                                    }\n                                                ].map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.a, {\n                                                        href: social.url,\n                                                        className: \"w-12 h-12 bg-white rounded-full flex items-center justify-center text-primary-black hover:bg-primary-peach hover:text-white transition-colors duration-300\",\n                                                        whileHover: {\n                                                            scale: 1.1,\n                                                            y: -2\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        \"data-cursor\": \"pointer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-bold\",\n                                                            children: social.name.slice(0, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                            lineNumber: 227,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, social.name, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"contact-item\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    ref: formRef,\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.input, {\n                                                type: \"text\",\n                                                name: \"name\",\n                                                placeholder: \"Your Name\",\n                                                value: formData.name,\n                                                onChange: handleChange,\n                                                className: \"w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 focus:shadow-lg\",\n                                                required: true,\n                                                whileFocus: {\n                                                    scale: 1.02\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.input, {\n                                                type: \"email\",\n                                                name: \"email\",\n                                                placeholder: \"Your Email\",\n                                                value: formData.email,\n                                                onChange: handleChange,\n                                                className: \"w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 focus:shadow-lg\",\n                                                required: true,\n                                                whileFocus: {\n                                                    scale: 1.02\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.textarea, {\n                                                name: \"message\",\n                                                placeholder: \"Your Message\",\n                                                value: formData.message,\n                                                onChange: handleChange,\n                                                rows: 5,\n                                                className: \"w-full px-6 py-4 bg-white rounded-xl border border-primary-black/10 focus:border-primary-peach focus:outline-none transition-all duration-300 resize-none focus:shadow-lg\",\n                                                required: true,\n                                                whileFocus: {\n                                                    scale: 1.02\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-input\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                type: \"submit\",\n                                                variant: \"primary\",\n                                                size: \"lg\",\n                                                className: \"w-full\",\n                                                isLoading: isSubmitting,\n                                                children: isSubmitting ? 'Sending...' : 'Send Message'\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"p-4 bg-green-100 text-green-800 rounded-xl text-center\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            children: \"Message sent successfully! I'll get back to you soon.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                            className: \"p-4 bg-red-100 text-red-800 rounded-xl text-center\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -10\n                                            },\n                                            children: \"Something went wrong. Please try again.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ContactSection.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContactSection, \"R90Yycrvlpg0VbaNFiVyXXyeufI=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__.useScrollAnimations\n    ];\n});\n_c = ContactSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContactSection);\nvar _c;\n$RefreshReg$(_c, \"ContactSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ContactSection.tsx\n"));

/***/ })

});