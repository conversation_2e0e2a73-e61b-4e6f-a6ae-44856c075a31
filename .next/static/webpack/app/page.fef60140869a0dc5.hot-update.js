"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ProjectsSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/ProjectsSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ProjectModal */ \"(app-pages-browser)/./src/components/ui/ProjectModal.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/animations */ \"(app-pages-browser)/./src/lib/animations.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_9__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_10__.ScrollTrigger);\nconst ProjectsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn, parallax: parallaxHook } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_6__.useScrollAnimations)();\n    const [projects, setProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredProjects, setFilteredProjects] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeCategory, setActiveCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('All');\n    const [selectedProject, setSelectedProject] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch projects on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const loadProjects = {\n                \"ProjectsSection.useEffect.loadProjects\": async ()=>{\n                    try {\n                        setLoading(true);\n                        const projectsData = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.fetchProjects)();\n                        setProjects(projectsData);\n                        setFilteredProjects(projectsData);\n                        const projectCategories = (0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.getProjectCategories)(projectsData);\n                        setCategories([\n                            'All',\n                            ...projectCategories\n                        ]);\n                    } catch (error) {\n                        console.error('Failed to load projects:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProjectsSection.useEffect.loadProjects\"];\n            loadProjects();\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    // Filter projects when category changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const filtered = (0,_lib_api__WEBPACK_IMPORTED_MODULE_8__.filterProjectsByCategory)(projects, activeCategory);\n            setFilteredProjects(filtered);\n        }\n    }[\"ProjectsSection.useEffect\"], [\n        projects,\n        activeCategory\n    ]);\n    const handleProjectClick = (project)=>{\n        setSelectedProject(project);\n        setIsModalOpen(true);\n    };\n    const handleCloseModal = ()=>{\n        setIsModalOpen(false);\n        setSelectedProject(null);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            const title = titleRef.current;\n            if (!section || !title) return;\n            // Animate title\n            slideIn(title, 'up', {\n                start: 'top 90%'\n            });\n            // Stagger animate project cards\n            staggerAnimation('.project-card', 'fadeIn', {\n                trigger: section,\n                start: 'top 70%',\n                stagger: 0.15\n            });\n            // Add parallax to project images\n            section.querySelectorAll('.project-image').forEach({\n                \"ProjectsSection.useEffect\": (img, index)=>{\n                    (0,_lib_animations__WEBPACK_IMPORTED_MODULE_7__.parallax)(img, {\n                        speed: 0.3 + index % 3 * 0.1\n                    });\n                    (0,_lib_animations__WEBPACK_IMPORTED_MODULE_7__.imageZoom)(img, {\n                        scale: 1.1\n                    });\n                }\n            }[\"ProjectsSection.useEffect\"]);\n            return ({\n                \"ProjectsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_10__.ScrollTrigger.getAll().forEach({\n                        \"ProjectsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ProjectsSection.useEffect\"]);\n                }\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"projects\",\n        ref: sectionRef,\n        className: \"py-20 bg-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-10 w-32 h-32 bg-primary-peach/5 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-10 w-48 h-48 bg-primary-green/5 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleRef,\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"h2\",\n                                font: \"clash\",\n                                weight: \"bold\",\n                                className: \"mb-4\",\n                                children: [\n                                    \"Featured \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-green\",\n                                        children: \"Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 22\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"body\",\n                                color: \"muted\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"A showcase of my latest work, featuring cutting-edge technologies and innovative design solutions.\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.button, {\n                                onClick: ()=>setActiveCategory(category),\n                                className: \"px-6 py-2 rounded-full font-medium transition-all duration-300 \".concat(activeCategory === category ? 'bg-primary-black text-white' : 'bg-primary-neutral text-primary-black hover:bg-primary-peach/20'),\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                \"data-cursor\": \"pointer\",\n                                children: category\n                            }, category, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, undefined),\n                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-primary-peach border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined),\n                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: filteredProjects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                className: \"project-card group cursor-pointer\",\n                                whileHover: {\n                                    y: -8\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: 'easeOut'\n                                },\n                                onClick: ()=>handleProjectClick(project),\n                                layout: true,\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                exit: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                transition: {\n                                    duration: 0.5,\n                                    delay: index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"bg-primary-neutral h-full overflow-hidden\",\n                                    variant: \"default\",\n                                    padding: \"none\",\n                                    rounded: \"xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gradient-to-br from-primary-peach/20 to-primary-green/20 relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"project-image w-full h-full bg-primary-peach/30 transition-transform duration-700 group-hover:scale-110\",\n                                                    style: {\n                                                        backgroundImage: \"linear-gradient(135deg,\\n                          \".concat(index % 3 === 0 ? '#fecf8b40' : index % 3 === 1 ? '#45523e40' : '#01010140', \",\\n                          transparent)\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-white/90 text-primary-black text-xs font-medium rounded-full\",\n                                                        children: project.category\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-primary-black/0 group-hover:bg-primary-black/20 transition-colors duration-300 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-white rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                width: \"20\",\n                                                                height: \"20\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                strokeWidth: \"2\",\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        d: \"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                        cx: \"12\",\n                                                                        cy: \"12\",\n                                                                        r: \"3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                        lineNumber: 207,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"h5\",\n                                                    font: \"satoshi\",\n                                                    weight: \"semibold\",\n                                                    className: \"mb-3 group-hover:text-primary-green transition-colors duration-300\",\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"muted\",\n                                                    className: \"mb-4 line-clamp-2\",\n                                                    children: project.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: [\n                                                        project.technologies.slice(0, 3).map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded\",\n                                                                children: tech\n                                                            }, tech, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 25\n                                                            }, undefined)),\n                                                        project.technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded\",\n                                                            children: [\n                                                                \"+\",\n                                                                project.technologies.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, project.id, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ProjectModal__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        project: selectedProject,\n                        isOpen: isModalOpen,\n                        onClose: handleCloseModal\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectsSection, \"YuYXCux0h4WLsSiqSaYVFLg5sxQ=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_6__.useScrollAnimations\n    ];\n});\n_c = ProjectsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectsSection);\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\n"));

/***/ })

});