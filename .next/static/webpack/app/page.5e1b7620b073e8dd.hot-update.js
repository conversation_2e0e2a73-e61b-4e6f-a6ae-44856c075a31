"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/AboutSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/AboutSection.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_3__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__.ScrollTrigger);\nconst AboutSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timelineRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn, fadeIn } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_2__.useScrollAnimations)();\n    const timeline = [\n        {\n            year: '2019',\n            title: 'Started Web Development',\n            description: 'Began my journey with HTML, CSS, and JavaScript, building my first websites.'\n        },\n        {\n            year: '2020',\n            title: 'React & Modern Frameworks',\n            description: 'Mastered React and modern development tools, started building complex applications.'\n        },\n        {\n            year: '2021',\n            title: 'Freelance Success',\n            description: 'Launched freelance career, working with clients worldwide on diverse projects.'\n        },\n        {\n            year: '2022',\n            title: 'Advanced Animations',\n            description: 'Specialized in GSAP and advanced animations, creating award-winning experiences.'\n        },\n        {\n            year: '2023',\n            title: 'Full-Stack Expertise',\n            description: 'Expanded to full-stack development with Node.js, databases, and cloud services.'\n        },\n        {\n            year: '2024',\n            title: 'AI Integration',\n            description: 'Integrated AI technologies and modern tools to create cutting-edge solutions.'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AboutSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            const timelineEl = timelineRef.current;\n            if (!section || !timelineEl) return;\n            // Animate main content\n            staggerAnimation('.about-content > *', 'fadeIn', {\n                trigger: section,\n                start: 'top 80%',\n                stagger: 0.2\n            });\n            // Animate timeline items\n            staggerAnimation('.timeline-item', 'slideUp', {\n                trigger: timelineEl,\n                start: 'top 80%',\n                stagger: 0.15\n            });\n            // Animate timeline line\n            gsap__WEBPACK_IMPORTED_MODULE_3__.gsap.fromTo('.timeline-line', {\n                height: '0%'\n            }, {\n                height: '100%',\n                duration: 2,\n                ease: 'power2.out',\n                scrollTrigger: {\n                    trigger: timelineEl,\n                    start: 'top 70%',\n                    end: 'bottom 30%',\n                    scrub: 1\n                }\n            });\n            return ({\n                \"AboutSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_4__.ScrollTrigger.getAll().forEach({\n                        \"AboutSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"AboutSection.useEffect\"]);\n                }\n            })[\"AboutSection.useEffect\"];\n        }\n    }[\"AboutSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 px-6 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"about-content text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"font-clash text-5xl md:text-6xl font-bold mb-8\",\n                        children: [\n                            \"About \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-primary-green\",\n                                children: \"Me\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 19\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-inter text-lg md:text-xl text-primary-black/80 leading-relaxed mb-8\",\n                        children: \"I'm a passionate developer who creates award-winning digital experiences. With expertise in modern web technologies and a keen eye for design, I bring ideas to life through code.\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-inter text-base text-primary-black/70 leading-relaxed mb-12\",\n                        children: \"My journey spans over 5 years of crafting innovative solutions for clients worldwide. I specialize in creating performant, accessible, and visually stunning web applications that push the boundaries of what's possible.\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-clash text-3xl font-bold text-primary-peach mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-primary-black/70\",\n                                        children: \"Projects Completed\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-clash text-3xl font-bold text-primary-green mb-2\",\n                                        children: \"5+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-primary-black/70\",\n                                        children: \"Years Experience\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-clash text-3xl font-bold text-primary-black mb-2\",\n                                        children: \"15+\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-primary-black/70\",\n                                        children: \"Happy Clients\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/AboutSection.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AboutSection, \"WGYKuhgym1zYAZDJFRrErpIIG1U=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_2__.useScrollAnimations\n    ];\n});\n_c = AboutSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutSection);\nvar _c;\n$RefreshReg$(_c, \"AboutSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/AboutSection.tsx\n"));

/***/ })

});