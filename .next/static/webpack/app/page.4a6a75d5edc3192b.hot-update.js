"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/preloader.ts":
/*!*********************************!*\
  !*** ./src/config/preloader.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   preloaderConfig: () => (/* binding */ preloaderConfig)\n/* harmony export */ });\n// Preloader Configuration\n// Switch between different preloader styles\nconst preloaderConfig = {\n    // Choose preloader style: 'awwwards' | 'minimal' | 'simple' | 'disabled'\n    // style: 'awwwards' as 'awwwards' | 'minimal' | 'simple' | 'disabled',\n    // Duration in milliseconds\n    duration: 3000,\n    // Enable/disable preloader entirely\n    enabled: true,\n    // Show preloader only on first visit (uses localStorage)\n    showOnlyOnFirstVisit: false,\n    // Custom loading messages for Awwwards style\n    loadingWords: [\n        'LOADING',\n        'CREATING',\n        'CRAFTING',\n        'BUILDING',\n        'DESIGNING',\n        'ANIMATING',\n        'READY'\n    ],\n    // Animation settings\n    animations: {\n        // Logo entrance delay\n        logoDelay: 0.2,\n        // Progress bar smoothness (lower = smoother, higher = choppier)\n        progressUpdateInterval: 50,\n        // Exit animation duration\n        exitDuration: 1000\n    },\n    // Visual settings\n    visual: {\n        // Show floating particles\n        showParticles: true,\n        // Show animated background\n        showAnimatedBackground: true,\n        // Show geometric shapes\n        showGeometricShapes: true,\n        // Particle count\n        particleCount: 20\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (preloaderConfig);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/preloader.ts\n"));

/***/ })

});