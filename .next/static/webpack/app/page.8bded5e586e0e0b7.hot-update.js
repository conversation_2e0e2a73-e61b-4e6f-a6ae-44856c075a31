"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/ProjectsSection.tsx":
/*!*****************************************************!*\
  !*** ./src/components/sections/ProjectsSection.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(app-pages-browser)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Container__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Container */ \"(app-pages-browser)/./src/components/ui/Container.tsx\");\n/* harmony import */ var _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useScrollAnimations */ \"(app-pages-browser)/./src/hooks/useScrollAnimations.ts\");\n/* harmony import */ var _lib_animations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/animations */ \"(app-pages-browser)/./src/lib/animations.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_7__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger);\nconst ProjectsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { staggerAnimation, slideIn, parallax: parallaxHook } = (0,_hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__.useScrollAnimations)();\n    const projects = [\n        {\n            id: 1,\n            title: 'E-commerce Platform',\n            description: 'A modern e-commerce platform built with Next.js and Stripe integration.',\n            image: '/images/project1.jpg',\n            technologies: [\n                'Next.js',\n                'React',\n                'Stripe',\n                'Tailwind CSS'\n            ],\n            category: 'Web Development'\n        },\n        {\n            id: 2,\n            title: 'Portfolio Website',\n            description: 'An award-winning portfolio website with stunning animations.',\n            image: '/images/project2.jpg',\n            technologies: [\n                'React',\n                'GSAP',\n                'Framer Motion',\n                'Three.js'\n            ],\n            category: 'Creative'\n        },\n        {\n            id: 3,\n            title: 'SaaS Dashboard',\n            description: 'A comprehensive dashboard with real-time data visualization.',\n            image: '/images/project3.jpg',\n            technologies: [\n                'Vue.js',\n                'Node.js',\n                'MongoDB',\n                'Chart.js'\n            ],\n            category: 'Web Application'\n        },\n        {\n            id: 4,\n            title: 'Mobile App UI',\n            description: 'Beautiful mobile app interface design with smooth animations.',\n            image: '/images/project4.jpg',\n            technologies: [\n                'React Native',\n                'Expo',\n                'TypeScript'\n            ],\n            category: 'Mobile'\n        },\n        {\n            id: 5,\n            title: 'Brand Identity',\n            description: 'Complete brand identity design including logo and typography.',\n            image: '/images/project5.jpg',\n            technologies: [\n                'Adobe Illustrator',\n                'Figma',\n                'Photoshop'\n            ],\n            category: 'Design'\n        },\n        {\n            id: 6,\n            title: 'AI Chat Interface',\n            description: 'Modern chat interface for AI-powered customer support.',\n            image: '/images/project6.jpg',\n            technologies: [\n                'React',\n                'Socket.io',\n                'OpenAI API',\n                'Node.js'\n            ],\n            category: 'AI/ML'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProjectsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            const title = titleRef.current;\n            if (!section || !title) return;\n            // Animate title\n            slideIn(title, 'up', {\n                start: 'top 90%'\n            });\n            // Stagger animate project cards\n            staggerAnimation('.project-card', 'fadeIn', {\n                trigger: section,\n                start: 'top 70%',\n                stagger: 0.15\n            });\n            // Add parallax to project images\n            section.querySelectorAll('.project-image').forEach({\n                \"ProjectsSection.useEffect\": (img, index)=>{\n                    (0,_lib_animations__WEBPACK_IMPORTED_MODULE_6__.parallax)(img, {\n                        speed: 0.3 + index % 3 * 0.1\n                    });\n                    (0,_lib_animations__WEBPACK_IMPORTED_MODULE_6__.imageZoom)(img, {\n                        scale: 1.1\n                    });\n                }\n            }[\"ProjectsSection.useEffect\"]);\n            return ({\n                \"ProjectsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_8__.ScrollTrigger.getAll().forEach({\n                        \"ProjectsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"ProjectsSection.useEffect\"]);\n                }\n            })[\"ProjectsSection.useEffect\"];\n        }\n    }[\"ProjectsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 bg-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 right-10 w-32 h-32 bg-primary-peach/5 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-10 w-48 h-48 bg-primary-green/5 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Container__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: \"xl\",\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: titleRef,\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"h2\",\n                                font: \"clash\",\n                                weight: \"bold\",\n                                className: \"mb-4\",\n                                children: [\n                                    \"Featured \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary-green\",\n                                        children: \"Projects\"\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 22\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                variant: \"body\",\n                                color: \"muted\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"A showcase of my latest work, featuring cutting-edge technologies and innovative design solutions.\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                        children: projects.map((project, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                className: \"project-card group\",\n                                whileHover: {\n                                    y: -8\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    ease: 'easeOut'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"bg-primary-neutral h-full overflow-hidden\",\n                                    variant: \"default\",\n                                    padding: \"none\",\n                                    rounded: \"xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-video bg-gradient-to-br from-primary-peach/20 to-primary-green/20 relative overflow-hidden\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"project-image w-full h-full bg-primary-peach/30 transition-transform duration-700 group-hover:scale-110\",\n                                                    style: {\n                                                        backgroundImage: \"linear-gradient(135deg,\\n                        \".concat(index % 3 === 0 ? '#fecf8b40' : index % 3 === 1 ? '#45523e40' : '#01010140', \",\\n                        transparent)\")\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-4 left-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-3 py-1 bg-white/90 text-primary-black text-xs font-medium rounded-full\",\n                                                        children: project.category\n                                                    }, void 0, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"h5\",\n                                                    font: \"satoshi\",\n                                                    weight: \"semibold\",\n                                                    className: \"mb-3 group-hover:text-primary-green transition-colors duration-300\",\n                                                    children: project.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"muted\",\n                                                    className: \"mb-4 line-clamp-2\",\n                                                    children: project.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: [\n                                                        project.technologies.slice(0, 3).map((tech)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded\",\n                                                                children: tech\n                                                            }, tech, false, {\n                                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 23\n                                                            }, undefined)),\n                                                        project.technologies.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-1 bg-primary-black/5 text-primary-black/70 text-xs rounded\",\n                                                            children: [\n                                                                \"+\",\n                                                                project.technologies.length - 3\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, project.id, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/ProjectsSection.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProjectsSection, \"cbnCI6HkycBMvbx/ZdbumNy1sQ0=\", false, function() {\n    return [\n        _hooks_useScrollAnimations__WEBPACK_IMPORTED_MODULE_5__.useScrollAnimations\n    ];\n});\n_c = ProjectsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProjectsSection);\nvar _c;\n$RefreshReg$(_c, \"ProjectsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\n"));

/***/ })

});