"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/sections/SkillsSection.tsx":
/*!***************************************************!*\
  !*** ./src/components/sections/SkillsSection.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\ngsap__WEBPACK_IMPORTED_MODULE_2__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger);\nconst SkillsSection = ()=>{\n    _s();\n    const sectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const skills = [\n        'React',\n        'Next.js',\n        'TypeScript',\n        'Node.js',\n        'Python',\n        'GSAP',\n        'Framer Motion',\n        'Tailwind CSS',\n        'Three.js',\n        'WebGL',\n        'PHP',\n        'MySQL'\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SkillsSection.useEffect\": ()=>{\n            const section = sectionRef.current;\n            if (!section) return;\n            gsap__WEBPACK_IMPORTED_MODULE_2__.gsap.fromTo(section.querySelectorAll('.skill-item'), {\n                opacity: 0,\n                scale: 0.8\n            }, {\n                opacity: 1,\n                scale: 1,\n                duration: 0.6,\n                stagger: 0.1,\n                ease: 'back.out(1.7)',\n                scrollTrigger: {\n                    trigger: section,\n                    start: 'top 80%',\n                    toggleActions: 'play none none reverse'\n                }\n            });\n            return ({\n                \"SkillsSection.useEffect\": ()=>{\n                    gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_3__.ScrollTrigger.getAll().forEach({\n                        \"SkillsSection.useEffect\": (trigger)=>trigger.kill()\n                    }[\"SkillsSection.useEffect\"]);\n                }\n            })[\"SkillsSection.useEffect\"];\n        }\n    }[\"SkillsSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: sectionRef,\n        className: \"py-20 px-6 bg-primary-neutral\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"font-clash text-5xl md:text-6xl font-bold text-center mb-16\",\n                    children: [\n                        \"Skills & \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-primary-peach\",\n                            children: \"Expertise\"\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 20\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                    children: skills.map((skill, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"skill-item bg-white rounded-xl p-6 text-center hover:bg-primary-peach/10 transition-colors duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-satoshi text-lg font-semibold\",\n                                children: skill\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, undefined)\n                        }, skill, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/sections/SkillsSection.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SkillsSection, \"O9MYfDkQexHh+zrn37J6HLSAdf8=\");\n_c = SkillsSection;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SkillsSection);\nvar _c;\n$RefreshReg$(_c, \"SkillsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/sections/SkillsSection.tsx\n"));

/***/ })

});