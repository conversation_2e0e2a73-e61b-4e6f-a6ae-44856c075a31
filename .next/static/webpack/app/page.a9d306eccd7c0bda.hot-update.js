"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/smoothScroll.ts":
/*!*********************************!*\
  !*** ./src/lib/smoothScroll.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLenis: () => (/* binding */ getLenis),\n/* harmony export */   initSmoothScroll: () => (/* binding */ initSmoothScroll),\n/* harmony export */   scrollTo: () => (/* binding */ scrollTo)\n/* harmony export */ });\n/* harmony import */ var gsap__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! gsap */ \"(app-pages-browser)/./node_modules/gsap/index.js\");\n/* harmony import */ var gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! gsap/ScrollTrigger */ \"(app-pages-browser)/./node_modules/gsap/ScrollTrigger.js\");\n\n\n// Register GSAP plugins\ngsap__WEBPACK_IMPORTED_MODULE_0__.gsap.registerPlugin(gsap_ScrollTrigger__WEBPACK_IMPORTED_MODULE_1__.ScrollTrigger);\nlet lenis = null;\nfunction initSmoothScroll() {\n    // For now, let's disable Lenis and use native smooth scroll\n    // This fixes the scrolling issue while maintaining scroll animations\n    // Just enable native smooth scrolling\n    document.documentElement.style.scrollBehavior = 'smooth';\n    // Cleanup function\n    return ()=>{\n        document.documentElement.style.scrollBehavior = 'auto';\n    };\n}\nfunction scrollTo(target, options) {\n    if (lenis) {\n        lenis.scrollTo(target, options);\n    }\n}\nfunction getLenis() {\n    return lenis;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/smoothScroll.ts\n"));

/***/ })

});