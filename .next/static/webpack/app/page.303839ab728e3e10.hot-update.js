"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/preloader.ts":
/*!*********************************!*\
  !*** ./src/config/preloader.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   preloaderConfig: () => (/* binding */ preloaderConfig)\n/* harmony export */ });\n// Preloader Configuration\n// Switch between different preloader styles\nconst preloaderConfig = {\n    // Choose preloader style: 'awwwards' | 'minimal' | 'simple' | 'disabled'\n    style: 'awwwards',\n    // Duration in milliseconds\n    duration: 3000,\n    // Enable/disable preloader entirely\n    enabled: true,\n    // Show preloader only on first visit (uses localStorage)\n    showOnlyOnFirstVisit: false,\n    // Custom loading messages for Awwwards style\n    loadingWords: [\n        'LOADING',\n        'CREATING',\n        'CRAFTING',\n        'BUILDING',\n        'DESIGNING',\n        'ANIMATING',\n        'READY'\n    ],\n    // Animation settings\n    animations: {\n        // Logo entrance delay\n        logoDelay: 0.2,\n        // Progress bar smoothness (lower = smoother, higher = choppier)\n        progressUpdateInterval: 50,\n        // Exit animation duration\n        exitDuration: 1000\n    },\n    // Visual settings\n    visual: {\n        // Show floating particles\n        showParticles: true,\n        // Show animated background\n        showAnimatedBackground: true,\n        // Show geometric shapes\n        showGeometricShapes: true,\n        // Particle count\n        particleCount: 20\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (preloaderConfig);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb25maWcvcHJlbG9hZGVyLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsMEJBQTBCO0FBQzFCLDRDQUE0QztBQUVyQyxNQUFNQSxrQkFBa0I7SUFDN0IseUVBQXlFO0lBQ3pFQyxPQUFPO0lBRVAsMkJBQTJCO0lBQzNCQyxVQUFVO0lBRVYsb0NBQW9DO0lBQ3BDQyxTQUFTO0lBRVQseURBQXlEO0lBQ3pEQyxzQkFBc0I7SUFFdEIsNkNBQTZDO0lBQzdDQyxjQUFjO1FBQ1o7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELHFCQUFxQjtJQUNyQkMsWUFBWTtRQUNWLHNCQUFzQjtRQUN0QkMsV0FBVztRQUVYLGdFQUFnRTtRQUNoRUMsd0JBQXdCO1FBRXhCLDBCQUEwQjtRQUMxQkMsY0FBYztJQUNoQjtJQUVBLGtCQUFrQjtJQUNsQkMsUUFBUTtRQUNOLDBCQUEwQjtRQUMxQkMsZUFBZTtRQUVmLDJCQUEyQjtRQUMzQkMsd0JBQXdCO1FBRXhCLHdCQUF3QjtRQUN4QkMscUJBQXFCO1FBRXJCLGlCQUFpQjtRQUNqQkMsZUFBZTtJQUNqQjtBQUNGLEVBQUM7QUFFRCxpRUFBZWQsZUFBZUEsRUFBQSIsInNvdXJjZXMiOlsiL0FwcGxpY2F0aW9ucy9YQU1QUC94YW1wcGZpbGVzL2h0ZG9jcy9wb3J0Zm9saW8vc3JjL2NvbmZpZy9wcmVsb2FkZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gUHJlbG9hZGVyIENvbmZpZ3VyYXRpb25cbi8vIFN3aXRjaCBiZXR3ZWVuIGRpZmZlcmVudCBwcmVsb2FkZXIgc3R5bGVzXG5cbmV4cG9ydCBjb25zdCBwcmVsb2FkZXJDb25maWcgPSB7XG4gIC8vIENob29zZSBwcmVsb2FkZXIgc3R5bGU6ICdhd3d3YXJkcycgfCAnbWluaW1hbCcgfCAnc2ltcGxlJyB8ICdkaXNhYmxlZCdcbiAgc3R5bGU6ICdhd3d3YXJkcycgYXMgJ2F3d3dhcmRzJyB8ICdtaW5pbWFsJyB8ICdzaW1wbGUnIHwgJ2Rpc2FibGVkJyxcbiAgXG4gIC8vIER1cmF0aW9uIGluIG1pbGxpc2Vjb25kc1xuICBkdXJhdGlvbjogMzAwMCxcbiAgXG4gIC8vIEVuYWJsZS9kaXNhYmxlIHByZWxvYWRlciBlbnRpcmVseVxuICBlbmFibGVkOiB0cnVlLFxuICBcbiAgLy8gU2hvdyBwcmVsb2FkZXIgb25seSBvbiBmaXJzdCB2aXNpdCAodXNlcyBsb2NhbFN0b3JhZ2UpXG4gIHNob3dPbmx5T25GaXJzdFZpc2l0OiBmYWxzZSxcbiAgXG4gIC8vIEN1c3RvbSBsb2FkaW5nIG1lc3NhZ2VzIGZvciBBd3d3YXJkcyBzdHlsZVxuICBsb2FkaW5nV29yZHM6IFtcbiAgICAnTE9BRElORycsXG4gICAgJ0NSRUFUSU5HJyxcbiAgICAnQ1JBRlRJTkcnLCBcbiAgICAnQlVJTERJTkcnLFxuICAgICdERVNJR05JTkcnLFxuICAgICdBTklNQVRJTkcnLFxuICAgICdSRUFEWSdcbiAgXSxcbiAgXG4gIC8vIEFuaW1hdGlvbiBzZXR0aW5nc1xuICBhbmltYXRpb25zOiB7XG4gICAgLy8gTG9nbyBlbnRyYW5jZSBkZWxheVxuICAgIGxvZ29EZWxheTogMC4yLFxuICAgIFxuICAgIC8vIFByb2dyZXNzIGJhciBzbW9vdGhuZXNzIChsb3dlciA9IHNtb290aGVyLCBoaWdoZXIgPSBjaG9wcGllcilcbiAgICBwcm9ncmVzc1VwZGF0ZUludGVydmFsOiA1MCxcbiAgICBcbiAgICAvLyBFeGl0IGFuaW1hdGlvbiBkdXJhdGlvblxuICAgIGV4aXREdXJhdGlvbjogMTAwMCxcbiAgfSxcbiAgXG4gIC8vIFZpc3VhbCBzZXR0aW5nc1xuICB2aXN1YWw6IHtcbiAgICAvLyBTaG93IGZsb2F0aW5nIHBhcnRpY2xlc1xuICAgIHNob3dQYXJ0aWNsZXM6IHRydWUsXG4gICAgXG4gICAgLy8gU2hvdyBhbmltYXRlZCBiYWNrZ3JvdW5kXG4gICAgc2hvd0FuaW1hdGVkQmFja2dyb3VuZDogdHJ1ZSxcbiAgICBcbiAgICAvLyBTaG93IGdlb21ldHJpYyBzaGFwZXNcbiAgICBzaG93R2VvbWV0cmljU2hhcGVzOiB0cnVlLFxuICAgIFxuICAgIC8vIFBhcnRpY2xlIGNvdW50XG4gICAgcGFydGljbGVDb3VudDogMjAsXG4gIH1cbn1cblxuZXhwb3J0IGRlZmF1bHQgcHJlbG9hZGVyQ29uZmlnXG4iXSwibmFtZXMiOlsicHJlbG9hZGVyQ29uZmlnIiwic3R5bGUiLCJkdXJhdGlvbiIsImVuYWJsZWQiLCJzaG93T25seU9uRmlyc3RWaXNpdCIsImxvYWRpbmdXb3JkcyIsImFuaW1hdGlvbnMiLCJsb2dvRGVsYXkiLCJwcm9ncmVzc1VwZGF0ZUludGVydmFsIiwiZXhpdER1cmF0aW9uIiwidmlzdWFsIiwic2hvd1BhcnRpY2xlcyIsInNob3dBbmltYXRlZEJhY2tncm91bmQiLCJzaG93R2VvbWV0cmljU2hhcGVzIiwicGFydGljbGVDb3VudCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/preloader.ts\n"));

/***/ })

});