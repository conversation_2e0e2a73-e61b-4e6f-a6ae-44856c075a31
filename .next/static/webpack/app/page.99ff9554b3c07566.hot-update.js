"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/HeroSection */ \"(app-pages-browser)/./src/components/sections/HeroSection.tsx\");\n/* harmony import */ var _components_sections_ProjectsSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/ProjectsSection */ \"(app-pages-browser)/./src/components/sections/ProjectsSection.tsx\");\n/* harmony import */ var _components_sections_SkillsSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sections/SkillsSection */ \"(app-pages-browser)/./src/components/sections/SkillsSection.tsx\");\n/* harmony import */ var _components_sections_AboutSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/sections/AboutSection */ \"(app-pages-browser)/./src/components/sections/AboutSection.tsx\");\n/* harmony import */ var _components_sections_ContactSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/sections/ContactSection */ \"(app-pages-browser)/./src/components/sections/ContactSection.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/Footer */ \"(app-pages-browser)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_ui_CustomCursor__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/CustomCursor */ \"(app-pages-browser)/./src/components/ui/CustomCursor.tsx\");\n/* harmony import */ var _components_ui_ScrollProgress__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/ScrollProgress */ \"(app-pages-browser)/./src/components/ui/ScrollProgress.tsx\");\n/* harmony import */ var _components_ui_ClickFeedback__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ClickFeedback */ \"(app-pages-browser)/./src/components/ui/ClickFeedback.tsx\");\n/* harmony import */ var _components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/PageTransition */ \"(app-pages-browser)/./src/components/ui/PageTransition.tsx\");\n/* harmony import */ var _components_ui_AwwardsPreloader__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/AwwardsPreloader */ \"(app-pages-browser)/./src/components/ui/AwwardsPreloader.tsx\");\n/* harmony import */ var _lib_smoothScroll__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/smoothScroll */ \"(app-pages-browser)/./src/lib/smoothScroll.ts\");\n/* harmony import */ var _hooks_usePerformance__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/usePerformance */ \"(app-pages-browser)/./src/hooks/usePerformance.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    // Performance monitoring\n    const { metrics, grade } = (0,_hooks_usePerformance__WEBPACK_IMPORTED_MODULE_14__.usePerformance)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Initialize smooth scroll\n            const cleanup = (0,_lib_smoothScroll__WEBPACK_IMPORTED_MODULE_13__.initSmoothScroll)();\n            return cleanup;\n        }\n    }[\"Home.useEffect\"], []);\n    const handlePreloaderComplete = ()=>{\n        setIsLoading(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_AwwardsPreloader__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                onComplete: handlePreloaderComplete\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_PageTransition__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_CustomCursor__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ScrollProgress__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ClickFeedback__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_HeroSection__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_ProjectsSection__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_SkillsSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_AboutSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_ContactSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/page.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Home, \"XrKmhbREdB/GQDA6EcnD2KdVp70=\", false, function() {\n    return [\n        _hooks_usePerformance__WEBPACK_IMPORTED_MODULE_14__.usePerformance\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});