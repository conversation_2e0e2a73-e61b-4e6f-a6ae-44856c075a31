/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRkFwcGxpY2F0aW9ucyUyRlhBTVBQJTJGeGFtcHBmaWxlcyUyRmh0ZG9jcyUyRnBvcnRmb2xpbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRkFwcGxpY2F0aW9ucyUyRlhBTVBQJTJGeGFtcHBmaWxlcyUyRmh0ZG9jcyUyRnBvcnRmb2xpbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRkFwcGxpY2F0aW9ucyUyRlhBTVBQJTJGeGFtcHBmaWxlcyUyRmh0ZG9jcyUyRnBvcnRmb2xpbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRkFwcGxpY2F0aW9ucyUyRlhBTVBQJTJGeGFtcHBmaWxlcyUyRmh0ZG9jcyUyRnBvcnRmb2xpbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmh0dHAtYWNjZXNzLWZhbGxiYWNrJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGQXBwbGljYXRpb25zJTJGWEFNUFAlMkZ4YW1wcGZpbGVzJTJGaHRkb2NzJTJGcG9ydGZvbGlvJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZBcHBsaWNhdGlvbnMlMkZYQU1QUCUyRnhhbXBwZmlsZXMlMkZodGRvY3MlMkZwb3J0Zm9saW8lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRkFwcGxpY2F0aW9ucyUyRlhBTVBQJTJGeGFtcHBmaWxlcyUyRmh0ZG9jcyUyRnBvcnRmb2xpbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGQXBwbGljYXRpb25zJTJGWEFNUFAlMkZ4YW1wcGZpbGVzJTJGaHRkb2NzJTJGcG9ydGZvbGlvJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTRJO0FBQzVJO0FBQ0EsME9BQStJO0FBQy9JO0FBQ0EsME9BQStJO0FBQy9JO0FBQ0Esb1JBQW9LO0FBQ3BLO0FBQ0Esd09BQThJO0FBQzlJO0FBQ0EsNFBBQXdKO0FBQ3hKO0FBQ0Esa1FBQTJKO0FBQzNKO0FBQ0Esc1FBQTZKIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1wYWdlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2h0dHAtYWNjZXNzLWZhbGxiYWNrL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9BcHBsaWNhdGlvbnMvWEFNUFAveGFtcHBmaWxlcy9odGRvY3MvcG9ydGZvbGlvL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9BcHBsaWNhdGlvbnMvWEFNUFAveGFtcHBmaWxlcy9odGRvY3MvcG9ydGZvbGlvL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvbWV0YWRhdGEtYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9BcHBsaWNhdGlvbnMvWEFNUFAveGFtcHBmaWxlcy9odGRvY3MvcG9ydGZvbGlvL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/ClientLayout.tsx */ \"(rsc)/./src/components/layout/ClientLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ThemeProvider.tsx */ \"(rsc)/./src/components/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/seo/GTMNoScript.tsx */ \"(rsc)/./src/components/seo/GTMNoScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/seo/MetaHead.tsx */ \"(rsc)/./src/components/seo/MetaHead.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d59564aac431\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvQXBwbGljYXRpb25zL1hBTVBQL3hhbXBwZmlsZXMvaHRkb2NzL3BvcnRmb2xpby9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZDU5NTY0YWFjNDMxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_seo_MetaHead__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/seo/MetaHead */ \"(rsc)/./src/components/seo/MetaHead.tsx\");\n/* harmony import */ var _components_seo_GTMNoScript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/seo/GTMNoScript */ \"(rsc)/./src/components/seo/GTMNoScript.tsx\");\n/* harmony import */ var _components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/ThemeProvider */ \"(rsc)/./src/components/providers/ThemeProvider.tsx\");\n/* harmony import */ var _components_layout_ClientLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/ClientLayout */ \"(rsc)/./src/components/layout/ClientLayout.tsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: 'Portfolio | Award-Winning Developer',\n    description: 'A stunning portfolio showcasing cutting-edge web development and design',\n    keywords: [\n        'portfolio',\n        'web development',\n        'design',\n        'awwwards',\n        'creative'\n    ],\n    authors: [\n        {\n            name: 'Your Name'\n        }\n    ],\n    viewport: 'width=device-width, initial-scale=1',\n    robots: 'index, follow',\n    openGraph: {\n        title: 'Portfolio | Award-Winning Developer',\n        description: 'A stunning portfolio showcasing cutting-edge web development and design',\n        type: 'website',\n        locale: 'en_US'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_6___default().className)} antialiased bg-primary-neutral dark:bg-primary-black text-primary-black dark:text-primary-neutral transition-colors duration-300`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_MetaHead__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_GTMNoScript__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_ThemeProvider__WEBPACK_IMPORTED_MODULE_4__.ThemeProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"smooth-wrapper\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"smooth-content\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_ClientLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/app/layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/ClientLayout.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/ClientLayout.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/providers/ThemeProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ThemeProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),
/* harmony export */   useTheme: () => (/* binding */ useTheme)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const useTheme = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/providers/ThemeProvider.tsx",
"useTheme",
);const ThemeProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/providers/ThemeProvider.tsx",
"ThemeProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/seo/GTMNoScript.tsx":
/*!********************************************!*\
  !*** ./src/components/seo/GTMNoScript.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/GTMNoScript.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/GTMNoScript.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/seo/MetaHead.tsx":
/*!*****************************************!*\
  !*** ./src/components/seo/MetaHead.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/MetaHead.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/MetaHead.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/ClientLayout.tsx */ \"(ssr)/./src/components/layout/ClientLayout.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/ThemeProvider.tsx */ \"(ssr)/./src/components/providers/ThemeProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/seo/GTMNoScript.tsx */ \"(ssr)/./src/components/seo/GTMNoScript.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/seo/MetaHead.tsx */ \"(ssr)/./src/components/seo/MetaHead.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Flayout%2FClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fproviders%2FThemeProvider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FGTMNoScript.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fcomponents%2Fseo%2FMetaHead.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/ClientLayout.tsx":
/*!************************************************!*\
  !*** ./src/components/layout/ClientLayout.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Navigation */ \"(ssr)/./src/components/layout/Navigation.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navigation__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/ClientLayout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvQ2xpZW50TGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUVxQztBQU10QixTQUFTQyxhQUFhLEVBQUVDLFFBQVEsRUFBcUI7SUFDbEUscUJBQ0U7OzBCQUNFLDhEQUFDRixtREFBVUE7Ozs7O1lBQ1ZFOzs7QUFHUCIsInNvdXJjZXMiOlsiL0FwcGxpY2F0aW9ucy9YQU1QUC94YW1wcGZpbGVzL2h0ZG9jcy9wb3J0Zm9saW8vc3JjL2NvbXBvbmVudHMvbGF5b3V0L0NsaWVudExheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBOYXZpZ2F0aW9uIGZyb20gJy4vTmF2aWdhdGlvbidcblxuaW50ZXJmYWNlIENsaWVudExheW91dFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDbGllbnRMYXlvdXQoeyBjaGlsZHJlbiB9OiBDbGllbnRMYXlvdXRQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8TmF2aWdhdGlvbiAvPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvPlxuICApXG59XG4iXSwibmFtZXMiOlsiTmF2aWdhdGlvbiIsIkNsaWVudExheW91dCIsImNoaWxkcmVuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(ssr)/./src/components/ui/Typography.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst Navigation = ()=>{\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scrolled, setScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const navItems = [\n        {\n            name: 'Home',\n            href: '#home',\n            preview: 'Creative Developer Portfolio'\n        },\n        {\n            name: 'Projects',\n            href: '#projects',\n            preview: 'Featured Work & Case Studies'\n        },\n        {\n            name: 'Skills',\n            href: '#skills',\n            preview: 'Technical Expertise'\n        },\n        {\n            name: 'About',\n            href: '#about',\n            preview: 'My Journey & Experience'\n        },\n        {\n            name: 'Contact',\n            href: '#contact',\n            preview: 'Let\\'s Work Together'\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    setScrolled(window.scrollY > 50);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    const handleNavClick = (href)=>{\n        setIsOpen(false);\n        const element = document.querySelector(href);\n        if (element) {\n            element.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.nav, {\n                className: `fixed top-0 left-0 right-0 z-40 transition-all duration-300 ${scrolled ? 'bg-white/90 backdrop-blur-md shadow-lg' : 'bg-transparent'}`,\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.6,\n                    ease: 'easeOut'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"font-clash text-2xl font-bold text-primary-black\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                \"data-cursor\": \"pointer\",\n                                children: \"YN\"\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        onMouseEnter: ()=>setHoveredItem(item.name),\n                                        onMouseLeave: ()=>setHoveredItem(null),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                href: item.href,\n                                                onClick: (e)=>{\n                                                    e.preventDefault();\n                                                    handleNavClick(item.href);\n                                                },\n                                                className: \"text-primary-black hover:text-primary-green transition-colors duration-300 font-medium\",\n                                                whileHover: {\n                                                    y: -2\n                                                },\n                                                \"data-cursor\": \"pointer\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                                                children: hoveredItem === item.name && item.preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                    className: \"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-2 bg-primary-black text-white text-sm rounded-lg whitespace-nowrap\",\n                                                    initial: {\n                                                        opacity: 0,\n                                                        y: -10,\n                                                        scale: 0.9\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        y: 0,\n                                                        scale: 1\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        y: -10,\n                                                        scale: 0.9\n                                                    },\n                                                    transition: {\n                                                        duration: 0.2\n                                                    },\n                                                    children: [\n                                                        item.preview,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-primary-black rotate-45\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, item.name, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                className: \"md:hidden w-10 h-10 flex flex-col items-center justify-center space-y-1\",\n                                onClick: ()=>setIsOpen(!isOpen),\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                \"data-cursor\": \"pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                                        className: \"w-6 h-0.5 bg-primary-black transition-all duration-300\",\n                                        animate: {\n                                            rotate: isOpen ? 45 : 0,\n                                            y: isOpen ? 4 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                                        className: \"w-6 h-0.5 bg-primary-black transition-all duration-300\",\n                                        animate: {\n                                            opacity: isOpen ? 0 : 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.span, {\n                                        className: \"w-6 h-0.5 bg-primary-black transition-all duration-300\",\n                                        animate: {\n                                            rotate: isOpen ? -45 : 0,\n                                            y: isOpen ? -4 : 0\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"fixed inset-0 z-30 md:hidden\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"absolute inset-0 bg-black/50 backdrop-blur-sm\",\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            onClick: ()=>setIsOpen(false)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"absolute top-0 right-0 w-80 h-full bg-white shadow-2xl\",\n                            initial: {\n                                x: '100%'\n                            },\n                            animate: {\n                                x: 0\n                            },\n                            exit: {\n                                x: '100%'\n                            },\n                            transition: {\n                                duration: 0.3,\n                                ease: 'easeOut'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8 pt-20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: navItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                transition: {\n                                                    delay: index * 0.1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: item.href,\n                                                    onClick: (e)=>{\n                                                        e.preventDefault();\n                                                        handleNavClick(item.href);\n                                                    },\n                                                    className: \"block py-3 border-b border-primary-neutral\",\n                                                    \"data-cursor\": \"pointer\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            variant: \"h5\",\n                                                            font: \"satoshi\",\n                                                            weight: \"semibold\",\n                                                            className: \"mb-1\",\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        item.preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            color: \"muted\",\n                                                            className: \"text-sm\",\n                                                            children: item.preview\n                                                        }, void 0, false, {\n                                                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, item.name, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                variant: \"h6\",\n                                                font: \"satoshi\",\n                                                weight: \"semibold\",\n                                                className: \"mb-4\",\n                                                children: \"Follow Me\"\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-4\",\n                                                children: [\n                                                    'GitHub',\n                                                    'LinkedIn',\n                                                    'Twitter'\n                                                ].map((social)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.a, {\n                                                        href: \"#\",\n                                                        className: \"w-10 h-10 bg-primary-neutral rounded-full flex items-center justify-center text-xs font-bold hover:bg-primary-peach transition-colors\",\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.95\n                                                        },\n                                                        \"data-cursor\": \"pointer\",\n                                                        children: social.slice(0, 2)\n                                                    }, social, false, {\n                                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/layout/Navigation.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/ThemeProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/ThemeProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useTheme,ThemeProvider auto */ \n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\nfunction ThemeProvider({ children, defaultTheme = 'light' }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultTheme);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            // Check for saved theme preference or default to 'light'\n            const savedTheme = localStorage.getItem('theme');\n            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n            const initialTheme = savedTheme || systemTheme;\n            setThemeState(initialTheme);\n            setMounted(true);\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            if (!mounted) return;\n            const root = window.document.documentElement;\n            // Remove previous theme classes\n            root.classList.remove('light', 'dark');\n            // Add current theme class\n            root.classList.add(theme);\n            // Save to localStorage\n            localStorage.setItem('theme', theme);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme,\n        mounted\n    ]);\n    const toggleTheme = ()=>{\n        setThemeState((prev)=>prev === 'light' ? 'dark' : 'light');\n    };\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n    };\n    // Prevent hydration mismatch\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                visibility: 'hidden'\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/providers/ThemeProvider.tsx\",\n            lineNumber: 67,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: {\n            theme,\n            toggleTheme,\n            setTheme\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/providers/ThemeProvider.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/ThemeProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/seo/GTMNoScript.tsx":
/*!********************************************!*\
  !*** ./src/components/seo/GTMNoScript.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GTMNoScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction GTMNoScript() {\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GTMNoScript.useEffect\": ()=>{\n            const loadAnalytics = {\n                \"GTMNoScript.useEffect.loadAnalytics\": async ()=>{\n                    try {\n                        const analyticsData = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.fetchAnalyticsSettings)();\n                        setAnalytics(analyticsData);\n                    } catch (error) {\n                        console.error('Failed to load analytics settings:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"GTMNoScript.useEffect.loadAnalytics\"];\n            loadAnalytics();\n        }\n    }[\"GTMNoScript.useEffect\"], []);\n    if (loading || !analytics.google_tag_manager_id) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"noscript\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n            src: `https://www.googletagmanager.com/ns.html?id=${analytics.google_tag_manager_id}`,\n            height: \"0\",\n            width: \"0\",\n            style: {\n                display: 'none',\n                visibility: 'hidden'\n            }\n        }, void 0, false, {\n            fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/GTMNoScript.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/seo/GTMNoScript.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/seo/GTMNoScript.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/seo/MetaHead.tsx":
/*!*****************************************!*\
  !*** ./src/components/seo/MetaHead.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MetaHead)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction MetaHead({ title, description, image, url }) {\n    const [metaTags, setMetaTags] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [analytics, setAnalytics] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"MetaHead.useEffect\": ()=>{\n            const loadMetaData = {\n                \"MetaHead.useEffect.loadMetaData\": async ()=>{\n                    try {\n                        const [metaData, analyticsData] = await Promise.all([\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_1__.fetchMetaTags)(),\n                            (0,_lib_api__WEBPACK_IMPORTED_MODULE_1__.fetchAnalyticsSettings)()\n                        ]);\n                        setMetaTags(metaData);\n                        setAnalytics(analyticsData);\n                    } catch (error) {\n                        console.error('Failed to load meta data:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"MetaHead.useEffect.loadMetaData\"];\n            loadMetaData();\n        }\n    }[\"MetaHead.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"MetaHead.useEffect\": ()=>{\n            if (loading) return;\n            // Use props or fallback to API data\n            const pageTitle = title || metaTags.site_name || 'Portfolio';\n            const pageDescription = description || metaTags.site_description || 'Professional portfolio website';\n            const pageImage = image || metaTags.og_image;\n            const pageUrl = url || metaTags.canonical_url || ( false ? 0 : '');\n            // Update document title\n            if (pageTitle) {\n                document.title = pageTitle;\n            }\n            // Update or create meta tags\n            const updateMetaTag = {\n                \"MetaHead.useEffect.updateMetaTag\": (name, content, property)=>{\n                    if (!content) return;\n                    const selector = property ? `meta[property=\"${property}\"]` : `meta[name=\"${name}\"]`;\n                    let meta = document.querySelector(selector);\n                    if (!meta) {\n                        meta = document.createElement('meta');\n                        if (property) {\n                            meta.setAttribute('property', property);\n                        } else {\n                            meta.setAttribute('name', name);\n                        }\n                        document.head.appendChild(meta);\n                    }\n                    meta.setAttribute('content', content);\n                }\n            }[\"MetaHead.useEffect.updateMetaTag\"];\n            // Basic meta tags\n            updateMetaTag('description', pageDescription);\n            if (metaTags.site_keywords) updateMetaTag('keywords', metaTags.site_keywords);\n            if (metaTags.meta_author) updateMetaTag('author', metaTags.meta_author);\n            if (metaTags.meta_robots) updateMetaTag('robots', metaTags.meta_robots);\n            // Open Graph tags\n            updateMetaTag('', metaTags.og_title || pageTitle, 'og:title');\n            updateMetaTag('', metaTags.og_description || pageDescription, 'og:description');\n            updateMetaTag('', 'website', 'og:type');\n            if (pageUrl) updateMetaTag('', pageUrl, 'og:url');\n            if (pageImage) updateMetaTag('', pageImage, 'og:image');\n            // Twitter Card tags\n            updateMetaTag('twitter:card', metaTags.twitter_card || 'summary_large_image');\n            if (metaTags.twitter_site) updateMetaTag('twitter:site', metaTags.twitter_site);\n            updateMetaTag('twitter:title', metaTags.og_title || pageTitle);\n            updateMetaTag('twitter:description', metaTags.og_description || pageDescription);\n            if (pageImage) updateMetaTag('twitter:image', pageImage);\n            // Canonical URL\n            if (pageUrl) {\n                let canonical = document.querySelector('link[rel=\"canonical\"]');\n                if (!canonical) {\n                    canonical = document.createElement('link');\n                    canonical.setAttribute('rel', 'canonical');\n                    document.head.appendChild(canonical);\n                }\n                canonical.setAttribute('href', pageUrl);\n            }\n            // Structured Data\n            if (metaTags.structured_data) {\n                let script = document.querySelector('script[type=\"application/ld+json\"]');\n                if (!script) {\n                    script = document.createElement('script');\n                    script.setAttribute('type', 'application/ld+json');\n                    document.head.appendChild(script);\n                }\n                script.textContent = typeof metaTags.structured_data === 'string' ? metaTags.structured_data : JSON.stringify(metaTags.structured_data);\n            }\n        }\n    }[\"MetaHead.useEffect\"], [\n        loading,\n        metaTags,\n        analytics,\n        title,\n        description,\n        image,\n        url\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"MetaHead.useEffect\": ()=>{\n            if (loading || !analytics) return;\n            // Google Analytics\n            if (analytics.google_analytics_id && !document.querySelector(`script[src*=\"${analytics.google_analytics_id}\"]`)) {\n                const script1 = document.createElement('script');\n                script1.async = true;\n                script1.src = `https://www.googletagmanager.com/gtag/js?id=${analytics.google_analytics_id}`;\n                document.head.appendChild(script1);\n                const script2 = document.createElement('script');\n                script2.textContent = `\n        window.dataLayer = window.dataLayer || [];\n        function gtag(){dataLayer.push(arguments);}\n        gtag('js', new Date());\n        gtag('config', '${analytics.google_analytics_id}');\n      `;\n                document.head.appendChild(script2);\n            }\n            // Google Tag Manager\n            if (analytics.google_tag_manager_id && !document.querySelector(`script[src*=\"${analytics.google_tag_manager_id}\"]`)) {\n                const script = document.createElement('script');\n                script.textContent = `\n        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n        })(window,document,'script','dataLayer','${analytics.google_tag_manager_id}');\n      `;\n                document.head.appendChild(script);\n            }\n            // Facebook Pixel\n            if (analytics.facebook_pixel_id && !window.fbq) {\n                const script = document.createElement('script');\n                script.textContent = `\n        !function(f,b,e,v,n,t,s)\n        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?\n        n.callMethod.apply(n,arguments):n.queue.push(arguments)};\n        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';\n        n.queue=[];t=b.createElement(e);t.async=!0;\n        t.src=v;s=b.getElementsByTagName(e)[0];\n        s.parentNode.insertBefore(t,s)}(window, document,'script',\n        'https://connect.facebook.net/en_US/fbevents.js');\n        fbq('init', '${analytics.facebook_pixel_id}');\n        fbq('track', 'PageView');\n      `;\n                document.head.appendChild(script);\n            }\n            // Hotjar\n            if (analytics.hotjar_id && !window.hj) {\n                const script = document.createElement('script');\n                script.textContent = `\n        (function(h,o,t,j,a,r){\n          h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};\n          h._hjSettings={hjid:${analytics.hotjar_id},hjsv:6};\n          a=o.getElementsByTagName('head')[0];\n          r=o.createElement('script');r.async=1;\n          r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;\n          a.appendChild(r);\n        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');\n      `;\n                document.head.appendChild(script);\n            }\n        }\n    }[\"MetaHead.useEffect\"], [\n        loading,\n        analytics\n    ]);\n    return null // This component doesn't render anything visible\n    ;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/seo/MetaHead.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Typography.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/Typography.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst Typography = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant = 'body', font = 'inter', weight = 'normal', color = 'primary', as, children, ...props }, ref)=>{\n    const variants = {\n        h1: 'text-6xl md:text-8xl lg:text-9xl leading-none',\n        h2: 'text-4xl md:text-5xl lg:text-6xl leading-tight',\n        h3: 'text-3xl md:text-4xl leading-tight',\n        h4: 'text-2xl md:text-3xl leading-snug',\n        h5: 'text-xl md:text-2xl leading-snug',\n        h6: 'text-lg md:text-xl leading-normal',\n        body: 'text-base md:text-lg leading-relaxed',\n        caption: 'text-sm leading-normal',\n        overline: 'text-xs uppercase tracking-wider leading-normal'\n    };\n    const fonts = {\n        clash: 'font-clash',\n        satoshi: 'font-satoshi',\n        inter: 'font-inter'\n    };\n    const weights = {\n        light: 'font-light',\n        normal: 'font-normal',\n        medium: 'font-medium',\n        semibold: 'font-semibold',\n        bold: 'font-bold'\n    };\n    const colors = {\n        primary: 'text-primary-black',\n        secondary: 'text-primary-green',\n        muted: 'text-primary-black/70',\n        accent: 'text-primary-peach'\n    };\n    const defaultElements = {\n        h1: 'h1',\n        h2: 'h2',\n        h3: 'h3',\n        h4: 'h4',\n        h5: 'h5',\n        h6: 'h6',\n        body: 'p',\n        caption: 'span',\n        overline: 'span'\n    };\n    const Component = as || defaultElements[variant] || 'p';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(variants[variant], fonts[font], weights[weight], colors[color], className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Applications/XAMPP/xamppfiles/htdocs/portfolio/src/components/ui/Typography.tsx\",\n        lineNumber: 74,\n        columnNumber: 7\n    }, undefined);\n});\nTypography.displayName = 'Typography';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Typography);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Typography.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchAboutSection: () => (/* binding */ fetchAboutSection),\n/* harmony export */   fetchAnalyticsSettings: () => (/* binding */ fetchAnalyticsSettings),\n/* harmony export */   fetchContactInfo: () => (/* binding */ fetchContactInfo),\n/* harmony export */   fetchFeaturedProjects: () => (/* binding */ fetchFeaturedProjects),\n/* harmony export */   fetchHeroSection: () => (/* binding */ fetchHeroSection),\n/* harmony export */   fetchMetaTags: () => (/* binding */ fetchMetaTags),\n/* harmony export */   fetchProject: () => (/* binding */ fetchProject),\n/* harmony export */   fetchProjects: () => (/* binding */ fetchProjects),\n/* harmony export */   fetchSettingsByGroup: () => (/* binding */ fetchSettingsByGroup),\n/* harmony export */   fetchSiteSettings: () => (/* binding */ fetchSiteSettings),\n/* harmony export */   fetchSkillCategories: () => (/* binding */ fetchSkillCategories),\n/* harmony export */   fetchSkills: () => (/* binding */ fetchSkills),\n/* harmony export */   fetchSocialLinks: () => (/* binding */ fetchSocialLinks),\n/* harmony export */   fetchTimeline: () => (/* binding */ fetchTimeline),\n/* harmony export */   filterProjectsByType: () => (/* binding */ filterProjectsByType),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getAllTechnologies: () => (/* binding */ getAllTechnologies),\n/* harmony export */   getAssetUrl: () => (/* binding */ getAssetUrl),\n/* harmony export */   getProjectYear: () => (/* binding */ getProjectYear),\n/* harmony export */   getTechnologyCategories: () => (/* binding */ getTechnologyCategories),\n/* harmony export */   isFeaturedProject: () => (/* binding */ isFeaturedProject),\n/* harmony export */   searchProjects: () => (/* binding */ searchProjects),\n/* harmony export */   sortProjectsByDate: () => (/* binding */ sortProjectsByDate),\n/* harmony export */   submitContactForm: () => (/* binding */ submitContactForm)\n/* harmony export */ });\n// API utilities for fetching data from Laravel Admin Panel\n// API Configuration\nconst API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\n// Generic API fetch function\nasync function apiRequest(endpoint) {\n    try {\n        const response = await fetch(`${API_BASE_URL}${endpoint}`, {\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return result.data;\n    } catch (error) {\n        console.error(`Error fetching ${endpoint}:`, error);\n        throw error;\n    }\n}\n// Hero Section API\nasync function fetchHeroSection() {\n    try {\n        return await apiRequest('/hero');\n    } catch (error) {\n        console.error('Error fetching hero section:', error);\n        return null;\n    }\n}\n// Projects API\nasync function fetchProjects() {\n    try {\n        return await apiRequest('/projects');\n    } catch (error) {\n        console.error('Error fetching projects:', error);\n        return getFallbackProjects();\n    }\n}\nasync function fetchFeaturedProjects() {\n    try {\n        return await apiRequest('/projects/featured');\n    } catch (error) {\n        console.error('Error fetching featured projects:', error);\n        return getFallbackProjects().slice(0, 3);\n    }\n}\nasync function fetchProject(slug) {\n    try {\n        return await apiRequest(`/projects/${slug}`);\n    } catch (error) {\n        console.error(`Error fetching project ${slug}:`, error);\n        return null;\n    }\n}\n// About Section API\nasync function fetchAboutSection() {\n    try {\n        return await apiRequest('/about');\n    } catch (error) {\n        console.error('Error fetching about section:', error);\n        return null;\n    }\n}\nasync function fetchTimeline() {\n    try {\n        return await apiRequest('/timeline');\n    } catch (error) {\n        console.error('Error fetching timeline:', error);\n        return [];\n    }\n}\n// Skills API\nasync function fetchSkills() {\n    try {\n        return await apiRequest('/skills');\n    } catch (error) {\n        console.error('Error fetching skills:', error);\n        return [];\n    }\n}\nasync function fetchSkillCategories() {\n    try {\n        return await apiRequest('/skills/categories');\n    } catch (error) {\n        console.error('Error fetching skill categories:', error);\n        return [];\n    }\n}\n// Contact API\nasync function fetchContactInfo() {\n    try {\n        return await apiRequest('/contact');\n    } catch (error) {\n        console.error('Error fetching contact info:', error);\n        return null;\n    }\n}\nasync function fetchSocialLinks() {\n    try {\n        return await apiRequest('/social-links');\n    } catch (error) {\n        console.error('Error fetching social links:', error);\n        return [];\n    }\n}\nasync function submitContactForm(data) {\n    try {\n        const response = await fetch(`${API_BASE_URL}/contact`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Accept': 'application/json'\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || `HTTP error! status: ${response.status}`);\n        }\n        const result = await response.json();\n        return result.data;\n    } catch (error) {\n        console.error('Error submitting contact form:', error);\n        throw error;\n    }\n}\n// Settings API\nasync function fetchSiteSettings() {\n    try {\n        return await apiRequest('/settings');\n    } catch (error) {\n        console.error('Error fetching site settings:', error);\n        return {};\n    }\n}\nasync function fetchSettingsByGroup(group) {\n    try {\n        return await apiRequest(`/settings/group/${group}`);\n    } catch (error) {\n        console.error(`Error fetching settings for group ${group}:`, error);\n        return {};\n    }\n}\nasync function fetchMetaTags() {\n    try {\n        return await apiRequest('/meta-tags');\n    } catch (error) {\n        console.error('Error fetching meta tags:', error);\n        return {};\n    }\n}\nasync function fetchAnalyticsSettings() {\n    try {\n        return await apiRequest('/analytics');\n    } catch (error) {\n        console.error('Error fetching analytics settings:', error);\n        return {};\n    }\n}\n// Fallback projects data (matching Laravel API structure)\nfunction getFallbackProjects() {\n    return [\n        {\n            id: 1,\n            title: 'E-commerce Platform',\n            slug: 'e-commerce-platform',\n            description: 'A modern e-commerce platform built with Laravel backend and React frontend, featuring secure payment processing, inventory management, and real-time analytics.',\n            short_description: 'A modern e-commerce platform with Laravel backend and React frontend.',\n            featured_image: '/images/project1.jpg',\n            gallery_images: [\n                '/images/project1-1.jpg',\n                '/images/project1-2.jpg'\n            ],\n            project_video: null,\n            project_type: 'featured',\n            live_url: 'https://example.com',\n            github_url: 'https://github.com/username/project1',\n            client: 'Tech Startup Inc.',\n            completion_date: '2024-06-15T00:00:00.000000Z',\n            technologies: [\n                {\n                    id: 1,\n                    name: 'Laravel',\n                    icon: null,\n                    color: '#FF2D20',\n                    category: 'backend'\n                },\n                {\n                    id: 2,\n                    name: 'React',\n                    icon: null,\n                    color: '#61DAFB',\n                    category: 'frontend'\n                },\n                {\n                    id: 3,\n                    name: 'MySQL',\n                    icon: null,\n                    color: '#4479A1',\n                    category: 'database'\n                },\n                {\n                    id: 4,\n                    name: 'Tailwind CSS',\n                    icon: null,\n                    color: '#06B6D4',\n                    category: 'frontend'\n                }\n            ],\n            created_at: '2024-01-15T00:00:00.000000Z',\n            updated_at: '2024-06-15T00:00:00.000000Z'\n        },\n        {\n            id: 2,\n            title: 'Task Management System',\n            slug: 'task-management-system',\n            description: 'A comprehensive task management system with team collaboration features, real-time updates, and advanced project tracking capabilities.',\n            short_description: 'A comprehensive task management system with team collaboration features.',\n            featured_image: '/images/project2.jpg',\n            gallery_images: [\n                '/images/project2-1.jpg',\n                '/images/project2-2.jpg'\n            ],\n            project_video: null,\n            project_type: 'normal',\n            live_url: 'https://tasks.example.com',\n            github_url: 'https://github.com/username/task-manager',\n            client: 'Remote Team Solutions',\n            completion_date: '2024-03-20T00:00:00.000000Z',\n            technologies: [\n                {\n                    id: 5,\n                    name: 'Vue.js',\n                    icon: null,\n                    color: '#4FC08D',\n                    category: 'frontend'\n                },\n                {\n                    id: 6,\n                    name: 'Node.js',\n                    icon: null,\n                    color: '#339933',\n                    category: 'backend'\n                },\n                {\n                    id: 7,\n                    name: 'MongoDB',\n                    icon: null,\n                    color: '#47A248',\n                    category: 'database'\n                },\n                {\n                    id: 8,\n                    name: 'Socket.io',\n                    icon: null,\n                    color: '#010101',\n                    category: 'realtime'\n                }\n            ],\n            created_at: '2024-01-10T00:00:00.000000Z',\n            updated_at: '2024-03-20T00:00:00.000000Z'\n        }\n    ];\n}\n// Utility functions for working with API data\n// Get unique technology categories from projects\nfunction getTechnologyCategories(projects) {\n    const categories = projects.flatMap((project)=>project.technologies.map((tech)=>tech.category || 'other'));\n    return Array.from(new Set(categories));\n}\n// Filter projects by type\nfunction filterProjectsByType(projects, type) {\n    if (type === 'all') return projects;\n    return projects.filter((project)=>project.project_type === type);\n}\n// Sort projects by completion date (newest first)\nfunction sortProjectsByDate(projects) {\n    return [\n        ...projects\n    ].sort((a, b)=>{\n        const dateA = new Date(a.completion_date || a.created_at);\n        const dateB = new Date(b.completion_date || b.created_at);\n        return dateB.getTime() - dateA.getTime();\n    });\n}\n// Get all technologies from projects\nfunction getAllTechnologies(projects) {\n    const allTechs = projects.flatMap((project)=>project.technologies);\n    const uniqueTechs = allTechs.filter((tech, index, self)=>index === self.findIndex((t)=>t.id === tech.id));\n    return uniqueTechs;\n}\n// Format date for display\nfunction formatDate(dateString) {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n}\n// Get asset URL (handle both relative and absolute URLs)\nfunction getAssetUrl(path) {\n    if (!path) return null;\n    if (path.startsWith('http')) return path;\n    return `${\"http://localhost:8000/api/v1\"?.replace('/api/v1', '') || 0}/storage/${path}`;\n}\n// Check if project is featured\nfunction isFeaturedProject(project) {\n    return project.project_type === 'featured';\n}\n// Get project completion year\nfunction getProjectYear(project) {\n    const date = new Date(project.completion_date || project.created_at);\n    return date.getFullYear();\n}\n// Search projects by title or description\nfunction searchProjects(projects, query) {\n    const lowercaseQuery = query.toLowerCase();\n    return projects.filter((project)=>project.title.toLowerCase().includes(lowercaseQuery) || project.description.toLowerCase().includes(lowercaseQuery) || project.short_description?.toLowerCase().includes(lowercaseQuery) || project.technologies.some((tech)=>tech.name.toLowerCase().includes(lowercaseQuery)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   breakpoints: () => (/* binding */ breakpoints),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   colors: () => (/* binding */ colors),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   durations: () => (/* binding */ durations),\n/* harmony export */   easings: () => (/* binding */ easings),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   spacing: () => (/* binding */ spacing),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   typography: () => (/* binding */ typography)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Color utilities\nconst colors = {\n    primary: {\n        black: '#010101',\n        peach: '#fecf8b',\n        green: '#45523e',\n        neutral: '#eeedf3'\n    }\n};\n// Animation easing functions\nconst easings = {\n    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',\n    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'\n};\n// Breakpoint utilities\nconst breakpoints = {\n    xs: '475px',\n    sm: '640px',\n    md: '768px',\n    lg: '1024px',\n    xl: '1280px',\n    '2xl': '1536px'\n};\n// Typography scale\nconst typography = {\n    fontSizes: {\n        xs: '0.75rem',\n        sm: '0.875rem',\n        base: '1rem',\n        lg: '1.125rem',\n        xl: '1.25rem',\n        '2xl': '1.5rem',\n        '3xl': '1.875rem',\n        '4xl': '2.25rem',\n        '5xl': '3rem',\n        '6xl': '3.75rem',\n        '7xl': '4.5rem',\n        '8xl': '6rem',\n        '9xl': '8rem'\n    },\n    fontWeights: {\n        thin: '100',\n        extralight: '200',\n        light: '300',\n        normal: '400',\n        medium: '500',\n        semibold: '600',\n        bold: '700',\n        extrabold: '800',\n        black: '900'\n    },\n    lineHeights: {\n        none: '1',\n        tight: '1.25',\n        snug: '1.375',\n        normal: '1.5',\n        relaxed: '1.625',\n        loose: '2'\n    }\n};\n// Spacing scale\nconst spacing = {\n    px: '1px',\n    0: '0px',\n    0.5: '0.125rem',\n    1: '0.25rem',\n    1.5: '0.375rem',\n    2: '0.5rem',\n    2.5: '0.625rem',\n    3: '0.75rem',\n    3.5: '0.875rem',\n    4: '1rem',\n    5: '1.25rem',\n    6: '1.5rem',\n    7: '1.75rem',\n    8: '2rem',\n    9: '2.25rem',\n    10: '2.5rem',\n    11: '2.75rem',\n    12: '3rem',\n    14: '3.5rem',\n    16: '4rem',\n    18: '4.5rem',\n    20: '5rem',\n    24: '6rem',\n    28: '7rem',\n    32: '8rem',\n    36: '9rem',\n    40: '10rem',\n    44: '11rem',\n    48: '12rem',\n    52: '13rem',\n    56: '14rem',\n    60: '15rem',\n    64: '16rem',\n    72: '18rem',\n    80: '20rem',\n    88: '22rem',\n    96: '24rem'\n};\n// Animation durations\nconst durations = {\n    75: '75ms',\n    100: '100ms',\n    150: '150ms',\n    200: '200ms',\n    300: '300ms',\n    500: '500ms',\n    700: '700ms',\n    1000: '1000ms'\n};\n// Utility function to format numbers\nfunction formatNumber(num) {\n    if (num >= 1000000) {\n        return (num / 1000000).toFixed(1) + 'M';\n    }\n    if (num >= 1000) {\n        return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n}\n// Utility function to truncate text\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + '...';\n}\n// Utility function to generate random ID\nfunction generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// Utility function to debounce\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n// Utility function to throttle\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/tailwind-merge","vendor-chunks/motion-dom","vendor-chunks/@swc","vendor-chunks/motion-utils","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FApplications%2FXAMPP%2Fxamppfiles%2Fhtdocs%2Fportfolio&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();