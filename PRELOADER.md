# 🎬 Awwwards-Style Preloader

A stunning, award-worthy preloader with advanced animations and smooth transitions.

## ✨ Features

### 🎨 **Visual Elements**
- **Animated logo entrance** with 3D transforms
- **Dynamic progress bar** with gradient effects
- **Floating particles** with physics-based movement
- **Geometric shapes** with rotation animations
- **Moving background grid** for depth
- **Gradient orbs** with breathing effects
- **Smooth counter animation** with easing

### 🎭 **Animation Styles**
- **GSAP-powered animations** for performance
- **Framer Motion** for React-specific effects
- **Custom easing curves** for professional feel
- **Staggered entrances** for visual hierarchy
- **Curtain exit effect** for smooth transition

### 🎯 **Two Preloader Styles**

#### 1. **Awwwards Style** (Full Experience)
- Complex animations and effects
- Multiple visual layers
- Dynamic loading words
- Particle systems
- Perfect for portfolio showcases

#### 2. **Minimal Style** (Clean & Fast)
- Simple, elegant design
- Faster loading time
- Clean typography
- Subtle animations
- Great for professional sites

## 🛠 Configuration

Edit `src/config/preloader.ts` to customize:

```typescript
export const preloaderConfig = {
  // Choose style: 'awwwards' | 'minimal' | 'disabled'
  style: 'awwwards',
  
  // Duration in milliseconds
  duration: 3000,
  
  // Enable/disable entirely
  enabled: true,
  
  // Show only on first visit
  showOnlyOnFirstVisit: false,
  
  // Custom loading messages
  loadingWords: [
    'LOADING',
    'CREATING', 
    'CRAFTING',
    'BUILDING',
    'DESIGNING',
    'ANIMATING',
    'READY'
  ],
  
  // Visual settings
  visual: {
    showParticles: true,
    showAnimatedBackground: true,
    showGeometricShapes: true,
    particleCount: 20,
  }
}
```

## 🎮 Usage

### Basic Implementation
```tsx
import SmartPreloader from '@/components/ui/SmartPreloader'

function App() {
  const [isLoading, setIsLoading] = useState(true)

  return (
    <>
      <SmartPreloader onComplete={() => setIsLoading(false)} />
      {!isLoading && <YourMainContent />}
    </>
  )
}
```

### Direct Component Usage
```tsx
// Use Awwwards style directly
import AwwardsPreloader from '@/components/ui/AwwardsPreloader'

<AwwardsPreloader 
  onComplete={handleComplete}
  duration={3000}
/>

// Use Minimal style directly  
import MinimalPreloader from '@/components/ui/MinimalPreloader'

<MinimalPreloader 
  onComplete={handleComplete}
  duration={2500}
/>
```

## 🎨 Customization

### Colors
The preloader uses your design system colors:
- `primary-black`: Main text and elements
- `primary-peach`: Accent color and progress
- `primary-green`: Secondary accent
- `primary-neutral`: Background and subtle elements

### Fonts
- **Logo**: Clash Display (bold, large)
- **Text**: Satoshi (medium, tracking)
- **Counter**: Inter/Mono (technical feel)

### Animations
All animations use professional easing curves:
- `power3.out`: Smooth deceleration
- `back.out(1.7)`: Bouncy entrance
- `[0.76, 0, 0.24, 1]`: Custom cubic-bezier

## 📱 Responsive Design

The preloader is fully responsive:
- **Mobile**: Optimized touch interactions
- **Tablet**: Scaled appropriately
- **Desktop**: Full visual experience
- **Large screens**: Enhanced effects

## ⚡ Performance

### Optimizations
- **GPU acceleration** for smooth animations
- **Efficient particle systems** with object pooling
- **Minimal DOM manipulation** using GSAP
- **Lazy loading** of heavy effects
- **Memory cleanup** on component unmount

### Loading Strategy
1. **Instant display** of basic structure
2. **Progressive enhancement** of effects
3. **Smooth transition** to main content
4. **Memory cleanup** after completion

## 🎯 Best Practices

### When to Use
- **Portfolio websites** showcasing creativity
- **Agency websites** demonstrating capabilities  
- **Product launches** building anticipation
- **Art/design sites** setting the mood

### When NOT to Use
- **E-commerce** sites (users want speed)
- **News/blog** sites (content priority)
- **Mobile-first** apps (battery/data concerns)
- **Accessibility-critical** applications

## 🔧 Advanced Customization

### Custom Loading Words
```typescript
// In preloader config
loadingWords: [
  'INITIALIZING',
  'LOADING ASSETS', 
  'PREPARING EXPERIENCE',
  'ALMOST READY',
  'WELCOME'
]
```

### Custom Animations
```typescript
// Override GSAP timeline
const customTimeline = gsap.timeline()
customTimeline
  .to(logo, { opacity: 1, duration: 1 })
  .to(progress, { width: '100%', duration: 2 })
```

### Conditional Loading
```typescript
// Show different preloaders based on device
const isMobile = window.innerWidth < 768
const preloaderStyle = isMobile ? 'minimal' : 'awwwards'
```

## 🐛 Troubleshooting

### Common Issues

1. **Preloader not showing**
   - Check `preloaderConfig.enabled`
   - Verify component is mounted
   - Check for JavaScript errors

2. **Animations not smooth**
   - Enable hardware acceleration
   - Reduce particle count
   - Check device performance

3. **Long loading time**
   - Reduce duration in config
   - Optimize asset loading
   - Consider minimal style

### Debug Mode
```typescript
// Add to config for debugging
debug: process.env.NODE_ENV === 'development'
```

## 🎉 Examples

### Portfolio Site
```typescript
// Show full experience for portfolio
style: 'awwwards',
duration: 4000,
showOnlyOnFirstVisit: true
```

### Business Site  
```typescript
// Quick, professional loading
style: 'minimal', 
duration: 2000,
showOnlyOnFirstVisit: false
```

### Mobile App
```typescript
// Disable for mobile performance
enabled: window.innerWidth > 768,
style: 'minimal',
duration: 1500
```

## 📊 Analytics

Track preloader performance:
```typescript
// Add to onComplete callback
onComplete: () => {
  analytics.track('preloader_completed', {
    duration: Date.now() - startTime,
    style: preloaderConfig.style
  })
}
```

The preloader is now ready to create an unforgettable first impression! 🚀
