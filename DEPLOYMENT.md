# 🚀 Deployment Guide

This guide covers deploying your award-winning portfolio to various platforms.

## 📋 Pre-Deployment Checklist

### 1. Environment Setup
- [ ] Update environment variables for production
- [ ] Configure custom domain (if applicable)
- [ ] Set up SSL certificate
- [ ] Configure analytics (Google Analytics, etc.)

### 2. Performance Optimization
- [ ] Run `npm run build:production` to test build
- [ ] Optimize images and assets
- [ ] Enable compression (gzip/brotli)
- [ ] Set up CDN for static assets
- [ ] Configure caching headers

### 3. SEO & Meta Tags
- [ ] Update meta descriptions and titles
- [ ] Add Open Graph images
- [ ] Configure robots.txt
- [ ] Submit sitemap to search engines
- [ ] Set up Google Search Console

## 🌐 Deployment Platforms

### Vercel (Recommended)
Vercel is the easiest way to deploy Next.js applications.

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# For production deployment
vercel --prod
```

**Environment Variables:**
- Add your environment variables in the Vercel dashboard
- Configure custom domains in the Vercel dashboard

### Netlify
```bash
# Build command
npm run build

# Publish directory
out

# Environment variables
# Set in Netlify dashboard under Site settings > Environment variables
```

**netlify.toml:**
```toml
[build]
  command = "npm run build && npm run export"
  publish = "out"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### AWS Amplify
1. Connect your GitHub repository
2. Set build settings:
   - Build command: `npm run build`
   - Base directory: `/`
   - Output directory: `.next`

### Traditional Hosting (cPanel, etc.)
```bash
# Build and export static files
npm run build
npm run export

# Upload the 'out' directory to your hosting provider
```

## 🔧 Environment Variables

Create a `.env.production` file:

```env
# Site Configuration
NEXT_PUBLIC_SITE_URL=https://yourwebsite.com
NEXT_PUBLIC_SITE_NAME="Your Name - Creative Developer"

# Analytics
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_HOTJAR_ID=XXXXXXX

# Contact Form
CONTACT_EMAIL=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# API Configuration
API_BASE_URL=https://yourwebsite.com/api
```

## 📊 Performance Monitoring

### Core Web Vitals
Monitor your site's performance:
- **LCP (Largest Contentful Paint)**: < 2.5s
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1

### Tools
- [PageSpeed Insights](https://pagespeed.web.dev/)
- [GTmetrix](https://gtmetrix.com/)
- [WebPageTest](https://www.webpagetest.org/)

## 🔒 Security

### Headers
Configure security headers in `next.config.js`:

```javascript
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
]

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ]
  },
}
```

## 📈 Analytics Setup

### Google Analytics 4
1. Create a GA4 property
2. Add the tracking ID to your environment variables
3. The analytics are already configured in the app

### Other Analytics
- **Hotjar**: For user behavior analysis
- **Mixpanel**: For event tracking
- **Plausible**: Privacy-friendly alternative

## 🎯 SEO Optimization

### Sitemap
The app automatically generates a sitemap. Submit it to:
- Google Search Console
- Bing Webmaster Tools

### Schema Markup
Structured data is already included for:
- Person schema
- Organization schema
- WebSite schema

## 🔄 CI/CD Pipeline

### GitHub Actions
Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Build application
      run: npm run build
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v20
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Errors**
   - Check Node.js version (18+ recommended)
   - Clear `.next` directory and rebuild
   - Verify all dependencies are installed

2. **Performance Issues**
   - Enable image optimization
   - Use proper caching headers
   - Minimize JavaScript bundles

3. **SEO Issues**
   - Verify meta tags are rendering
   - Check robots.txt accessibility
   - Ensure proper URL structure

## 📞 Support

If you encounter issues:
1. Check the [Next.js documentation](https://nextjs.org/docs)
2. Review deployment platform specific guides
3. Check browser console for errors
4. Verify environment variables are set correctly

## 🎉 Post-Deployment

After successful deployment:
- [ ] Test all functionality
- [ ] Verify contact form works
- [ ] Check analytics tracking
- [ ] Test on multiple devices/browsers
- [ ] Monitor performance metrics
- [ ] Set up uptime monitoring
