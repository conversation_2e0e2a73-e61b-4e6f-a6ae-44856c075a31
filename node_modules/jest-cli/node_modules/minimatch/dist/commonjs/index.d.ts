import { AST } from './ast.js';
type Platform = 'aix' | 'android' | 'darwin' | 'freebsd' | 'haiku' | 'linux' | 'openbsd' | 'sunos' | 'win32' | 'cygwin' | 'netbsd';
export interface MinimatchOptions {
    nobrace?: boolean;
    nocomment?: boolean;
    nonegate?: boolean;
    debug?: boolean;
    noglobstar?: boolean;
    noext?: boolean;
    nonull?: boolean;
    windowsPathsNoEscape?: boolean;
    allowWindowsEscape?: boolean;
    partial?: boolean;
    dot?: boolean;
    nocase?: boolean;
    nocaseMagicOnly?: boolean;
    magicalBraces?: boolean;
    matchBase?: boolean;
    flipNegate?: boolean;
    preserveMultipleSlashes?: boolean;
    optimizationLevel?: number;
    platform?: Platform;
    windowsNoMagicRoot?: boolean;
}
export declare const minimatch: {
    (p: string, pattern: string, options?: MinimatchOptions): boolean;
    sep: Sep;
    GLOBSTAR: typeof GLOBSTAR;
    filter: (pattern: string, options?: MinimatchOptions) => (p: string) => boolean;
    defaults: (def: MinimatchOptions) => typeof minimatch;
    braceExpand: (pattern: string, options?: MinimatchOptions) => string[];
    makeRe: (pattern: string, options?: MinimatchOptions) => false | MMRegExp;
    match: (list: string[], pattern: string, options?: MinimatchOptions) => string[];
    AST: typeof AST;
    Minimatch: typeof Minimatch;
    escape: (s: string, { windowsPathsNoEscape, }?: Pick<MinimatchOptions, "windowsPathsNoEscape">) => string;
    unescape: (s: string, { windowsPathsNoEscape, }?: Pick<MinimatchOptions, "windowsPathsNoEscape">) => string;
};
type Sep = '\\' | '/';
export declare const sep: Sep;
export declare const GLOBSTAR: unique symbol;
export declare const filter: (pattern: string, options?: MinimatchOptions) => (p: string) => boolean;
export declare const defaults: (def: MinimatchOptions) => typeof minimatch;
export declare const braceExpand: (pattern: string, options?: MinimatchOptions) => string[];
export declare const makeRe: (pattern: string, options?: MinimatchOptions) => false | MMRegExp;
export declare const match: (list: string[], pattern: string, options?: MinimatchOptions) => string[];
export type MMRegExp = RegExp & {
    _src?: string;
    _glob?: string;
};
export type ParseReturnFiltered = string | MMRegExp | typeof GLOBSTAR;
export type ParseReturn = ParseReturnFiltered | false;
export declare class Minimatch {
    options: MinimatchOptions;
    set: ParseReturnFiltered[][];
    pattern: string;
    windowsPathsNoEscape: boolean;
    nonegate: boolean;
    negate: boolean;
    comment: boolean;
    empty: boolean;
    preserveMultipleSlashes: boolean;
    partial: boolean;
    globSet: string[];
    globParts: string[][];
    nocase: boolean;
    isWindows: boolean;
    platform: Platform;
    windowsNoMagicRoot: boolean;
    regexp: false | null | MMRegExp;
    constructor(pattern: string, options?: MinimatchOptions);
    hasMagic(): boolean;
    debug(..._: any[]): void;
    make(): void;
    preprocess(globParts: string[][]): string[][];
    adjascentGlobstarOptimize(globParts: string[][]): string[][];
    levelOneOptimize(globParts: string[][]): string[][];
    levelTwoFileOptimize(parts: string | string[]): string[];
    firstPhasePreProcess(globParts: string[][]): string[][];
    secondPhasePreProcess(globParts: string[][]): string[][];
    partsMatch(a: string[], b: string[], emptyGSMatch?: boolean): false | string[];
    parseNegate(): void;
    matchOne(file: string[], pattern: ParseReturn[], partial?: boolean): boolean;
    braceExpand(): string[];
    parse(pattern: string): ParseReturn;
    makeRe(): false | MMRegExp;
    slashSplit(p: string): string[];
    match(f: string, partial?: boolean): boolean;
    static defaults(def: MinimatchOptions): typeof Minimatch;
}
export { AST } from './ast.js';
export { escape } from './escape.js';
export { unescape } from './unescape.js';
//# sourceMappingURL=index.d.ts.map