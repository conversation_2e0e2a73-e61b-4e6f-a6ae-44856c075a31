{"name": "pure-rand", "version": "7.0.1", "description": " Pure random number generator written in TypeScript", "type": "commonjs", "main": "lib/pure-rand.js", "exports": {"./package.json": "./package.json", "./distribution/*": {"require": {"types": "./lib/types/distribution/*.d.ts", "default": "./lib/distribution/*.js"}, "import": {"types": "./lib/esm/types/distribution/*.d.ts", "default": "./lib/esm/distribution/*.js"}}, "./generator/*": {"require": {"types": "./lib/types/generator/*.d.ts", "default": "./lib/generator/*.js"}, "import": {"types": "./lib/esm/types/generator/*.d.ts", "default": "./lib/esm/generator/*.js"}}, "./types/*": {"require": {"types": "./lib/types/types/*.d.ts", "default": "./lib/types/*.js"}, "import": {"types": "./lib/esm/types/types/*.d.ts", "default": "./lib/esm/types/*.js"}}, ".": {"require": {"types": "./lib/types/pure-rand.d.ts", "default": "./lib/pure-rand.js"}, "import": {"types": "./lib/esm/types/pure-rand.d.ts", "default": "./lib/esm/pure-rand.js"}}}, "module": "lib/esm/pure-rand.js", "types": "lib/types/pure-rand.d.ts", "files": ["lib"], "sideEffects": false, "packageManager": "yarn@4.6.0", "scripts": {"format:check": "prettier --list-different .", "format": "prettier --write .", "build": "tsc && tsc -p ./tsconfig.declaration.json", "build:esm": "tsc --module es2015 --outDir lib/esm --moduleResolution node && tsc -p ./tsconfig.declaration.json --outDir lib/esm/types && cp package.esm-template.json lib/esm/package.json", "build:prod": "yarn build && yarn build:esm && node postbuild/main.mjs", "build:prod-ci": "cross-env EXPECT_GITHUB_SHA=true yarn build:prod", "test": "jest --config jest.config.js --coverage", "build:bench:old": "tsc --outDir lib-reference/", "build:bench:new": "tsc --outDir lib-test/", "bench": "node perf/benchmark.cjs"}, "repository": {"type": "git", "url": "git+https://github.com/dubzzz/pure-rand.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/dubzzz/pure-rand/issues"}, "homepage": "https://github.com/dubzzz/pure-rand#readme", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.13.1", "cross-env": "^7.0.3", "fast-check": "^3.23.2", "jest": "^29.7.0", "prettier": "3.4.2", "replace-in-file": "^8.3.0", "source-map-support": "^0.5.21", "tinybench": "^3.1.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.5.3"}, "keywords": ["seed", "random", "prng", "generator", "pure", "rand", "mersenne", "random number generator", "fastest", "fast"], "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}]}