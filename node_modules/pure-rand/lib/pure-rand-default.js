"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.unsafeUniformIntDistribution = exports.unsafeUniformBigIntDistribution = exports.unsafeUniformArrayIntDistribution = exports.uniformIntDistribution = exports.uniformBigIntDistribution = exports.uniformArrayIntDistribution = exports.xoroshiro128plus = exports.xorshift128plus = exports.mersenne = exports.congruential32 = exports.unsafeSkipN = exports.unsafeGenerateN = exports.skipN = exports.generateN = exports.__commitHash = exports.__version = exports.__type = void 0;
var LinearCongruential_1 = require("./generator/LinearCongruential");
Object.defineProperty(exports, "congruential32", { enumerable: true, get: function () { return LinearCongruential_1.congruential32; } });
var MersenneTwister_1 = require("./generator/MersenneTwister");
Object.defineProperty(exports, "mersenne", { enumerable: true, get: function () { return MersenneTwister_1.mersenne; } });
var XorShift_1 = require("./generator/XorShift");
Object.defineProperty(exports, "xorshift128plus", { enumerable: true, get: function () { return XorShift_1.xorshift128plus; } });
var XoroShiro_1 = require("./generator/XoroShiro");
Object.defineProperty(exports, "xoroshiro128plus", { enumerable: true, get: function () { return XoroShiro_1.xoroshiro128plus; } });
var UniformArrayIntDistribution_1 = require("./distribution/UniformArrayIntDistribution");
Object.defineProperty(exports, "uniformArrayIntDistribution", { enumerable: true, get: function () { return UniformArrayIntDistribution_1.uniformArrayIntDistribution; } });
var UniformBigIntDistribution_1 = require("./distribution/UniformBigIntDistribution");
Object.defineProperty(exports, "uniformBigIntDistribution", { enumerable: true, get: function () { return UniformBigIntDistribution_1.uniformBigIntDistribution; } });
var UniformIntDistribution_1 = require("./distribution/UniformIntDistribution");
Object.defineProperty(exports, "uniformIntDistribution", { enumerable: true, get: function () { return UniformIntDistribution_1.uniformIntDistribution; } });
var UnsafeUniformArrayIntDistribution_1 = require("./distribution/UnsafeUniformArrayIntDistribution");
Object.defineProperty(exports, "unsafeUniformArrayIntDistribution", { enumerable: true, get: function () { return UnsafeUniformArrayIntDistribution_1.unsafeUniformArrayIntDistribution; } });
var UnsafeUniformBigIntDistribution_1 = require("./distribution/UnsafeUniformBigIntDistribution");
Object.defineProperty(exports, "unsafeUniformBigIntDistribution", { enumerable: true, get: function () { return UnsafeUniformBigIntDistribution_1.unsafeUniformBigIntDistribution; } });
var UnsafeUniformIntDistribution_1 = require("./distribution/UnsafeUniformIntDistribution");
Object.defineProperty(exports, "unsafeUniformIntDistribution", { enumerable: true, get: function () { return UnsafeUniformIntDistribution_1.unsafeUniformIntDistribution; } });
var SkipN_1 = require("./distribution/SkipN");
Object.defineProperty(exports, "skipN", { enumerable: true, get: function () { return SkipN_1.skipN; } });
var GenerateN_1 = require("./distribution/GenerateN");
Object.defineProperty(exports, "generateN", { enumerable: true, get: function () { return GenerateN_1.generateN; } });
var UnsafeGenerateN_1 = require("./distribution/UnsafeGenerateN");
Object.defineProperty(exports, "unsafeGenerateN", { enumerable: true, get: function () { return UnsafeGenerateN_1.unsafeGenerateN; } });
var UnsafeSkipN_1 = require("./distribution/UnsafeSkipN");
Object.defineProperty(exports, "unsafeSkipN", { enumerable: true, get: function () { return UnsafeSkipN_1.unsafeSkipN; } });
var __type = 'commonjs';
exports.__type = __type;
var __version = '7.0.1';
exports.__version = __version;
var __commitHash = '2248506b66d969d1a8b477a4dde8e24cbac33e6a';
exports.__commitHash = __commitHash;
