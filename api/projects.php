<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Sample project data
$projects = [
    [
        'id' => 1,
        'title' => 'E-commerce Platform',
        'description' => 'A modern e-commerce platform built with Next.js and Stripe integration.',
        'image' => '/images/project1.jpg',
        'technologies' => ['Next.js', 'React', 'Stripe', 'Tailwind CSS'],
        'category' => 'Web Development',
        'year' => 2024,
        'url' => 'https://example.com',
        'github' => 'https://github.com/username/project1'
    ],
    [
        'id' => 2,
        'title' => 'Portfolio Website',
        'description' => 'An award-winning portfolio website with stunning animations and interactions.',
        'image' => '/images/project2.jpg',
        'technologies' => ['React', 'GSAP', 'Framer Motion', 'Three.js'],
        'category' => 'Creative',
        'year' => 2024,
        'url' => 'https://example.com',
        'github' => 'https://github.com/username/project2'
    ],
    [
        'id' => 3,
        'title' => 'SaaS Dashboard',
        'description' => 'A comprehensive dashboard for SaaS applications with real-time data visualization.',
        'image' => '/images/project3.jpg',
        'technologies' => ['Vue.js', 'Node.js', 'MongoDB', 'Chart.js'],
        'category' => 'Web Application',
        'year' => 2023,
        'url' => 'https://example.com',
        'github' => 'https://github.com/username/project3'
    ],
    [
        'id' => 4,
        'title' => 'Mobile App UI',
        'description' => 'Beautiful mobile app interface design with smooth animations.',
        'image' => '/images/project4.jpg',
        'technologies' => ['React Native', 'Expo', 'TypeScript'],
        'category' => 'Mobile',
        'year' => 2023,
        'url' => 'https://example.com',
        'github' => 'https://github.com/username/project4'
    ],
    [
        'id' => 5,
        'title' => 'Brand Identity',
        'description' => 'Complete brand identity design including logo, colors, and typography.',
        'image' => '/images/project5.jpg',
        'technologies' => ['Adobe Illustrator', 'Figma', 'Photoshop'],
        'category' => 'Design',
        'year' => 2023,
        'url' => 'https://example.com',
        'github' => null
    ],
    [
        'id' => 6,
        'title' => 'AI Chat Interface',
        'description' => 'Modern chat interface for AI-powered customer support system.',
        'image' => '/images/project6.jpg',
        'technologies' => ['React', 'Socket.io', 'OpenAI API', 'Node.js'],
        'category' => 'AI/ML',
        'year' => 2024,
        'url' => 'https://example.com',
        'github' => 'https://github.com/username/project6'
    ]
];

// Handle different request methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Get all projects or filter by category
        $category = isset($_GET['category']) ? $_GET['category'] : null;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : null;
        
        $filteredProjects = $projects;
        
        if ($category) {
            $filteredProjects = array_filter($projects, function($project) use ($category) {
                return strtolower($project['category']) === strtolower($category);
            });
        }
        
        if ($limit) {
            $filteredProjects = array_slice($filteredProjects, 0, $limit);
        }
        
        echo json_encode([
            'success' => true,
            'data' => array_values($filteredProjects),
            'total' => count($filteredProjects)
        ]);
        break;
        
    case 'POST':
        // Add new project (for admin use)
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Invalid JSON data'
            ]);
            exit();
        }
        
        // Validate required fields
        $required = ['title', 'description', 'technologies', 'category'];
        foreach ($required as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => "Missing required field: $field"
                ]);
                exit();
            }
        }
        
        // In a real application, you would save to database
        $newProject = [
            'id' => count($projects) + 1,
            'title' => $input['title'],
            'description' => $input['description'],
            'image' => $input['image'] ?? '/images/default.jpg',
            'technologies' => $input['technologies'],
            'category' => $input['category'],
            'year' => $input['year'] ?? date('Y'),
            'url' => $input['url'] ?? null,
            'github' => $input['github'] ?? null
        ];
        
        echo json_encode([
            'success' => true,
            'message' => 'Project added successfully',
            'data' => $newProject
        ]);
        break;
        
    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
        break;
}
?>
