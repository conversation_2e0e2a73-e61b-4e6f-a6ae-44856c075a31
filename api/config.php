<?php
// API Configuration
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database configuration (if using a database)
define('DB_HOST', 'localhost');
define('DB_NAME', 'portfolio');
define('DB_USER', 'root');
define('DB_PASS', '');

// API Response helper function
function sendResponse($success, $data = null, $message = '', $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode([
        'success' => $success,
        'data' => $data,
        'message' => $message,
        'timestamp' => date('c')
    ]);
    exit();
}

// Error handler
function handleError($message, $statusCode = 500) {
    sendResponse(false, null, $message, $statusCode);
}

// Validate required fields
function validateRequired($data, $required) {
    foreach ($required as $field) {
        if (!isset($data[$field]) || empty($data[$field])) {
            handleError("Missing required field: $field", 400);
        }
    }
}

// Sanitize input
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// Rate limiting (simple implementation)
function checkRateLimit($identifier, $maxRequests = 100, $timeWindow = 3600) {
    $file = sys_get_temp_dir() . '/rate_limit_' . md5($identifier);
    $now = time();
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        if ($data['reset_time'] > $now) {
            if ($data['requests'] >= $maxRequests) {
                handleError('Rate limit exceeded', 429);
            }
            $data['requests']++;
        } else {
            $data = ['requests' => 1, 'reset_time' => $now + $timeWindow];
        }
    } else {
        $data = ['requests' => 1, 'reset_time' => $now + $timeWindow];
    }
    
    file_put_contents($file, json_encode($data));
}
?>
