<?php
require_once 'config.php';

// Rate limiting
$clientIP = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
checkRateLimit($clientIP, 10, 3600); // 10 requests per hour

switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        handleContactForm();
        break;
    default:
        handleError('Method not allowed', 405);
}

function handleContactForm() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        handleError('Invalid JSON data', 400);
    }
    
    // Validate required fields
    validateRequired($input, ['name', 'email', 'message']);
    
    // Sanitize input
    $data = sanitizeInput($input);
    
    // Validate email
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        handleError('Invalid email address', 400);
    }
    
    // Validate message length
    if (strlen($data['message']) < 10) {
        handleError('Message must be at least 10 characters long', 400);
    }
    
    if (strlen($data['message']) > 1000) {
        handleError('Message must be less than 1000 characters', 400);
    }
    
    // Process the contact form
    $result = processContactForm($data);
    
    if ($result) {
        sendResponse(true, null, 'Message sent successfully');
    } else {
        handleError('Failed to send message', 500);
    }
}

function processContactForm($data) {
    // In a real application, you would:
    // 1. Save to database
    // 2. Send email notification
    // 3. Send auto-reply to user
    
    // For demo purposes, we'll just log the data
    $logEntry = [
        'timestamp' => date('c'),
        'name' => $data['name'],
        'email' => $data['email'],
        'message' => $data['message'],
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    // Log to file
    $logFile = __DIR__ . '/logs/contact_' . date('Y-m-d') . '.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);
    
    // Simulate email sending
    return sendEmail($data);
}

function sendEmail($data) {
    // In a real application, you would use a proper email service
    // like SendGrid, Mailgun, or PHP's mail() function
    
    $to = '<EMAIL>';
    $subject = 'New Contact Form Submission from ' . $data['name'];
    $message = "
    New contact form submission:
    
    Name: {$data['name']}
    Email: {$data['email']}
    Message: {$data['message']}
    
    Timestamp: " . date('Y-m-d H:i:s') . "
    IP Address: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "
    ";
    
    $headers = [
        'From: <EMAIL>',
        'Reply-To: ' . $data['email'],
        'Content-Type: text/plain; charset=UTF-8'
    ];
    
    // For demo purposes, we'll just return true
    // In production, uncomment the line below:
    // return mail($to, $subject, $message, implode("\r\n", $headers));
    
    return true;
}
?>
