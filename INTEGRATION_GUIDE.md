# 🔗 Portfolio Integration Guide

## ✅ **INTEGRATION COMPLETE!**

Your **Awwwards-style Next.js portfolio** is now fully connected to the **Laravel 11 Admin Panel**! 

---

## 🚀 **How to Run Both Systems**

### 1. **Start Laravel Admin Panel**
```bash
cd portfolio-admin
php artisan serve
```
- **Admin Panel**: http://localhost:8000/admin
- **API**: http://localhost:8000/api/v1/*

### 2. **Start Next.js Portfolio**
```bash
cd portfolio  # (root directory)
npm run dev
```
- **Portfolio Website**: http://localhost:3001

---

## 🎯 **What's Connected**

### ✅ **Hero Section**
- **Dynamic Content**: Title lines, subtitle from Laravel admin
- **CTA Button**: Configurable text and link
- **Background**: Support for images/videos (ready for upload)
- **Animations**: Configurable animation settings

### ✅ **Projects Section**
- **Real-time Data**: Projects from Laravel admin panel
- **Featured Projects**: Automatic filtering by project type
- **Technology Stack**: Dynamic tech badges with colors
- **Project Images**: Automatic asset URL handling
- **Project Details**: Full project information display

### ✅ **API Integration**
- **Hero Data**: `fetchHeroSection()`
- **Projects**: `fetchProjects()`, `fetchFeaturedProjects()`
- **Contact**: `fetchContactInfo()`, `submitContactForm()`
- **Skills**: `fetchSkills()`, `fetchSkillCategories()`
- **Settings**: `fetchSiteSettings()`

---

## 🔧 **Admin Panel Features**

### **Content Management**
1. **Hero Section** - Update homepage hero content
2. **Projects** - Add/edit portfolio projects with media
3. **About & Timeline** - Manage about content and career timeline
4. **Skills** - Organize skills by categories
5. **Contact** - Update contact info and view form submissions
6. **Site Settings** - Configure SEO, theme, analytics

### **Admin Access**
- **URL**: http://localhost:8000/admin
- **Email**: <EMAIL>
- **Password**: password123

---

## 🌐 **API Endpoints**

### **Public Endpoints** (Used by Next.js)
```
GET  /api/v1/hero                 - Hero section data
GET  /api/v1/projects             - All projects
GET  /api/v1/projects/featured    - Featured projects only
GET  /api/v1/projects/{slug}      - Single project
GET  /api/v1/about                - About section
GET  /api/v1/timeline             - Career timeline
GET  /api/v1/skills               - Skills by categories
GET  /api/v1/contact              - Contact information
GET  /api/v1/social-links         - Social media links
POST /api/v1/contact              - Submit contact form
GET  /api/v1/settings             - Site settings
```

### **Documentation**
- **API Docs**: http://localhost:8000/api/docs
- **API Status**: http://localhost:8000/api/status

---

## 🎨 **How to Update Content**

### **1. Update Hero Section**
1. Go to Admin Panel → Hero Sections
2. Edit the active hero section
3. Changes appear instantly on the website

### **2. Add New Project**
1. Go to Admin Panel → Projects
2. Click "Create Project"
3. Add title, description, images, technologies
4. Set as "Featured" for homepage display
5. Publish to make it visible

### **3. Manage Contact Form**
1. Form submissions appear in Admin Panel → Contact → Contact Submissions
2. Update contact info in Admin Panel → Contact → Contact Info
3. Manage social links in Admin Panel → Contact → Social Links

---

## 🔒 **Security Features**

### **Rate Limiting**
- API: 100 requests/minute
- Contact Form: 3 submissions/hour
- Authentication: 5 attempts/15 minutes

### **File Security**
- Secure file uploads with validation
- XSS protection on all inputs
- CSRF protection for forms

---

## 🎯 **Next Steps**

### **Content Updates**
1. **Add Your Projects** - Upload real project data
2. **Update Hero Content** - Customize homepage messaging
3. **Configure Settings** - Set SEO, analytics, theme colors
4. **Add About Content** - Write your story and timeline

### **Customization**
1. **Theme Colors** - Update in Site Settings
2. **SEO Settings** - Configure meta tags and descriptions
3. **Analytics** - Add Google Analytics/GTM IDs
4. **Social Links** - Add your social media profiles

### **Production Deployment**
1. **Environment Variables** - Update API URLs for production
2. **Database** - Switch to MySQL/PostgreSQL for production
3. **File Storage** - Configure cloud storage (AWS S3, etc.)
4. **SSL Certificates** - Enable HTTPS for both systems

---

## 🎉 **Success!**

Your portfolio is now a **professional CMS-powered website** with:
- ✅ **Beautiful Awwwards-style frontend**
- ✅ **Powerful Laravel admin panel**
- ✅ **Real-time content updates**
- ✅ **Enterprise-level security**
- ✅ **Production-ready architecture**

**Happy coding! 🚀**
